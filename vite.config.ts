/// <reference types='vitest' />
import { fileURLToPath, URL } from 'url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin'

import dotenv from 'dotenv'

dotenv.config()

export default defineConfig({
  root: __dirname,
  cacheDir: './node_modules/.vite/.',
  base: '/',
  server: {
    port: 4200,
    host: 'localhost'
  },

  preview: {
    port: 4300,
    host: 'localhost'
  },

  plugins: [vue(), nxViteTsPaths()],

  build: {
    outDir: './dist',
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true
    }
  },

  resolve: {
    alias: {
      '@acquisition-accredited-network': fileURLToPath(
        new URL('./modules/acquisition/accredited-network/src', import.meta.url)
      ),
      '@acquisition-products': fileURLToPath(
        new URL('./modules/acquisition/products/src', import.meta.url)
      ),
      '@acquisition-vic-product-option': fileURLToPath(
        new URL('./modules/acquisition/vic-product-option/src', import.meta.url)
      ),
      '@feature-flags': fileURLToPath(new URL('./modules/feature-flags/src', import.meta.url)),
      '@business-sales-agent': fileURLToPath(
        new URL('./modules/business/sales-agent/src', import.meta.url)
      ),
      '@business-sales-firm': fileURLToPath(
        new URL('./modules/business/sales-firm/src', import.meta.url)
      ),
      '@business-sales-firm-staff': fileURLToPath(
        new URL('./modules/business/sales-firm-staff/src', import.meta.url)
      ),
      '@company-staff': fileURLToPath(new URL('./modules/company/staff/src', import.meta.url)),
      '@sherlock': fileURLToPath(new URL('./modules/sherlock/src', import.meta.url)),
      '@http': fileURLToPath(new URL('./modules/core/http/src', import.meta.url)),
      '@commons': fileURLToPath(new URL('./modules/core/commons/src', import.meta.url)),
      '@procedures': fileURLToPath(new URL('./modules/procedures/src', import.meta.url)),
      '@code-pricing': fileURLToPath(
        new URL('./modules/provider-lifecycle/code-pricing/src', import.meta.url)
      ),
      '@specialist-earnings': fileURLToPath(
        new URL('./modules/provider-lifecycle/specialist-earnings/src', import.meta.url)
      ),
      '@healthcare-team-address': fileURLToPath(
        new URL('./modules/healthcare-team/address/src', import.meta.url)
      ),
      '@healthcare-team-registration': fileURLToPath(
        new URL('./modules/healthcare-team/registration/src', import.meta.url)
      ),
      '@specialties': fileURLToPath(new URL('./modules/specialties/src', import.meta.url)),
      '@health-professionals': fileURLToPath(
        new URL('./modules/health-professionals/src', import.meta.url)
      ),
      '@staff': fileURLToPath(new URL('./modules/staff/src', import.meta.url)),
      '@healthcare-team-association': fileURLToPath(
        new URL('./modules/healthcare-team/association/src', import.meta.url)
      )
    }
  },

  test: {
    globals: true,
    cacheDir: './node_modules/.vitest',
    environment: 'jsdom',
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'modules/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache', '.nx', 'e2e', '**/index.ts'],
    reporters: ['default'],
    setupFiles: ['./vitest.setup.ts'],
    testTimeout: 10000,
    css: false,
    useAtomics: true,
    maxConcurrency: 6,
    minThreads: 8,
    slowTestThreshold: 500,
    coverage: {
      include: [
        'src/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx,vue}',
        'modules/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx,vue}'
      ],
      exclude: [
        'node_modules',
        'dist',
        '.idea',
        '.git',
        '.cache',
        '.nx',
        'e2e',
        'modules/**/*/models/*',
        'modules/**/*/mocks/*',
        'app/**/*/mocks/*',
        'modules/**/*/constants/*',
        'modules/**/*/tests/*',
        'modules/**/*/schemas/*',
        'modules/**/*/*.config.ts',
        'modules/**/*/*.d.ts',
        'modules/**/*/index.ts',
        'modules/**/*/types.ts'
      ],
      reportsDirectory: './coverage/app-backoffice',
      provider: 'v8',
      reporter: ['lcov']
    }
  }
})
