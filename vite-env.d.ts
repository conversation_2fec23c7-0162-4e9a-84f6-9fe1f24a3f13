/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly NODE_ENV: string
  readonly VITE_ENV: string
  readonly VITE_FIREBASE_API_KEY: string
  readonly VITE_FIREBASE_AUTH_DOMAIN: string
  readonly VITE_FIREBASE_DATABASE_URL: string
  readonly VITE_FIREBASE_PROJECT_ID: string
  readonly VITE_FIREBASE_STORAGE_BUCKET: string
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string
  readonly VITE_FIREBASE_APP_ID: string
  readonly VITE_FIREBASE_MEASUREMENT_ID: string
  readonly VITE_SEGMENT_ANALYTICS_WRITE_KEY: string
  readonly VITE_BACKOFFICE_BFF_PREFIX_URL: string
  readonly VITE_BACKOFFICE_URL: string
  readonly VITE_ENABLE_VUE_QUERY_DEVTOOLS: string
  // more env variables...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
