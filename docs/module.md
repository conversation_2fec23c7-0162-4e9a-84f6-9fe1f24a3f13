# Best pratives to create a module

## summary
1. [Create a folder with the module name](#1-create-a-folder-with-the-module-name)
2. [Make sure that your module has a owner in .github/CODEOWNERS](#2-make-sure-that-your-module-has-a-owner-in-githubcodeowners)
3. [Following the stack of the project](#3-following-the-stack-of-the-project)
4. [Integration with the BFF](#4-integration-with-the-bff)

## 1. Create a folder with the module name
When you are creating a new module, you should follow the rules below:

1. Don't use the squad name
2. Don't Use the Alliance name

Because this names probably will change with some frequency

Instead, follow the rules below:
- Name of the module should have the same name of the context
- If you context have more then one module, group them by a new folder with the context and module name

## 2. Make sure that your module has a owner in .github/CODEOWNERS
This is important to make sure that the right person will review your code

If your team doesn't have permissions to see this repository, open a Pull request on the terraform, adding your team, you can follow this [example](https://github.com/alice-health/terraform/pull/2255) of how do that.

## 3. Following the stack of the project
If you are creating a new module, you should follow the stack of the project.

- Use typescript in your files
- Every View or Component should have a unit test
- We use the MSW to mock the API, so you should use it too, every module should work without the API just with the MSW, this mocks are used in the test environment too.
- Reuse our core modules to build your module
  - Every request to the API is made by our HTTP module and the Tanstack Query.
  - We have [templates](./modules/core/commons/src/layout) to attend simple pages like lists and forms.
- Use the Design System to build your views, you can see the documentation [here](https://github.com/alice-health/frontend-tools/tree/main/packages/design-system)
- Use our [custom hooks](https://github.com/alice-health/frontend-tools/tree/main/packages/vue-hooks) to handle common behaviors like form validation.
  - To validate forms we have a [hook](https://github.com/alice-health/frontend-tools/blob/main/packages/vue-hooks/src/lib/useValidation.ts) integrated with the Zod library
  - To handle with the pagination we have this [hook](https://github.com/alice-health/frontend-tools/blob/main/packages/vue-hooks/src/lib/usePagination.ts)
  - You can see other examples directly on the vue-hooks repository, our in the modules of this project.
- To unit test environment you can build the application render with our `buildCustomRender` present in the lib `@alice-health/unit-presets/src/testing-library`

## 4. Integration with the BFF
Every module should call just the BFF that was configured in the HTTP module. If you need to see what the BFF will return on the endpoints you can see the [swagger](https://backoffice-bff-api.wonderland.engineering/swagger).

- You shouldn't need call multiple requests to build a view
- Endpoint with pagination will return the data and the pagination information
- You souldn't need the change the properties name of the data that you receive or send to the BFF. Every map of this properties to adapt the data to what the backend services need is done by the BFF.

If some of this things is much different of what is written here, you should open discussion with the team to understand why the BFF is not following the rules.
