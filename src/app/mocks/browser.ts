import { setupWorker } from 'msw/browser'
import { featureFlagsHandlers } from '@alice-backoffice/feature-flags'
import { sherlockHandlers } from '@alice-backoffice/sherlock'
import { salesAgentHandlers } from '@alice-backoffice/sales-agent'
import { salesFirmHandlers } from '@alice-backoffice/sales-firm'
import { accreditedNetworkHandlers } from '@alice-backoffice/acquisition-accredited-network'
import { salesFirmStaffHandlers } from '@alice-backoffice/sales-firm-staff'
import { vicProductOptionHandlers } from '@alice-backoffice/acquisition-vic-product-option'
import { specialtiesHandlers } from '@alice-backoffice/specialties'
import { healthProfessionalsHandlers } from '@alice-backoffice/health-professionals'
import { teamAssociationHandlers } from '@alice-backoffice/healthcare-team-association'
import { proceduresHandlers } from '@alice-backoffice/procedures'
import { staffHandlers } from '@alice-backoffice/staff'

const handlers = [
  ...featureFlagsHandlers,
  ...sherlockHandlers,
  ...salesAgentHandlers,
  ...salesFirmHandlers,
  ...salesFirmStaffHandlers,
  ...accreditedNetworkHandlers,
  ...vicProductOptionHandlers,
  ...proceduresHandlers,
  ...specialtiesHandlers,
  ...healthProfessionalsHandlers,
  ...teamAssociationHandlers,
  ...staffHandlers
]

export const worker = setupWorker(...handlers)
