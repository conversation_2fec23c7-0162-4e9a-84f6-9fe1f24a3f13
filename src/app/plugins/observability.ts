import pkg from '../../../package.json'

import ObservabilityPlugin from '@alice-health/observability'

const options = {
  env: import.meta.env.VITE_ENV as string,
  service: import.meta.env.VITE_DATADOG_SERVICE as string,
  clientToken: import.meta.env.VITE_DATADOG_CLIENT_TOKEN as string,
  applicationId: import.meta.env.VITE_DATADOG_APPLICATION_ID as string,
  customOptions: {
    version: pkg.version,
    defaultPrivacyLevel: 'mask-user-input'
  }
}

export const plugin = ObservabilityPlugin(options)

plugin?.startSessionReplayRecording()
plugin?.initLogs()
