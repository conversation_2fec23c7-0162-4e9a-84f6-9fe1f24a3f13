<script setup lang="ts">
import { onMounted, provide, reactive, ref } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import {
  WLink,
  WSidebar,
  WList,
  WListItem,
  WSnackbar,
  WAccordion
} from '@alice-health/wonderland-vue'
import { VueQueryDevtools } from '@tanstack/vue-query-devtools'

import { isAuthenticated, logout, getUser, listenTokenChanged } from '@alice-backoffice/auth'

/*
 * Refs & Reactives
 */

const snackbar = ref()
const showSidebar = ref(false)
const sidebarOpenState = ref(true)

const user = reactive({
  name: 'Username',
  image: 'https://via.placeholder.com/150'
})

/*
 * Injections & Providers
 */

provide('snackbar', snackbar)

/*
 * Constants
 */

const enableVueQueryDevtools = import.meta.env.VITE_ENABLE_VUE_QUERY_DEVTOOLS === 'true'

const APP_NAME = 'Backoffice'
const logo = 'https://assets.alice.com.br/contract/logo.png'

/*
 * Hooks
 */

const router = useRouter()

/*
 * Methods
 */

function checkAuth() {
  showSidebar.value = isAuthenticated()

  if (showSidebar.value) {
    user.name = getUser()?.displayName
    user.image = getUser()?.photoURL
  }
}

function onLogout() {
  logout().then(async () => {
    await router.push('/login')
    user.name = ''
    user.image = ''
  })
}

function handleToggleSidebar(event: CustomEvent<boolean>) {
  sidebarOpenState.value = event.detail
}

/*
 * Lifecycle Hooks
 */

onMounted(async () => {
  await router.isReady()
  checkAuth()
  listenTokenChanged()
})

/*
 * Router Hooks
 */

router.afterEach(() => {
  checkAuth()
})
</script>

<template>
  <div v-if="showSidebar">
    <WSidebar
      :label="APP_NAME"
      :user="user"
      :logo="logo"
      @w-logout="onLogout"
      @w-toggle-show="handleToggleSidebar"
    >
      <WList slot="content">
        <WAccordion label="Engenharia">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'feature-flags-list' }">
                <WLink size="small" variant="secondary" quiet :href="href">Feature flags</WLink>
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>

        <WAccordion label="Empresa">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'company/staff' }">
                <WLink size="small" variant="secondary" quiet :href="href">Company Staff</WLink>
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>

        <WAccordion label="Business">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'business/sales-agent' }">
                <WLink size="small" variant="secondary" quiet :href="href">Corretor</WLink>
              </router-link>
            </WListItem>
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'business/sales-firm' }">
                <WLink size="small" variant="secondary" quiet :href="href">Corretora</WLink>
              </router-link>
            </WListItem>
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'business/sales-firm-staff' }">
                <WLink size="small" variant="secondary" quiet :href="href"
                  >Funcionário da Corretora</WLink
                >
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>

        <WAccordion label="Aquisição">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'acquisition/vic-product-option' }">
                <WLink size="small" variant="secondary" quiet :href="href"
                  >Opções de Produtos</WLink
                >
              </router-link>
            </WListItem>
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'acquisition/accredited-network' }">
                <WLink size="small" variant="secondary" quiet :href="href"
                  >Agrupamento de Rede Credenciada</WLink
                >
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>

        <WAccordion label="Staff">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'staff/list' }">
                <WLink size="small" variant="secondary" quiet :href="href">Pessoas</WLink>
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>

        <WAccordion label="Time de Saúde">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'healthcare-team/list' }">
                <WLink size="small" variant="secondary" quiet :href="href">Times de Saúde</WLink>
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>

        <WAccordion label="Especialidades">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'specialty-list' }">
                <WLink size="small" variant="secondary" quiet :href="href"
                  >Lista de especialidades</WLink
                >
              </router-link>
            </WListItem>
            <WListItem text-align="left">
              <router-link
                v-slot="{ href }"
                :to="{ name: 'suggested-procedures-specialties-list' }"
              >
                <WLink size="small" variant="secondary" quiet :href="href"
                  >Procedimentos sugeridos</WLink
                >
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>

        <WAccordion label="Especialistas">
          <WList slot="content">
            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'procedures/resources/list' }">
                <WLink variant="secondary" quiet :href="href">Cadastro de códigos</WLink>
              </router-link>
            </WListItem>

            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'list-pricings' }">
                <WLink variant="secondary" quiet :href="href">Precificação de códigos</WLink>
              </router-link>
            </WListItem>

            <WListItem text-align="left">
              <router-link v-slot="{ href }" :to="{ name: 'specialist-earnings-invoices' }">
                <WLink variant="secondary" quiet :href="href">Resumos de ganhos</WLink>
              </router-link>
            </WListItem>
          </WList>
        </WAccordion>
      </WList>
    </WSidebar>
  </div>

  <WSnackbar ref="snackbar" />

  <div
    :class="{
      'router-container': true,
      'sidebar-open': sidebarOpenState
    }"
  >
    <RouterView v-slot="{ Component }">
      <component :is="Component" />
    </RouterView>
  </div>

  <VueQueryDevtools :initial-is-open="false" v-if="enableVueQueryDevtools" />
</template>

<style scoped lang="scss"></style>
