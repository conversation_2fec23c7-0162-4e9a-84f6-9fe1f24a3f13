import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import PageNotFound from '../views/PageNotFound.vue'

import { healthProfessionalsRoutes } from '@alice-backoffice/health-professionals'
import { featureFlagsRoutes } from '@alice-backoffice/feature-flags'
import { proceduresRoutes } from '@alice-backoffice/procedures'
import { codePricingRoutes } from '@alice-backoffice/code-pricing'
import { specialistEarningsRoutes } from '@alice-backoffice/specialist-earnings'
import { sherlockRoutes } from '@alice-backoffice/sherlock'
import { specialtiesRoutes } from '@alice-backoffice/specialties'
import { staffRoutes } from '@alice-backoffice/staff'
import { healthcareTeamRoutes } from '@alice-backoffice/healthcare-team-registration'
import { companyStaffRoutes } from '@alice-backoffice/company-staff'
import { salesAgentRoutes } from '@alice-backoffice/sales-agent'
import { salesFirmRoutes } from '@alice-backoffice/sales-firm'
import { salesFirmStaffRoutes } from '@alice-backoffice/sales-firm-staff'
import { accreditedNetworkRoutes } from '@alice-backoffice/acquisition-accredited-network'
import { vicProductOptionRoutes } from '@alice-backoffice/acquisition-vic-product-option'
import { productsRoutes } from '@alice-backoffice/acquisition-products'
import { teamAssociationRoutes } from '@alice-backoffice/healthcare-team-association'

import { authGuard } from '@alice-backoffice/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: async () => {
      const views = await import('@alice-backoffice/auth')
      return views.LoginView
    }
  },
  {
    path: '/',
    name: 'Home',
    redirect: '/feature-flags'
  },
  ...healthProfessionalsRoutes(authGuard),
  ...featureFlagsRoutes(authGuard),
  ...proceduresRoutes(authGuard),
  ...accreditedNetworkRoutes(authGuard),
  ...productsRoutes(authGuard),
  ...vicProductOptionRoutes(authGuard),
  ...healthcareTeamRoutes(authGuard),
  ...salesFirmStaffRoutes(authGuard),
  ...salesFirmRoutes(authGuard),
  ...salesAgentRoutes(authGuard),
  ...staffRoutes(authGuard),
  ...companyStaffRoutes(authGuard),
  ...sherlockRoutes(authGuard),
  ...specialtiesRoutes(authGuard),
  ...teamAssociationRoutes(authGuard),
  ...codePricingRoutes(authGuard),
  ...specialistEarningsRoutes(authGuard),
  {
    path: '/404',
    name: 'Not found',
    component: PageNotFound
  },
  {
    path: '/:pathMatch(.*)',
    redirect: '/404'
  }
]

export const router = createRouter({
  history: createWebHistory(),
  routes
})
