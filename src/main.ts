import './styles.scss'
import './app/plugins/observability'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import { router } from './app/router'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { ComponentLibrary } from '@alice-health/wonderland-vue'

import App from './app/App.vue'
import Validation from './app/plugins/validation'

const app = createApp(App)
const pinia = createPinia()

const cacheOptions = {
  queryClientConfig: {
    defaultOptions: {
      queries: {
        /** 1 hour */
        gcTime: 60 * 60 * 1000,
        /** 10 minutes */
        staleTime: 10 * 60 * 1000,
        refetchOnWindowFocus: false
      }
    }
  }
}

app.use(pinia)
app.use(router)
app.use(Validation)
app.use(ComponentLibrary)
app.use(VueQueryPlugin, cacheOptions)

async function mockingServerIfEnabled() {
  const enable = Boolean(import.meta.env.VITE_ENABLE_MOCK)

  if (!enable) return

  const { worker } = await import('./app/mocks/browser')

  return worker.start({
    onUnhandledRequest(req, print) {
      const excludedRoutes = [
        'datadoghq',
        'node_modules',
        'googleapis',
        'googleusercontent',
        'assets'
      ]
      const isExcluded = excludedRoutes.some((route) => req?.url?.includes(route))

      if (isExcluded) return

      print.warning()
    }
  })
}

;(async () => {
  await mockingServerIfEnabled()
  app.mount('#root')
})()
