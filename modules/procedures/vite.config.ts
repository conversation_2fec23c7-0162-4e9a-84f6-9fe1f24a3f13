/// <reference types='vitest' />
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin'
import { fileURLToPath } from 'url'
import path from 'path'

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/modules/procedures',

  plugins: [
    vue(),
    nxViteTsPaths(),
    dts({
      entryRoot: 'src',
      tsConfigFilePath: path.join(__dirname, 'tsconfig.lib.json'),
      skipDiagnostics: true
    })
  ],

  resolve: {
    alias: {
      '@procedures': fileURLToPath(new URL('./src', import.meta.url)),
      '@http': fileURLToPath(new URL('../core/http/src', import.meta.url)),
      '@commons': fileURLToPath(new URL('../core/commons/src', import.meta.url))
    }
  },

  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [nxViteTsPaths()]
  // },

  // Configuration for building your library.
  // See: https://vitejs.dev/guide/build.html#library-mode
  build: {
    outDir: '../../dist/modules/procedures',
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true
    },
    lib: {
      // Could also be a dictionary or array of multiple entry points.
      entry: 'src/index.ts',
      name: 'procedures',
      fileName: 'index',
      // Change this to the formats you want to support.
      // Don't forget to update your package.json as well.
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      // External packages that should not be bundled into your library.
      external: ['vite-tsconfig-paths']
    }
  }
})
