import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { createTestingPinia } from '@pinia/testing'

export * from '@alice-health/unit-presets/src/testing-library'
import { userEvent } from '@testing-library/user-event'
import type { StateTree } from 'pinia'
import { vi } from 'vitest'

/**
 * Create a testing pinia instance
 *
 * @param storeInitialState Store initial state
 */
export function createTestPinia(storeInitialState?: StateTree | undefined) {
  return createTestingPinia({
    initialState: storeInitialState,
    stubActions: true,
    createSpy: vi.fn
  })
}

export const testRouterInstance = createRouter({
  history: createWebHistory('/'),
  routes: [
    { path: '/', name: 'home', component: async () => ({}) },
    { path: '/procedures', name: 'procedures', component: async () => ({}) },
    { path: '/create', name: 'procedures/resources/create', component: async () => ({}) },
    { path: '/', name: 'procedures/resources/list', component: async () => ({}) },
    { path: '/:id', name: 'procedures/resources/read', component: async () => ({}) },
    { path: '/:id/edit', name: 'procedures/resources/edit', component: async () => ({}) },
    {
      path: '/selection-type',
      name: 'procedures/resources/create/selection-type',
      component: async () => ({})
    },

    {
      path: '/:id/code-form',
      name: 'procedures/resources/create/code-form',
      component: async () => ({})
    },
    {
      path: '/:id/code-specialties',
      name: 'procedures/resources/edit/code-form',
      component: async () => ({})
    },
    {
      path: '/code-specialties',
      name: 'procedures/resources/create/code-specialties',
      component: async () => ({})
    },
    {
      path: '/:id/code-specialties',
      name: 'procedures/resources/edit/code-specialties',
      component: async () => ({})
    }
  ]
})

export const customRender = (
  component: Component,
  {
    props = {},
    slots = {},
    mocks = {},
    stubs = {},
    provide = {},
    query = {},
    params = {},
    store = {},
    ...other
  } = {}
) => {
  if (Object.keys(query).length) {
    testRouterInstance.push({ query })
  }

  if (Object.keys(params).length) {
    testRouterInstance.push({ params })
  }

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [
        testRouterInstance,
        VueQueryPlugin,
        createTestPinia({
          initialState: { ...store }
        })
      ],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}

const user = userEvent.setup()

export { user }
