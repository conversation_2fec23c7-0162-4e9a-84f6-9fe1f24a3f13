import type { HealthSpecialistCodesList } from '@procedures/models'
import { HttpResponse, http } from 'msw'

const pagination = {
  totalPages: 1,
  pageSize: 1
}

const results: HealthSpecialistCodesList[] = [
  {
    id: '39377bad-db40-4c64-b128-a5616713aa00',
    aliceCode: '80674504',
    primaryTuss: '50000756',
    aliceDescription:
      'Atendimento fisioterapeutico ambulatorial individual ao paciente com disfuncao decorrente de alteracoes do sistema cardiovascular',
    type: {
      friendlyName: 'Unitário',
      value: 'SINGLE',
      color: 'gray'
    },
    serviceType: {
      friendlyName: 'Exame',
      value: 'EXAM',
      color: 'yellow'
    },
    status: 'INACTIVE',
    pricingStatus: {
      friendlyName: 'Possui pendências',
      value: 'PENDING',
      color: 'red'
    },
    specialtiesText: '1 especialidade'
  },
  {
    id: 'e97c08cb-3814-4a69-8d37-45c82db9d000',
    aliceCode: '80695050',
    primaryTuss: '40808068',
    aliceDescription: 'Marcacao pre-cirurgica editada com sucesso',
    type: {
      friendlyName: 'Unitário',
      value: 'SINGLE',
      color: 'gray'
    },
    serviceType: {
      friendlyName: 'Exame',
      value: 'EXAM',
      color: 'yellow'
    },
    status: 'ACTIVE',
    pricingStatus: {
      friendlyName: 'Possui pendências',
      value: 'PENDING',
      color: 'red'
    },
    specialtiesText: '3 especialidades'
  },
  {
    id: '6df28e8f-37ec-4fd8-ba7e-1f0f82f34000',
    aliceCode: '80088466',
    primaryTuss: '30101131',
    aliceDescription: 'Exames bacterioscopicos de lesao cutanea, muco ou linfa - alterado',
    type: {
      friendlyName: 'Unitário',
      value: 'SINGLE',
      color: 'gray'
    },
    serviceType: {
      friendlyName: 'Exame',
      value: 'EXAM',
      color: 'yellow'
    },
    status: 'ACTIVE',
    pricingStatus: {
      friendlyName: 'Possui pendências',
      value: 'PENDING',
      color: 'red'
    },
    specialtiesText: '3 especialidades'
  }
]

export const healthSpecialistPayload = {
  pagination,
  results
}

export const listHealthSpecialistResourceBundle = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthSpecialistResourceBundle`,
  () => HttpResponse.json(healthSpecialistPayload)
)
