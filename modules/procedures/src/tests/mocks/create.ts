import type { GetTussCodesResponse } from '@procedures/api/queries/getTussCodes'
import { HttpResponse, http } from 'msw'

export const listOfTussCodes: GetTussCodesResponse = [
  {
    id: '123456-consultation-id',
    code: '10101012',
    description: 'Consulta médica em consultório',
    tableType: '22'
  },
  {
    id: '968acace-a546-48a7-851d-43b397d42d00',
    code: '40712516',
    description: 'Testosterona total',
    tableType: '22'
  },
  {
    id: 'e4478f25-7515-411c-90d1-30b2139d1200',
    code: '40712508',
    description: 'Testosterona livre',
    tableType: '22'
  },
  {
    id: 'cbb389b5-d64f-4285-b1d9-f058573a3e00',
    code: '40310027',
    description: 'Antibiograma (teste sensibil. antibioticos e quimioterapicos)',
    tableType: '22'
  }
]

export const listTussCodes = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareResource/tussResources`,
  () => {
    return HttpResponse.json(listOfTussCodes)
  }
)
