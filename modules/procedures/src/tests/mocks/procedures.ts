import { http, HttpResponse } from 'msw'

export const procedure = {
  id: '39377bad-db40-4c64-b128-a5616713aa00',
  primaryTuss: '50000756',
  secondaryResources: [],
  executionAmount: 1,
  executionEnvironment: 'DOES_NOT_APPLY',
  aliceDescription:
    'Atendimento fisioterapeutico ambulatorial individual ao paciente com disfuncao decorrente de alteracoes do sistema cardiovascular',
  aliceCode: '80674504',
  status: 'INACTIVE',
  serviceType: 'EXAM'
}

export const getProcedure = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthSpecialistResourceBundle/:id`,
  () => HttpResponse.json(procedure)
)
