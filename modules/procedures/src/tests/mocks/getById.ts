import { CodeServiceType, ExecutionEnvironment } from '@procedures/models/CodesConfig'
import type { Procedure } from '@procedures/models/Procedure'

import { HttpResponse, http } from 'msw'

const procedureResponse: Procedure = {
  id: '9b08db3b-9eee-4f90-9dc8-dce7d6644500',
  aliceCode: '80294449',
  primaryTuss: '31309186',
  aliceDescription: 'Biópsia',
  type: 'EXAM',
  serviceType: CodeServiceType.EXAM,
  status: 'INACTIVE',
  executionEnvironment: ExecutionEnvironment.SURGICAL,
  secondaryResources: [
    {
      id: '968acace-a546-48a7-851d-43b397d42d00',
      code: '40712516',
      description: 'Testosterona total',
      tableType: '22'
    }
  ],
  executionAmount: 1
}

export const getById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthSpecialistResourceBundle/123`,
  () => HttpResponse.json(procedureResponse)
)
