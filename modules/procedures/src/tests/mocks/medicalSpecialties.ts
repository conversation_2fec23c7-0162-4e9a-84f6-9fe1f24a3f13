import { http, HttpResponse } from 'msw'

export const getMedicalSpecialties = http.get(
  `${
    import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL
  }/healthSpecialistResourceBundle/:id/medicalSpecialties`,
  () => HttpResponse.json(medicalSpecialtyPayload)
)

export const getAllMedicalSpecialties = http.get(
  `${
    import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL
  }/medicalSpecialty/healthSpecialistResourceBundle`,
  () => HttpResponse.json(allMedicalSpecialtyPayload)
)

/*
 *  Mock data
 */

export const medicalSpecialtyPayload = {
  pagination: {
    pageSize: 10,
    totalPages: 1,
    page: 1
  },
  results: [
    {
      id: 'caeec448-2549-421a-b782-86148b1a0a00',
      name: '<PERSON><PERSON>og<PERSON>',
      isTherapy: false,
      pricingStatus: {
        friendlyName: 'Possui pendências',
        value: 'PENDING',
        color: 'RED'
      },
      hasScheduledPriceChange: false,
      medicalSpecialtyId: '684ccfe2-0752-4bce-a441-2c44b845e700'
    }
  ]
}

export const allMedicalSpecialtyPayload = {
  medicalSpecialties: [
    {
      id: 'b0e6681a-4e94-4915-bcee-4603d8100b00',
      name: 'Agendamentos',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: 'e372330f-d777-4606-a83f-aa67a8e58e00',
      name: 'Cardiologia',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: '44d302d7-90f8-40d2-8b91-97f190cdc800',
      name: 'Cirurgia Geral (Aparelho Digestivo)',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: '24a80d81-4025-44b5-8712-5ebe57e8c600',
      name: 'Cirurgia Vascular',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: 'c640a2b7-1540-42d1-94d0-ae7647eb7e00',
      name: 'Dermatologia',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: 'd8eb14ff-b79c-46b2-b770-0a2fb0b5d900',
      name: 'Endocrinologia e Metabologia',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: 'e1cd7070-4f0e-40ed-96bf-44ceb6866600',
      name: 'Especialidade sem subespecialidade',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: 'b4b507df-4d25-4a45-9314-3f70a226d600',
      name: 'Gastroenterologia clínica',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    },
    {
      id: '684ccfe2-0752-4bce-a441-2c44b845e700',
      name: 'Urologia',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: false
    }
  ],
  therapySpecialties: [
    {
      id: '5aa155a2-d654-4e1b-b842-4a380dbb6300',
      name: 'Fisioterapia',
      type: 'SPECIALTY',
      active: true,
      isAdvancedAccess: false,
      isTherapy: true
    }
  ]
}
