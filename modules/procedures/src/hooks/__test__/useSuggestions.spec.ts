import { describe, expect, it } from 'vitest'
import type { Rule } from '../useSuggestions'
import { useSuggestions } from '../useSuggestions'

type Data = {
  executionAmount: number
  secondaryResources: unknown[]
  serviceType: 'CONSULTATION' | 'PROCEDURE' | 'EXAM'
}

describe('useSuggestions', () => {
  it('should return suggestions based on validated rules', () => {
    const data: Data = {
      executionAmount: 2,
      secondaryResources: [],
      serviceType: 'CONSULTATION'
    }

    const rules: Rule[] = [
      {
        validator: data.serviceType !== 'CONSULTATION' && data.executionAmount === 2,
        value: 'Bilateral',
        order: 0
      },
      {
        validator:
          data.secondaryResources.length > 0 ||
          (data.serviceType !== 'CONSULTATION' && data.executionAmount > 1),
        value: 'Pacote',
        order: 3
      },
      {
        validator: data.serviceType === 'CONSULTATION',
        value: 'Consulta',
        order: 2
      },
      {
        validator: data.serviceType === 'CONSULTATION' && data.executionAmount > 1,
        value: `${Number(data.executionAmount)} horas`,
        order: 1
      }
    ]

    const defaults = ['de', ' - ', 'para']

    const suggestions = useSuggestions(rules, defaults)

    expect(suggestions).toEqual(['2 horas', 'Consulta', 'de', ' - ', 'para'])
  })

  it('should return only default values when no rule is validated', () => {
    const rules = [
      { validator: false, value: 'Suggestion 1' },
      { validator: false, value: 'Suggestion 2' }
    ]
    const defaults = ['Default 1', 'Default 2']

    const suggestions = useSuggestions(rules, defaults)

    expect(suggestions).toEqual(['Default 1', 'Default 2'])
  })

  it('should return an empty list when there are no rules or default values', () => {
    const rules: Rule[] = []
    const defaults: string[] = []

    const suggestions = useSuggestions(rules, defaults)

    expect(suggestions).toEqual([])
  })

  it('should return only the values of validated rules when default values are not provided', () => {
    const rules = [
      { validator: true, value: 'Suggestion 1' },
      { validator: true, value: 'Suggestion 2' }
    ]

    const suggestions = useSuggestions(rules)

    expect(suggestions).toEqual(['Suggestion 1', 'Suggestion 2'])
  })

  it('should handle empty rules and provided default values', () => {
    const rules: Rule[] = []
    const defaults: string[] = ['Default 1', 'Default 2']

    const suggestions = useSuggestions(rules, defaults)

    expect(suggestions).toEqual(['Default 1', 'Default 2'])
  })

  it('should respect the order property when sorting suggestions', () => {
    const rules: Rule[] = [
      {
        validator: true,
        value: 'Last',
        order: 3
      },
      {
        validator: true,
        value: 'First',
        order: 0
      },
      {
        validator: true,
        value: 'Second',
        order: 1
      },
      {
        validator: true,
        value: 'Third',
        order: 2
      }
    ]

    const defaults = ['Default 1', 'Default 2']

    const suggestions = useSuggestions(rules, defaults)

    expect(suggestions).toEqual(['First', 'Second', 'Third', 'Last', 'Default 1', 'Default 2'])
  })
})
