import type { RouteR<PERSON>ordRaw, NavigationGuardWithThis } from 'vue-router'

const proceduresRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/procedures',
      name: 'procedures',
      beforeEnter,
      children: [
        {
          path: 'resources/list',
          name: 'procedures/resources/list',
          component: async () => await import('@procedures/views/ListView.vue')
        },
        {
          path: 'resources/create',
          name: 'procedures/resources/create',
          component: async () => await import('@procedures/views/CreateView.vue'),
          children: [
            {
              path: 'selection-type',
              name: 'procedures/resources/create/selection-type',
              component: async () => await import('@procedures/views/Create/CodeTypeSelection.vue')
            },

            {
              path: 'code-form',
              name: 'procedures/resources/create/code-form',
              component: async () => await import('@procedures/views/Create/CodeForm.vue')
            },
            {
              path: ':id/code-form',
              name: 'procedures/resources/edit/code-form',
              component: async () => await import('@procedures/views/Create/CodeForm.vue')
            },
            {
              path: 'code-specialties',
              name: 'procedures/resources/create/code-specialties',
              component: async () => await import('@procedures/views/Create/CodeSpecialties.vue')
            },
            {
              path: ':id/code-specialties',
              name: 'procedures/resources/edit/code-specialties',
              component: async () => await import('@procedures/views/Create/CodeSpecialties.vue')
            }
          ]
        },
        {
          path: 'resources/:id',
          name: 'procedures/resources/read',
          component: async () => await import('@procedures/views/ReadCodeView.vue')
        },
        {
          path: 'resources/:id/edit',
          name: 'procedures/resources/edit',
          component: async () => await import('@procedures/views/CreateView.vue')
        }
      ]
    }
  ]
}

export default proceduresRoutes
