import type { WSkeletonComponentProps } from '@procedures/models'

export const readCodeSkeletonLine1: WSkeletonComponentProps = {
  gap: {
    row: 10,
    column: 10
  },
  layout: [
    [
      { width: 120, height: 20, borderRadius: 4 },
      { width: 40, height: 20, transparent: true },
      { width: 160, height: 20, borderRadius: 4 }
    ],
    [
      { width: 60, height: 20, borderRadius: 4 },
      { width: 20, height: 20, borderRadius: 4 },
      { width: 70, height: 20, transparent: true },
      { width: 220, height: 20, borderRadius: 4 },
      { width: 20, height: 20, borderRadius: 4 }
    ]
  ]
}

export const readCodeSkeletonLine2: WSkeletonComponentProps = {
  gap: {
    row: 10,
    column: 10
  },
  layout: [
    [
      { width: 120, height: 20, borderRadius: 4 },
      { width: 40, height: 20, transparent: true },
      { width: 120, height: 20, borderRadius: 4 }
    ],
    [
      { width: 60, height: 20, borderRadius: 4 },
      { width: 100, height: 20, transparent: true },
      { width: 100, height: 20, borderRadius: 4 }
    ]
  ]
}
