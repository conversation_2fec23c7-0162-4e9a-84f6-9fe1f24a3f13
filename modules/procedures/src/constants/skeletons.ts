import type { WSkeletonGap, WSkeletonLayout } from '@procedures/models'

export function makeCheckboxWithLabelSkeleton(width: number) {
  return [
    { width: 20, height: 20, borderRadius: 8 },
    { width, height: 16, borderRadius: 4 }
  ]
}

export const specialtiesSkeletonGap: WSkeletonGap = {
  row: 10,
  column: 20
}

export const specialtiesSkeleton1: WSkeletonLayout = [
  makeCheckboxWithLabelSkeleton(200),
  makeCheckboxWithLabelSkeleton(200),
  makeCheckboxWithLabelSkeleton(140),
  makeCheckboxWithLabelSkeleton(220),
  makeCheckboxWithLabelSkeleton(100),
  makeCheckboxWithLabelSkeleton(130),
  makeCheckboxWithLabelSkeleton(180),
  makeCheckboxWithLabelSkeleton(200)
]

export const specialtiesSkeleton2: WSkeletonLayout = [
  makeCheckboxWithLabelSkeleton(200),
  makeCheckboxWithLabelSkeleton(100),
  makeCheckboxWithLabelSkeleton(200),
  makeCheckboxWithLabelSkeleton(130),
  makeCheckboxWithLabelSkeleton(220),
  makeCheckboxWithLabelSkeleton(200),
  makeCheckboxWithLabelSkeleton(140),
  makeCheckboxWithLabelSkeleton(180)
]
