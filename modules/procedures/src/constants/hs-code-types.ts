import {
  type CodeServiceConfig,
  type CodeAttributeFriendlyDescription,
  CodeServiceType
} from '@procedures/models/CodesConfig'

export const codesServices: Array<CodeServiceConfig> = [
  {
    name: CodeServiceType.CONSULTATION,
    label: 'Consulta',
    order: 1,
    helperText:
      'Ao adicionar 2 ou mais, você estará criando um pacote de consulta, use essa opção para indicar 2h de terapia, por exemplo.',
    minAmount: 1,
    maxAmount: 14,
    hasSecondaryTuss: false
  },
  {
    name: CodeServiceType.EXAM,
    label: 'Exame',
    order: 2,
    helperText:
      'Ao adicionar 2 ou mais, você estará indicando um código que será definido por múltiplas execuções, como por exemplo exames executados em ambos olhos.',
    minAmount: 1,
    maxAmount: 5,
    hasSecondaryTuss: true
  },
  {
    name: CodeServiceType.PROCEDURE,
    label: 'Procedimento',
    order: 3,
    helperText:
      'Ao adicionar 2 ou mais, você estará indicando um código que será definido por múltiplas execuções, como por exemplo procedimentos executados em ambos olhos.',
    minAmount: 1,
    maxAmount: 5,
    hasSecondaryTuss: true
  }
]

export const codesTypes: Array<CodeAttributeFriendlyDescription> = [
  {
    name: 'BUNDLE',
    label: 'Pacote'
  },
  {
    name: 'SINGLE',
    label: 'Unitário'
  }
]

export const codesStatus: Array<CodeAttributeFriendlyDescription> = [
  {
    name: 'ACTIVE',
    label: 'Ativo'
  },
  {
    name: 'INACTIVE',
    label: 'Inativo'
  }
]
