<script setup lang="ts">
import type { TussCode } from '@procedures/models/TussCode'

import { storeToRefs } from 'pinia'
import { computed, ref, watch } from 'vue'

import { useRoute } from 'vue-router'
import { useQuery } from '@tanstack/vue-query'
import { useDebounce } from '@alice-health/vue-hooks'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'

import { getTussCodes } from '@procedures/api/queries/getTussCodes'

import {
  WAutocomplete,
  WTable,
  WTableHeader,
  WTableHeaderCell,
  WTableBody,
  WTableRow,
  WTableBodyCell,
  WButton,
  WH<PERSON><PERSON>,
  WParagraph
} from '@alice-health/wonderland-vue'
import { LabelWithTooltip } from '@procedures/components'

/*
 * Custom Types
 */

interface TussCodeWithSearch extends TussCode {
  searchDescription: string
}

/*
 * Stores
 */

const store = useCreateFormStore()
const { form } = storeToRefs(store)

/*
 * Computeds
 */

const disabled = computed(() => {
  return form.value?.executionAmount > 1
})

/*
 * Hooks
 */

const route = useRoute()

const tussCodeField = ref('')
const searchTussCodeDebouced = useDebounce(tussCodeField, 800)

/*
 * Constants
 */

const columnWidthMap = {
  code: '100px',
  description: '',
  buttons: '70px'
}

const headers = [
  { id: 1, title: 'Cód. TUSS', width: columnWidthMap.code },
  { id: 2, title: 'Descrição', width: columnWidthMap.description },
  { id: 3, width: columnWidthMap.buttons }
]

/*
 * Emits
 */

const emit = defineEmits(['setSecondaryTussCode', 'addSecondaryResource', 'clearAutocompleteInput'])

/*
 * Refs
 */

const invalid = ref(false)
const secondaryTussCodeSuggestions = ref<TussCodeWithSearch[]>([])

/*
 * Computeds
 */

const getTussCodeFilterParam = computed(() => {
  return {
    filter: `{"q": "${tussCodeField.value}"}`
  }
})

/*
 * Requests & Mutations
 */

const { data, isFetched, refetch } = useQuery({
  queryKey: ['tussCodes', getTussCodeFilterParam],
  queryFn: () => getTussCodes(getTussCodeFilterParam.value)
})

/*
 * Methods
 */

const updateTussCodeSearchKey = (event: CustomEvent) => {
  tussCodeField.value = event?.detail

  if (event?.detail === '') {
    invalid.value = false
    emit('clearAutocompleteInput')
  }
}

const tussCodeIsSelected = (tussCode: TussCode) => {
  return form.value?.secondaryResources?.some((item) => item.id === tussCode.id)
}

const addSelectedTussCode = (e: CustomEvent) => {
  const newTussCode = e.detail
  if (tussCodeIsSelected(newTussCode)) {
    return
  }

  invalid.value = form.value?.primaryTuss === newTussCode.code

  const selectedTuss = e.detail
  store.form.secondaryResources = [...(store.form.secondaryResources || []), selectedTuss]
}

const deleteTussCodeItem = (tussCode: TussCode) => {
  const newSelectedTussCode = form.value?.secondaryResources!.filter(
    (item) => item.id !== tussCode.id
  )
  store.form.secondaryResources = newSelectedTussCode
}

const searchTussCodeChange = async () => {
  if (searchTussCodeDebouced.value === '') {
    secondaryTussCodeSuggestions.value = []
    return
  }

  await refetch()

  if (isFetched) {
    secondaryTussCodeSuggestions.value =
      data.value?.map((tussCode) => ({
        ...tussCode,
        searchDescription: `${tussCode.code} - ${tussCode.description}`
      })) || []
  }
}

/*
 * Watchers
 */

watch(searchTussCodeDebouced, searchTussCodeChange)
</script>

<template>
  <div>
    <LabelWithTooltip
      label="Código TUSS secundário (opcional)"
      helperText="O TUSS secundário deve ser diferente do TUSS indicando como primário. "
    />

    <div>
      <WAutocomplete
        v-if="!route.params.id"
        leadingIcon="icSearch"
        searchByKey="searchDescription"
        placeholder="Digite o código"
        :value="tussCodeField"
        :items="secondaryTussCodeSuggestions"
        :invalid="invalid"
        :disabled="disabled"
        @wChange="updateTussCodeSearchKey"
        @wSelect="addSelectedTussCode"
      />

      <WHelper v-if="invalid" mode="error">
        O TUSS secundário deve ser diferente do primário.
      </WHelper>
    </div>
  </div>

  <WTable v-if="form?.secondaryResources?.length">
    <WTableHeader slot="header">
      <WTableHeaderCell v-for="header in headers" :key="header.id" :width="header.width">
        {{ header.title }}
      </WTableHeaderCell>
    </WTableHeader>

    <WTableBody slot="body">
      <WTableRow v-for="item in form?.secondaryResources" :key="item.id">
        <WTableBodyCell :width="columnWidthMap.code">
          <WParagraph mode="tertiary" size="large">{{ item.code }}</WParagraph>
        </WTableBodyCell>

        <WTableBodyCell :width="columnWidthMap.description">
          <WParagraph size="large">{{ item.description }}</WParagraph>
        </WTableBodyCell>

        <WTableBodyCell :width="columnWidthMap.buttons">
          <WButton
            :iconButton="true"
            icon="icTrash"
            size="small"
            variant="secondary"
            @click="deleteTussCodeItem(item)"
            v-if="!route.params.id"
          />
        </WTableBodyCell>
      </WTableRow>
    </WTableBody>
  </WTable>
</template>
