import { customRender, screen, waitFor } from '@procedures/tests'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'

import SpecialtiesTable from '../SpecialtiesTable.vue'

vi.mock('vue-router', async () => {
  const actualModule = await vi.importActual<typeof import('vue-router')>('vue-router')

  return {
    ...actualModule,
    useRoute: () => ({
      params: { id: '123' },
      query: { page: '1', pageSize: '10' }
    })
  }
})

describe('SpecialtiesTable', () => {
  it('should show loading state while fetching data', async () => {
    customRender(SpecialtiesTable)

    await waitForWonderlandComponents(expect)

    await waitFor(async () => {
      expect(await screen.findByText('Urologia')).toBeInTheDocument()
    })
  })

  it('render data from api properly', async () => {
    customRender(SpecialtiesTable)

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Carregando...')).toBeInTheDocument()
  })
})
