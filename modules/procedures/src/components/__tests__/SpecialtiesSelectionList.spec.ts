import { customRender, screen, waitFor, user, createTestPinia } from '@procedures/tests'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'
import { SpecialtiesSelectionList } from '@procedures/components'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'

import { allMedicalSpecialtyPayload } from '@procedures/tests/mocks/medicalSpecialties'
import { createPinia, setActivePinia } from 'pinia'

const mockSpecialties = allMedicalSpecialtyPayload.medicalSpecialties

const props = {
  title: 'Especialidades médicas',
  specialties: mockSpecialties,
  all: {
    label: 'Todas as especialidades',
    value: 'all'
  },
  loading: false
}

describe.skip('SpecialtiesSelectionList', () => {
  const testPinia = createTestPinia()
  const store = vi.mocked(useCreateFormStore(testPinia))

  beforeEach(() => {
    setActivePinia(createPinia())

    store.resetStore()
  })

  it('renders properly with initial state', async () => {
    customRender(SpecialtiesSelectionList, { props, store })

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Especialidades médicas')).toBeInTheDocument()
    expect(screen.getByText('Todas as especialidades')).toBeInTheDocument()
    expect(screen.getByText('Cardiologia')).toBeInTheDocument()
    expect(screen.getByText('Urologia')).toBeInTheDocument()
    expect(screen.getByText('Gastroenterologia clínica')).toBeInTheDocument()
  })

  it('renders loading state correctly', async () => {
    customRender(SpecialtiesSelectionList, {
      props: { ...props, loading: true },
      store
    })

    await waitForWonderlandComponents(expect)

    const skeletons = document.querySelector('.specialties-selection-list__skeleton')
    expect(skeletons).toBeInTheDocument()
  })

  it('handles selecting individual specialties', async () => {
    customRender(SpecialtiesSelectionList, { props, store })

    await waitForWonderlandComponents(expect)

    const dermatologyCheckbox = await screen.findByRole('checkbox', { name: 'Dermatologia' })
    await user.click(dermatologyCheckbox)

    await waitFor(() => {
      expect(screen.getByRole('checkbox', { name: 'Dermatologia' })).toBeChecked()
    })

    await user.click(dermatologyCheckbox)

    await waitFor(() => {
      expect(screen.getByRole('checkbox', { name: 'Dermatologia' })).not.toBeChecked()
    })
  })

  it.only('handles selecting all specialties', async () => {
    customRender(SpecialtiesSelectionList, { props, store })

    await waitForWonderlandComponents(expect)

    const allSpecialtiesCheckbox = await screen.findByRole('checkbox', {
      name: 'Todas as especialidades'
    })

    await user.click(allSpecialtiesCheckbox)

    await waitFor(() => {
      expect(screen.getByRole('checkbox', { name: 'Dermatologia' })).toBeChecked()
    })
  })

  it('does not show "Todas as especialidades" checkbox when there is only one specialty', async () => {
    customRender(SpecialtiesSelectionList, {
      props: { ...props, specialties: [mockSpecialties[0]] },
      store
    })

    await waitForWonderlandComponents(expect)

    expect(screen.queryByText('Todas as especialidades')).not.toBeInTheDocument()
    expect(screen.getByText('Agendamentos')).toBeInTheDocument()
  })

  it('handles "Select All" checkbox state correctly with individual selections', async () => {
    customRender(SpecialtiesSelectionList, { props, store })

    await waitForWonderlandComponents(expect)

    const allSpecialtiesCheckbox = await screen.findByRole('checkbox', {
      name: 'Todas as especialidades'
    })

    expect(allSpecialtiesCheckbox).not.toBeChecked()

    await user.click(allSpecialtiesCheckbox)
    expect(allSpecialtiesCheckbox).toBeChecked()

    const urologyCheckbox = await screen.findByRole('checkbox', { name: 'Urologia' })

    await user.click(urologyCheckbox)

    await waitFor(() => {
      expect(allSpecialtiesCheckbox).not.toBeChecked()
    })

    await user.click(urologyCheckbox)
    expect(allSpecialtiesCheckbox).toBeChecked()

    await user.click(allSpecialtiesCheckbox)
    expect(allSpecialtiesCheckbox).not.toBeChecked()
  })
})
