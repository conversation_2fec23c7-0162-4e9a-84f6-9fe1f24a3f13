import { customRender, screen } from '@procedures/tests'
import SuggestionTag from '../SuggestionTag.vue'
import userEvent from '@testing-library/user-event'

describe('SuggestionTag', () => {
  it('renders properly with default props', async () => {
    customRender(SuggestionTag, {
      props: {
        label: 'Test Label',
        icon: 'icAdd'
      }
    })

    expect(screen.getByText('Test Label')).toBeInTheDocument()
  })

  it('renders with custom icon', async () => {
    customRender(SuggestionTag, {
      props: {
        label: 'Test Label',
        icon: 'icSearch'
      }
    })

    expect(screen.getByText('Test Label')).toBeInTheDocument()
  })

  it('emits click event with label when clicked', async () => {
    const onClick = vi.fn()
    customRender(SuggestionTag, {
      props: {
        label: 'Test Label',
        icon: 'icAdd',
        onClick
      }
    })

    const button = screen.getByRole('button')
    await userEvent.click(button)
    expect(onClick).toHaveBeenCalledWith('Test Label')
  })
})
