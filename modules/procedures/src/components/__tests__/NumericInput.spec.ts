import { customRender, screen, user, fireEvent, waitFor } from '@procedures/tests'

import { NumericInput } from '@procedures/components'

describe('NumericInput', () => {
  it('renders properly', async () => {
    customRender(NumericInput, {
      props: {
        quantity: 1,
        minimum: 1,
        maximum: 2
      }
    })

    const minusButton = await screen.findByRole('button', { name: 'icDelete' })
    const plusButton = screen.getByRole('button', { name: 'icAdd' })

    expect(minusButton).toBeDisabled()
    expect(plusButton).toBeEnabled()

    expect(screen.getByRole('spinbutton')).toHaveValue(1)
  })

  it('Should disable add button if amount in input is bigger or equal to maximun value', async () => {
    customRender(NumericInput, {
      props: {
        quantity: 1,
        minimum: 1,
        maximum: 2
      }
    })

    const minusButton = await screen.findByRole('button', { name: 'icD<PERSON>te' })
    const plusButton = screen.getByRole('button', { name: 'icAdd' })

    expect(minusButton).toBeDisabled()
    expect(plusButton).toBeEnabled()

    expect(screen.getByRole('spinbutton')).toHaveValue(1)

    await fireEvent.click(plusButton)

    await waitFor(() => {
      expect(screen.getByRole('spinbutton')).toHaveValue(2)
      expect(plusButton).toBeDisabled()
    })
  })

  it('should show error message if user types value bigger than maximum', async () => {
    customRender(NumericInput, {
      props: {
        quantity: 1,
        minimum: 1,
        maximum: 2
      }
    })

    const input = await screen.findByRole('spinbutton')
    await user.type(input, '3')

    await waitFor(() => {
      expect(screen.getByText('Insira uma quantidade entre 1 e 2')).toBeInTheDocument()
    })
  })
})
