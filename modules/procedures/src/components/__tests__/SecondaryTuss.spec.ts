import { customRender, screen, fireEvent, waitFor } from '@procedures/tests'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'
import { SecondaryTussCode } from '@procedures/components'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'

const mockTussCode = {
  id: '1',
  code: '10101',
  description: 'Test TUSS Code',
  searchDescription: '10101 - Test TUSS Code'
}

const mockSecondaryTussCode = [
  {
    id: '2',
    code: '10102',
    description: 'Secondary TUSS Code',
    searchDescription: '10102 - Secondary TUSS Code',
    tableType: '22',
    tableDescription: 'Secondary TUSS Code'
  }
]

describe('SecondaryTussCode', () => {
  it('renders properly with initial state', async () => {
    customRender(SecondaryTussCode)

    const store = useCreateFormStore()

    store.form.secondaryResources = []
    store.form.primaryTuss = '10101'

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Código TUSS secundário (opcional)')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Digite o código')).toBeInTheDocument()
  })

  it('should show error when selecting same code as primary', async () => {
    customRender(SecondaryTussCode)

    const store = useCreateFormStore()

    store.form.secondaryResources = []
    store.form.primaryTuss = '10101'

    await waitForWonderlandComponents(expect)

    const autocomplete = screen.getByPlaceholderText('Digite o código')
    await fireEvent(autocomplete, new CustomEvent('wSelect', { detail: mockTussCode }))

    await waitFor(() => {
      expect(
        screen.getByText('O TUSS secundário deve ser diferente do TUSS indicando como primário.')
      ).toBeInTheDocument()
    })
  })

  it('should display selected secondary TUSS codes in table', async () => {
    customRender(SecondaryTussCode)

    const store = useCreateFormStore()

    store.form.secondaryResources = mockSecondaryTussCode
    store.form.primaryTuss = '10101'

    await waitForWonderlandComponents(expect)

    expect(await screen.findByText('10102')).toBeInTheDocument()
    expect(screen.getByText('Secondary TUSS Code')).toBeInTheDocument()
  })

  it('should remove secondary TUSS code when delete button is clicked', async () => {
    customRender(SecondaryTussCode)

    const store = useCreateFormStore()

    store.form.secondaryResources = mockSecondaryTussCode
    store.form.primaryTuss = '10101'

    await waitForWonderlandComponents(expect)

    const deleteButton = screen.getByRole('button', { name: 'icTrash' })
    await fireEvent.click(deleteButton)

    await waitFor(() => {
      expect(store.form.secondaryResources).toEqual([])
    })
  })
})
