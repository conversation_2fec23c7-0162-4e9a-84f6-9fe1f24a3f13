import { describe, it, expect, vi } from 'vitest'
import { customRender, screen, user } from '../../tests'
import InfoBlock from '../InfoBlock.vue'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'

// Mock the clipboard hook and clipboard API
vi.mock('@commons/hooks', () => ({
  useClipboard: () => ({
    copy: vi.fn().mockImplementation(async ({ text }) => {
      await navigator.clipboard.writeText(text)
    })
  })
}))

describe('InfoBlock.vue', () => {
  const defaultProps = {
    label: 'Test Label',
    value: 'Test Value'
  }

  it('renders properly with required props', async () => {
    customRender(InfoBlock, {
      props: defaultProps
    })

    await waitForWonderlandComponents(expect)

    expect(screen.getByTestId('info-block')).toBeInTheDocument()
    expect(screen.getByText('Test Label')).toBeInTheDocument()
    expect(screen.getByText('Test Value')).toBeInTheDocument()
    expect(screen.queryByRole('button')).not.toBeInTheDocument()
  })

  it('renders copy button when enableCopy is true', async () => {
    customRender(InfoBlock, {
      props: {
        ...defaultProps,
        enableCopy: true
      }
    })

    await waitForWonderlandComponents(expect)

    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('applies minWidth style when provided', async () => {
    customRender(InfoBlock, {
      props: {
        ...defaultProps,
        minWidth: '200px'
      }
    })

    await waitForWonderlandComponents(expect)

    const infoBlock = screen.getByTestId('info-block')
    expect(infoBlock).toHaveStyle({ minWidth: '200px' })
  })

  it('emits copy event when copy button is clicked', async () => {
    const clipboardSpy = vi
      .spyOn(navigator.clipboard, 'writeText')
      .mockImplementation(() => Promise.resolve())

    customRender(InfoBlock, {
      props: {
        ...defaultProps,
        enableCopy: true
      }
    })

    await waitForWonderlandComponents(expect)

    const copyButton = screen.getByRole('button')
    expect(copyButton).toBeInTheDocument()

    await user.click(copyButton)

    expect(clipboardSpy).toHaveBeenCalledWith('Test Value')
    clipboardSpy.mockRestore()
  })
})
