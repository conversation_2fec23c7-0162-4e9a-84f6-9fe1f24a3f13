import { customRender, screen } from '@procedures/tests'

import { DisplayField } from '@procedures/components'

describe('Display Field', () => {
  it('renders properly', async () => {
    customRender(DisplayField, {
      props: {
        label: 'Label do campo',
        value: 'Valor do campo'
      }
    })

    expect(screen.getByText('Label do campo')).toBeInTheDocument()
    expect(screen.getByText('Valor do campo')).toBeInTheDocument()
  })
})
