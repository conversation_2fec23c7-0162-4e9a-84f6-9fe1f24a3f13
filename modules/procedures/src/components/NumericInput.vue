<template>
  <div>
    <div class="custom-numeric-input">
      <WButton
        :iconButton="true"
        icon="icDelete"
        variant="tertiary"
        @click="decrease"
        :disabled="amount === props.minimum || props.disabled"
      />
      <WTextfield
        :value="amount"
        min="0"
        type="number"
        @w-change="inputChange"
        :invalid="invalidAmount()"
        :customValidators="[invalidAmount]"
        :disabled="props.disabled"
      />

      <WButton
        :iconButton="true"
        icon="icAdd"
        variant="tertiary"
        @click="increase"
        :disabled="amount >= props.maximum || props.disabled"
      />
    </div>
    <WHelper v-if="invalidAmount()" mode="error"
      >Insira uma quantidade entre {{ minimum }} e {{ maximum }}</WHelper
    >
  </div>
</template>
<script setup lang="ts">
import { WButton, WHelper, WTextfield } from '@alice-health/wonderland-vue'
import { onMounted, ref, watch } from 'vue'

const props = defineProps({
  quantity: {
    type: Number,
    required: false,
    default: 0
  },
  minimum: {
    type: Number,
    required: false,
    default: 0
  },
  maximum: {
    type: Number,
    required: false,
    default: Infinity
  },
  disabled: {
    type: Boolean,
    required: false,
    default: false
  }
})

const amount = ref(props.quantity)

const emit = defineEmits(['change'])

const inputChange = (e: CustomEvent) => {
  amount.value = parseInt(e.detail)
}

const increase = () => {
  amount.value = amount.value + 1
}

const decrease = () => {
  amount.value = amount.value - 1
}

const invalidAmount = () => {
  return amount.value < props.minimum || amount.value > props.maximum
}

watch(
  () => props.quantity,
  (value) => {
    amount.value = value
  }
)

watch(
  () => amount.value,
  (value) => {
    if (value) {
      emit('change', amount.value)
    }
  }
)
</script>
<style lang="scss" scoped>
.custom-numeric-input {
  display: flex;
  align-items: center;
  gap: var(--gl-spacing-02);
  max-width: 175px;
}
</style>
