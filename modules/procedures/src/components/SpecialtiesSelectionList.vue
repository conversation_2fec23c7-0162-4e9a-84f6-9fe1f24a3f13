<script lang="ts" setup>
import type { MedicalSpecialtyShortResponse } from '@procedures/api/queries'

import { useCreateFormStore } from '@procedures/store/useCreateFormStore'

import {
  specialtiesSkeleton1,
  specialtiesSkeleton2,
  specialtiesSkeletonGap
} from '@procedures/constants'

import { WCheckbox, WSkeleton, WTitle } from '@alice-health/wonderland-vue'
import { computed } from 'vue'

/*
 * Custom Types
 */

type Props = {
  title: string
  specialties: MedicalSpecialtyShortResponse[]
  all: {
    label: string
    value: string
  }
  loading: boolean
}

/*
 * Props & Emits
 */

const props = withDefaults(defineProps<Props>(), {
  specialties: () => [],
  allItemsLabel: 'Todas',
  loading: false
})

/*
 * Stores
 */

const store = useCreateFormStore()

/*
 * Methods
 */

function handleSelectAll() {
  if (isAllSelected.value) {
    store.unselectAllSpecialties()
  } else {
    const allSpecialtyIds = props.specialties.map((specialty) => specialty.id)

    store.selectAllSpecialties(allSpecialtyIds)
  }
}

const isAllSelected = computed(
  () =>
    props.specialties.length > 0 &&
    props.specialties.every((specialty) => store.specialties.includes(specialty.id))
)

function handleSpecialtyChange(event: Event) {
  const target = event.target as HTMLInputElement
  store.handleSelectSpecialty(target.value)
}
</script>

<template>
  <div class="specialties-selection-list">
    <WTitle level="2" size="medium" variant="plain">{{ title }}</WTitle>

    <div v-if="loading" class="specialties-selection-list__skeleton">
      <WSkeleton :layout="specialtiesSkeleton1" :gap="specialtiesSkeletonGap" />
      <WSkeleton :layout="specialtiesSkeleton2" :gap="specialtiesSkeletonGap" />
    </div>

    <div v-else class="specialties-selection-list__items">
      <WCheckbox
        v-if="specialties.length > 1"
        :key="all.value"
        :value="all.value"
        :label="all.label"
        :checked="isAllSelected"
        @change="handleSelectAll"
      />

      <WCheckbox
        v-for="specialty in specialties"
        :key="specialty.id"
        :label="specialty.name"
        :aria-label="specialty.name"
        :value="specialty.id"
        :checked="store.specialties.includes(specialty.id)"
        @change="handleSpecialtyChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.specialties-selection-list {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-08);
  max-width: 600px;

  &__skeleton {
    display: flex;
    flex-direction: row;
    gap: var(--gl-spacing-08);
  }

  &__items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    column-gap: var(--gl-spacing-08);
    row-gap: var(--gl-spacing-06);
  }
}
</style>
