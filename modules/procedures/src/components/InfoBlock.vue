<script lang="ts" setup>
import { useClipboard } from '@commons/hooks'

import { WButton, WLabel, WParagraph } from '@alice-health/wonderland-vue'

type Events = {
  (e: 'copy', value: string): void
}

type Props = {
  label: string
  value: string
  minWidth?: string | number
  enableCopy?: boolean
}

const emit = defineEmits<Events>()
const props = defineProps<Props>()

const { copy } = useClipboard()

async function copyToClipboard() {
  await copy({
    text: props.value,
    callback: () => emit('copy', props.value),
    notifyOnError: true
  })
}
</script>

<template>
  <div class="info-block" data-testid="info-block" :style="{ minWidth }">
    <WLabel variant="heavy" mode="primary">{{ label }}</WLabel>

    <div class="info-block__copy">
      <WParagraph variant="plain">{{ value }}</WParagraph>

      <WButton
        v-if="enableCopy"
        icon-button
        icon="icCopy"
        size="medium"
        variant="tertiary"
        @click="copyToClipboard"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.info-block {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  align-content: flex-start;

  gap: var(--gl-spacing-05);

  &__copy {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    gap: var(--gl-spacing-03);
  }
}
</style>
