<script setup lang="ts">
import type { WTagComponentProps } from '@procedures/models'

import { computed, watch } from 'vue'

import { useQuery } from '@tanstack/vue-query'
import { usePagination } from '@alice-health/vue-hooks'

import { getResourceBundleMedicalSpecialties } from '@procedures/api/queries'

import {
  WButton,
  WIcon,
  WListControllers,
  WParagraph,
  WSelect,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WTag,
  WTitle
} from '@alice-health/wonderland-vue'
import { useRoute, useRouter } from 'vue-router'

/*
 * Constants
 */

const limits = [5, 10, 15]

/*
 * Hooks
 */

const route = useRoute()
const router = useRouter()

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

/*
 * Computeds
 */

const enabled = computed(() => !!route?.params?.id)

const getMedicalSpecialtiesParams = computed(() => ({
  filter: `{"q": "${route?.params?.id}" }`,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/*
 * Queries & Mutations
 */

const {
  data: fetchedMedicalSpecialties,
  isLoading: isLoadingMedicalSpecialties,
  error: errorMedicalSpecialties,
  refetch: refetchMedicalSpecialties
} = useQuery({
  queryKey: [
    'medicalSpecialties',
    route?.params?.id,
    getMedicalSpecialtiesParams.value,
    currentPage.value,
    currentLimit.value
  ],
  queryFn: () =>
    getResourceBundleMedicalSpecialties(
      route?.params?.id as string,
      getMedicalSpecialtiesParams.value
    ),
  enabled,
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages

    return results
  }
})

/*
 * Functions
 */

function formatDate(date: string) {
  return new Date(date).toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}

function getDateRange(beginAt: string, endAt: string) {
  if (!beginAt || !endAt) return 'Sem vigência'

  if (beginAt === endAt) return formatDate(beginAt)

  if (!endAt) return `${formatDate(beginAt)} a Indefinido`

  return `${formatDate(beginAt)} a ${formatDate(endAt)}`
}

/*
 * Watchers
 */

watch(currentPage, () => refetchMedicalSpecialties())
watch(currentLimit, () => refetchMedicalSpecialties())
</script>

<template>
  <div class="specialties-table">
    <div class="specialties-table__header">
      <WTitle level="2" size="large" variant="heavy">Especialidades</WTitle>

      <WListControllers
        hide-input
        has-pagination
        margin="none"
        has-items-per-page-select
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WPaginationNext="setNext"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
          </WSelect>
        </div>
      </WListControllers>
    </div>

    <WTable>
      <WTableHeader slot="head">
        <WTableHeaderCell>
          <WParagraph variant="plain">Nome</WParagraph>
        </WTableHeaderCell>

        <WTableHeaderCell>
          <WParagraph variant="plain">Vigência</WParagraph>
        </WTableHeaderCell>

        <WTableHeaderCell>
          <WParagraph variant="plain">Status da precificação</WParagraph>
        </WTableHeaderCell>
      </WTableHeader>

      <WTableBody slot="body">
        <WTableRow v-if="isLoadingMedicalSpecialties">
          <div class="specialties-table__row-text">
            <WParagraph size="large">Carregando...</WParagraph>
          </div>
        </WTableRow>

        <WTableRow v-else-if="errorMedicalSpecialties">
          <div class="specialties-table__row-text">
            <WParagraph size="large">Erro ao carregar os dados</WParagraph>

            <WButton variant="secondary" size="small" @click="refetchMedicalSpecialties">
              Tentar novamente
            </WButton>
          </div>
        </WTableRow>

        <WTableRow v-else-if="fetchedMedicalSpecialties?.length === 0">
          <div class="specialties-table__row-text">
            <WParagraph size="large">Não há especialidades para exibir</WParagraph>
          </div>
        </WTableRow>

        <template v-else>
          <WTableRow v-for="row in fetchedMedicalSpecialties" :key="row.id">
            <WTableBodyCell>
              <WParagraph variant="plain">{{ row?.name }}</WParagraph>
            </WTableBodyCell>

            <WTableBodyCell>
              <div class="specialties-table__cell">
                <div class="specialties-table__cell-row">
                  <div class="specialties-table__cell-row-text">
                    <WParagraph variant="plain">
                      {{ getDateRange(row?.currentBeginAt, row?.currentEndAt) }}
                    </WParagraph>
                  </div>
                </div>

                <div v-if="row?.hasScheduledPriceChange" class="specialties-table__cell-update">
                  <WIcon icon="icRepeat" />
                  <WParagraph variant="plain" mode="secondary">Update agendado</WParagraph>
                </div>
              </div>
            </WTableBodyCell>

            <WTableBodyCell>
              <WTag
                v-if="row?.pricingStatus"
                :color="(row?.pricingStatus?.color?.toLowerCase() as WTagComponentProps['color'])"
                :label="row?.pricingStatus?.friendlyName"
              />
            </WTableBodyCell>
          </WTableRow>
        </template>
      </WTableBody>
    </WTable>
  </div>
</template>

<style lang="scss" scoped>
.specialties-table {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-05);

  &__cell {
    display: flex;
    flex-direction: column;

    gap: var(--gl-spacing-01);

    &-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: var(--gl-spacing-02);
    }

    &-update {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: var(--gl-spacing-02);
      color: var(--sys-color-content-tertiary);
    }
  }

  &__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    width: 100%;
  }

  &__row {
    &-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      align-content: center;

      width: 100%;

      gap: var(--gl-spacing-05);
      padding: var(--gl-spacing-10);
    }
  }
}
</style>
