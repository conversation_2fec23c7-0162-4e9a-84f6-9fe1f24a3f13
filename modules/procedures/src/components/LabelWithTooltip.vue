<script setup lang="ts">
import type { WIconComponentProps } from '@procedures/models'

import { WLabel, WIcon, WPopover, WParagraph } from '@alice-health/wonderland-vue'

type Props = {
  label: string
  icon?: WIconComponentProps['icon']
  helperText: string
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'icInfo'
})
</script>

<template>
  <div class="label--tooltip">
    <WLabel size="large">{{ props.label }} </WLabel>

    <WPopover>
      <div slot="content">
        <WParagraph>{{ props.helperText }}</WParagraph>
      </div>

      <WIcon slot="custom" :icon="props.icon" />
    </WPopover>
  </div>
</template>

<style scoped>
.label--tooltip {
  display: flex;
  align-items: center;
  gap: var(--gl-spacing-02);
  margin-bottom: var(--gl-spacing-02);
}
</style>
