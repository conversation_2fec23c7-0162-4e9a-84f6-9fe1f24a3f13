<script lang="ts" setup>
import { computed } from 'vue'

import { useQuery } from '@tanstack/vue-query'

import { getTussCodes } from '@procedures/api/queries'

import {
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WTitle,
  WParagraph,
  WButton,
  WIcon
} from '@alice-health/wonderland-vue'

import type { Procedure } from '@procedures/models'

/**
 * Types
 */

type TableRow = {
  code: string
  description: string
  executionAmount: number
  isPrimary: boolean
}

type Props = {
  id: string
  data?: Procedure | null
  isLoading: boolean
  error: Error | null
  refetch: () => void
}

/**
 * Vue Defines
 */

const props = defineProps<Props>()

/**
 * Constants
 */

const columnWidthMap = {
  code: '100px',
  description: '',
  isPrimary: '100px',
  executionAmount: '100px'
}

const headers = [
  { id: 1, title: 'TUSS', width: columnWidthMap.code },
  { id: 2, title: 'Descrição', width: columnWidthMap.description },
  { id: 3, title: 'Primário', width: columnWidthMap.isPrimary },
  { id: 4, title: 'Execuções', width: columnWidthMap.executionAmount }
]

/*
 * Requests
 */

const enabledTussCodes = computed(() => !!props.data?.primaryTuss)

const getTussCodeParams = computed(() => ({
  filter: `{"q": "${props.data?.primaryTuss}" }`
}))

const {
  data: fetchedTussCodes,
  isLoading: isLoadingTussCodes,
  error: errorTussCodes,
  refetch: refetchTussCodes
} = useQuery({
  queryKey: ['tussCodes', props.data?.primaryTuss, getTussCodeParams.value],
  queryFn: () => getTussCodes(getTussCodeParams.value),
  enabled: enabledTussCodes
})

/**
 * Computeds & Refs
 */

const hasError = computed(() => props.error || errorTussCodes.value)
const isLoading = computed(() => props.isLoading || isLoadingTussCodes.value)

const tableData = computed(() => {
  const firstItem =
    props.data?.primaryTuss && fetchedTussCodes.value?.[0]?.description
      ? {
          code: props.data.primaryTuss,
          description: fetchedTussCodes.value?.[0]?.description,
          executionAmount: props.data?.executionAmount,
          isPrimary: true
        }
      : undefined

  const items = props.data
    ? [
        firstItem,
        ...(props.data?.secondaryResources || []).map((resource) => ({
          code: resource.code,
          description: resource.description,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          executionAmount: (resource as any).executionAmount || 1,
          isPrimary: false
        }))
      ]
    : ([] as TableRow[])

  return items.filter(Boolean)
})

/**
 * Functions
 */

function refetchAll() {
  props.refetch()
  refetchTussCodes()
}
</script>

<template>
  <div class="prices-table">
    <WTitle level="2" size="large" variant="heavy">Tabela de preços</WTitle>

    <WTable>
      <WTableHeader slot="header">
        <WTableHeaderCell
          size="large"
          v-for="header in headers"
          :width="header.width"
          :key="header.id"
        >
          {{ header?.title }}
        </WTableHeaderCell>
      </WTableHeader>

      <WTableBody slot="body">
        <WTableRow v-if="isLoading || isLoadingTussCodes">
          <div class="prices-table-row-text">
            <WParagraph size="large">Carregando...</WParagraph>
          </div>
        </WTableRow>

        <WTableRow v-else-if="hasError">
          <div class="prices-table-row-text">
            <WParagraph size="large">Erro ao carregar os dados</WParagraph>

            <WButton variant="secondary" size="small" @click="refetchAll">
              Tentar novamente
            </WButton>
          </div>
        </WTableRow>

        <WTableRow v-else-if="tableData.length === 0">
          <div class="prices-table-row-text">
            <WParagraph size="large">Não há códigos para exibir</WParagraph>
          </div>
        </WTableRow>

        <template v-else>
          <WTableRow v-for="row in tableData" :key="row?.code">
            <WTableBodyCell :width="columnWidthMap.code">
              <WParagraph mode="tertiary" size="large">
                {{ row?.code }}
              </WParagraph>
            </WTableBodyCell>

            <WTableBodyCell style="word-break: break-all" :width="columnWidthMap.description">
              <WParagraph size="large">
                {{ row?.description }}
              </WParagraph>
            </WTableBodyCell>

            <WTableBodyCell :width="columnWidthMap.isPrimary">
              <WParagraph size="large">
                <WIcon :icon="row?.isPrimary ? 'icChatSent' : 'icClose'" />
              </WParagraph>
            </WTableBodyCell>

            <WTableBodyCell :width="columnWidthMap.executionAmount">
              <WParagraph size="large">
                {{ row?.executionAmount }}
              </WParagraph>
            </WTableBodyCell>
          </WTableRow>
        </template>
      </WTableBody>
    </WTable>
  </div>
</template>

<style lang="scss" scoped>
.prices-table {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-05);

  &-cell-date {
    display: flex;
    flex-direction: column;

    gap: var(--gl-spacing-01);
  }

  &-cell-date-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--gl-spacing-02);
  }

  &-cell-date-update {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--gl-spacing-02);
    color: var(--sys-color-content-tertiary);
  }

  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    width: 100%;
  }

  &-row-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-content: center;

    width: 100%;

    gap: var(--gl-spacing-05);
    padding: var(--gl-spacing-10);
  }
}
</style>
