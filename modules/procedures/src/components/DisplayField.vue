<template>
  <div class="display">
    <WLabel size="large" v-if="props.label">{{ props.label }}</WLabel>
    <div class="display__content">
      <WParagraph size="large">{{ props.value }}</WParagraph>
    </div>
  </div>
</template>
<script setup lang="ts">
import { WParagraph, WLabel } from '@alice-health/wonderland-vue'

const props = defineProps({
  label: {
    type: String,
    required: false
  },
  value: {
    type: String,
    required: true
  }
})
</script>
<style scoped lang="scss">
.display {
  display: flex;
  min-height: 76px;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--gl-spacing-02);
  align-self: stretch;

  &__content {
    display: flex;
    padding: var(--gl-spacing-03);
    align-items: flex-start;
    gap: var(--gl-spacing-03);
    flex: 1 0 0;
    align-self: stretch;
    border-bottom: var(--gl-border-width-xs) solid var(--sys-color-stroke-disabled);
  }
}
</style>
