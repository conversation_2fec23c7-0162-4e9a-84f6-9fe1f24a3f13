<script lang="ts" setup>
import type { WIconComponentProps } from '@procedures/models'
import { WIcon, WParagraph } from '@alice-health/wonderland-vue'

type Props = {
  label: string
  icon: WIconComponentProps['icon']
}

defineEmits(['click'])

withDefaults(defineProps<Props>(), {
  icon: 'icAdd'
})
</script>
<template>
  <!-- eslint-disable-next-line @alice-health/w-button -->
  <button class="suggestion_tag" @click="$emit('click', label)">
    <WParagraph variant="plain">
      {{ label }}
    </WParagraph>
    <WIcon size="medium" :icon="icon" />
  </button>
</template>
<style lang="scss" scoped>
.suggestion_tag {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  gap: var(--gl-spacing-02);
  height: var(--gl-spacing-08);
  width: fit-content;
  padding: var(--gl-spacing-02) var(--gl-spacing-04);
  border: 1px solid var(--sys-color-stroke-idle);
  border-radius: 8px;
  background: var(--gl-color-shades-white);
  cursor: pointer;
}
</style>
