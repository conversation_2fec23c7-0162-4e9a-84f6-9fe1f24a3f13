import { describe, it, expect } from 'vitest'

import { getHealthSpecialistCodes } from '../getHealthSpecialistCodes'

import { healthSpecialistPayload } from '@procedures/tests/mocks/list'

describe('queries | getHealthSpecialistCodes', () => {
  it('should fetch health specialist codes', async () => {
    const result = await getHealthSpecialistCodes()

    expect(result).toEqual(healthSpecialistPayload)
  })
})
