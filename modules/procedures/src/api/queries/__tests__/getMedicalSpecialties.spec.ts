import { describe, it, expect } from 'vitest'

import { getResourceBundleMedicalSpecialties } from '../getMedicalSpecialties'

import { medicalSpecialtyPayload } from '@procedures/tests/mocks/medicalSpecialties'

describe('getMedicalSpecialties', () => {
  it('should fetch medical specialties for a resource bundle with ID', async () => {
    const result = await getResourceBundleMedicalSpecialties('123', { param1: 'value1' })

    expect(result).toEqual(medicalSpecialtyPayload)
  })
})
