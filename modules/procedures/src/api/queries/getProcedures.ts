import type { Procedure } from '@procedures/models'
import type { PaginatedResponse } from '@commons/index'

import { useQueryString } from '@alice-health/vue-hooks'

import http from '@http/index'

export type ShortProcedure = Omit<Procedure, 'createdAt' | 'updatedAt' | 'description'>
export type GetProceduresResponse = PaginatedResponse<ShortProcedure[]>
export type GetProcedureResponse = Procedure

export const getProceduresList = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<GetProceduresResponse>(
    `/healthSpecialistResourceBundle${queryString}`
  )

  return data
}

export const getProcedure = async (id: string) => {
  const { data } = await http.get<GetProcedureResponse>(`/healthSpecialistResourceBundle/${id}`)

  return data
}
