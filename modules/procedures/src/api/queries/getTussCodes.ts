import type { TussCode } from '@procedures/models'

import { useQueryString } from '@alice-health/vue-hooks'

import http from '@http/index'

export type GetTussCodesResponse = TussCode[]

export const getTussCodes = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<GetTussCodesResponse>(
    `/healthcareResource/tussResources${queryString}`
  )
  return data
}
