import type { PaginatedResponse } from '@commons/index'
import type { HealthSpecialistCodes, HealthSpecialistCodesList } from '@procedures/models'

import { useQueryString } from '@alice-health/vue-hooks'

import http from '@http/index'

export type ShortHealthSpecialistCodes = Omit<
  HealthSpecialistCodes,
  'createdAt' | 'updatedAt' | 'description'
>
export type GetHealthSpecialistCodesResponse = PaginatedResponse<HealthSpecialistCodesList[]>

export const getHealthSpecialistCodes = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<GetHealthSpecialistCodesResponse>(
    `/healthSpecialistResourceBundle${queryString}`
  )
  return data
}
