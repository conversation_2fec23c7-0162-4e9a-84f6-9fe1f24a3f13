/* eslint-disable @typescript-eslint/no-unused-vars */
import type { Procedure } from '@procedures/models'

import http from '@http/index'

export const createHealthSpecialistAliceCode = async (form: Partial<Procedure>) => {
  const { aliceCode: _unused, specialtiesCount: _unused2, ...rest } = form

  const { data } = await http.post<Procedure>(`/healthSpecialistResourceBundle`, rest)

  return data
}

export const updateHealthSpecialistAliceCode = async (
  id: string,
  form: Partial<Procedure & { medicalSpecialtyIds: string[] }>
) => {
  const { data } = await http.put<Procedure>(`/healthSpecialistResourceBundle/${id}`, form)

  return data
}

export const updateSpecialtiesAndStatus = async ({
  id,
  form
}: {
  id: string | null
  form?: Partial<{ medicalSpecialtyIds: string[]; status: string }>
}) => {
  if (!id) throw new Error('ID is required')

  const { data } = await http.patch<Procedure>(`/healthSpecialistResourceBundle/${id}`, form)

  return data
}
