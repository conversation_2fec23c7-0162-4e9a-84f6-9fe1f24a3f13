import {
  codesServices,
  codesStatus,
  codesTypes,
  executionEnvironments
} from '@procedures/constants'
import {
  type CodeServiceConfig,
  type CodeAttributeFriendlyDescription
} from '@procedures/models/CodesConfig'

type codesFriendlyValues = {
  [key: string]: Array<CodeAttributeFriendlyDescription | CodeServiceConfig>
}

const codesFriendlyValues: codesFriendlyValues = {
  serviceType: codesServices,
  type: codesTypes,
  status: codesStatus
}

export function getFriendlyServiceType(serviceType: string) {
  return getFriendlyDescription('serviceType', serviceType)
}

export function getFriendlyType(type: string) {
  return getFriendlyDescription('type', type)
}

export function getFriendlyStatus(status: string) {
  return getFriendlyDescription('status', status)
}

export function getFriendlyDescription(attribute: string, value: string) {
  const objArray = codesFriendlyValues[attribute]
  return objArray.find((obj) => obj.name === value)?.label
}

export function getFriendlyExecutionEnvironment(executionEnvironment: string) {
  return executionEnvironments[executionEnvironment]
}
