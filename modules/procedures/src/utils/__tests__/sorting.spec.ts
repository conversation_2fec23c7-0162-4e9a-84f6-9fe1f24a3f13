import { sortByAlphabeticalOrder } from '@procedures/utils'

describe('sortByAlphabeticalOrder', () => {
  it('should sort an array of objects by a given key in alphabetical order', () => {
    const array = [{ name: '<PERSON>' }, { name: '<PERSON>' }, { name: '<PERSON>' }]

    const sortedArray = sortByAlphabeticalOrder(array, 'name')

    expect(sortedArray).toEqual([{ name: '<PERSON>' }, { name: '<PERSON>' }, { name: '<PERSON>' }])
  })

  it('should sort an array already in alphabetical order', () => {
    const array = [{ name: '<PERSON>' }, { name: '<PERSON>' }, { name: '<PERSON>' }]

    const sortedArray = sortByAlphabeticalOrder(array, 'name')

    expect(sortedArray).toEqual([{ name: '<PERSON>' }, { name: '<PERSON>' }, { name: '<PERSON>' }])
  })

  it('should sort an array by other key', () => {
    const array = [
      { name: '<PERSON>', city: 'New York' },
      { name: '<PERSON>', city: 'Los Angeles' },
      { name: '<PERSON>', city: 'Chicago' }
    ]

    const sortedArray = sortByAlphabeticalOrder(array, 'city')

    expect(sortedArray).toEqual([
      { name: '<PERSON>', city: 'Chicago' },
      { name: 'Jane', city: 'Los Angeles' },
      { name: 'John', city: 'New York' }
    ])
  })
})
