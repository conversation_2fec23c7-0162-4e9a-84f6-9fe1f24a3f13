import { customRender, screen, waitFor, user } from '@procedures/tests'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'
import CodeSpecialties from '@procedures/views/Create/CodeSpecialties.vue'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'
import { allMedicalSpecialtyPayload } from '@procedures/tests/mocks/medicalSpecialties'

describe.skip('CodeSpecialties', () => {
  it('renders properly with initial state', async () => {
    customRender(CodeSpecialties, {
      params: { id: '123' }
    })

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Clínicas e cirúrgicas')).toBeInTheDocument()
    expect(screen.getByText('Terapias')).toBeInTheDocument()
    expect(screen.getByText('Ativar código')).toBeInTheDocument()
    expect(screen.getByText('Continuar')).toBeInTheDocument()
  })

  it('renders loading state correctly', async () => {
    customRender(CodeSpecialties, {
      params: { id: '123' }
    })

    await waitForWonderlandComponents(expect)

    const loadingElements = document.querySelectorAll('.specialties-selection-list__skeleton')
    expect(loadingElements.length).toBeGreaterThan(0)
  })

  it('handles code activation toggle correctly', async () => {
    customRender(CodeSpecialties, {
      params: { id: '123' }
    })

    await waitForWonderlandComponents(expect)

    const store = useCreateFormStore()
    const activationSwitch = screen.getByRole('switch', { name: 'Ativar código' })

    expect(activationSwitch).not.toBeChecked()

    await user.click(activationSwitch)

    await waitFor(() => {
      expect(store.form.status).toBe('ACTIVE')
    })

    await user.click(activationSwitch)

    await waitFor(() => {
      expect(store.form.status).toBe('INACTIVE')
    })
  })

  it('disables continue button when no specialties are selected', async () => {
    customRender(CodeSpecialties, {
      params: { id: '123' }
    })

    await waitForWonderlandComponents(expect)

    const store = useCreateFormStore()
    store.resetStore()

    const continueButton = screen.getByRole('button', { name: 'Continuar' })
    expect(continueButton).toBeDisabled()
  })

  it('enables continue button when specialties are selected', async () => {
    customRender(CodeSpecialties, {
      params: { id: '123' }
    })

    await waitForWonderlandComponents(expect)

    const store = useCreateFormStore()
    store.specialties = ['specialty-1', 'specialty-2']

    const continueButton = screen.getByRole('button', { name: 'Continuar' })
    expect(continueButton).toBeEnabled()
  })

  it('shows validation errors when trying to continue without selecting specialties', async () => {
    customRender(CodeSpecialties, {
      params: { id: '123' }
    })

    await waitForWonderlandComponents(expect)

    const continueButton = screen.getByRole('button', { name: 'Continuar' })
    await user.click(continueButton)

    const store = useCreateFormStore()
    expect(store.currentStep).not.toBe(2)
  })

  it('loads and displays medical specialties correctly', async () => {
    customRender(CodeSpecialties, {
      params: { id: '123' }
    })

    await waitForWonderlandComponents(expect)

    await waitFor(() => {
      expect(screen.getByText('Cardiologia')).toBeInTheDocument()
    })

    const medicalSpecialties = allMedicalSpecialtyPayload.medicalSpecialties
    medicalSpecialties.forEach((specialty) => {
      expect(screen.getByText(specialty.name)).toBeInTheDocument()
    })
  })
})
