import type { Procedure } from '@procedures/models'
import { ProcedureStatusEnum } from '@procedures/schemas/ProcedureForm'
import { CodeServiceType, ExecutionEnvironment } from '@procedures/models/CodesConfig'

import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'
import { customRender, screen, user, waitFor, within } from '@procedures/tests'

import CodeForm from '@procedures/views/Create/CodeForm.vue'
import CodeTypeSelection from '@procedures/views/Create/CodeTypeSelection.vue'

describe.skip('CodeForm', () => {
  const store = {
    createProcedureStore: {
      totalSteps: 3,
      currentStep: 1,
      form: {
        serviceType: CodeServiceType.CONSULTATION,
        executionEnvironment: ExecutionEnvironment.DOES_NOT_APPLY,
        type: CodeTypeSelection.CONSULTATION,
        status: ProcedureStatusEnum.enum.INACTIVE,
        primaryTuss: '**********',
        aliceCode: '**********',
        aliceDescription: 'Teste',
        secondaryResources: [],
        executionAmount: 1,
        specialtiesCount: 0,
        tussDescription: 'Teste description TUSS'
      } as Procedure
    }
  }

  const procedureFormStore = {
    createProcedureStore: {
      form: {
        ...store.createProcedureStore.form,
        serviceType: CodeServiceType.PROCEDURE,
        executionEnvironment: ExecutionEnvironment.OUTPATIENT,
        status: ProcedureStatusEnum.enum.ACTIVE,
        type: CodeTypeSelection.PROCEDURE
      }
    }
  }

  it('renders properly with consultation type', async () => {
    customRender(CodeForm, { store })

    await waitForWonderlandComponents(expect)

    expect(await screen.findByRole('spinbutton')).toBeInTheDocument()
    expect(await screen.findByPlaceholderText('Digite a descrição')).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: 'Continuar' })).toBeInTheDocument()
  })

  it('renders properly with procedure type', async () => {
    customRender(CodeForm, { store: procedureFormStore })

    await waitForWonderlandComponents(expect)

    expect(await screen.findByRole('spinbutton')).toBeInTheDocument()
    expect(await screen.findByPlaceholderText('Digite a descrição')).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: 'Continuar' })).toBeInTheDocument()
    expect(await screen.findByText('Ambiente de execução do procedimento')).toBeInTheDocument()
    expect(await screen.findByRole('radio', { name: 'Ambulatorial' })).toBeInTheDocument()
    expect(await screen.findByRole('radio', { name: 'Hospitalar / Cirúrgico' })).toBeInTheDocument()
  })

  it('should update form when selecting a tuss code', async () => {
    customRender(CodeForm)

    await waitForWonderlandComponents(expect)

    const autocomplete = await screen.findByRole('combobox')
    const input = within(autocomplete).getByRole('textbox')

    await user.type(input, 'teste')

    await waitFor(async () => {
      const listitem = await screen.findByRole('listitem')
      expect(listitem).toBeInTheDocument()
    })

    await user.click(screen.getByRole('listitem'))

    const descriptionInput = screen.getByPlaceholderText('Digite a descrição')
    await user.type(descriptionInput, 'Test description')

    const continueButton = screen.getByRole('button', { name: 'Continuar' })
    await user.click(continueButton)

    await waitFor(() => {
      expect(screen.getByText('Descrição do código Alice')).toBeInTheDocument()
    })
  })

  it('should show tuss code suggestions when typing', async () => {
    customRender(CodeForm, { store })

    await waitForWonderlandComponents(expect)

    const autocomplete = await screen.findByRole('combobox')
    const input = within(autocomplete).getByRole('textbox')

    await user.type(input, 'teste')

    await waitFor(
      () => {
        const suggestions = screen.getAllByRole('listitem')
        expect(suggestions.length).toBeGreaterThan(0)
      },
      { timeout: 2000 }
    )
  })

  it('should show secondary tuss field for exam type when execution amount is 1', async () => {
    const examStore = {
      createProcedureStore: {
        ...store.createProcedureStore,
        form: {
          ...store.createProcedureStore.form,
          serviceType: CodeServiceType.EXAM,
          executionAmount: 1
        }
      }
    }

    customRender(CodeForm, { store: examStore })

    await waitForWonderlandComponents(expect)

    await waitFor(
      () => {
        const tussAutocompletes = screen.getAllByRole('combobox')
        expect(tussAutocompletes).toHaveLength(2)
      },
      { timeout: 2000 }
    )
  })

  it('should hide secondary tuss field when execution amount is greater than 1', async () => {
    const examStore = {
      createProcedureStore: {
        ...store.createProcedureStore,
        form: {
          ...store.createProcedureStore.form,
          serviceType: CodeServiceType.EXAM,
          executionAmount: 2
        }
      }
    }

    customRender(CodeForm, { store: examStore })

    await waitForWonderlandComponents(expect)

    const tussAutocompletes = await screen.findAllByRole('combobox')
    expect(tussAutocompletes).toHaveLength(1)
  })
})
