import { customRender, screen, waitFor } from '@procedures/tests'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'

import ListView from '@procedures/views/ListView.vue'

describe('ListView', () => {
  it('renders properly', async () => {
    customRender(ListView)

    await waitForWonderlandComponents(expect)

    expect(
      await screen.findByRole('button', {
        name: 'Cadastrar novo código'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()
    expect(await screen.findByRole('textbox')).toBeInTheDocument()

    expect(screen.getByText('Código Alice')).toBeInTheDocument()
    expect(screen.getByText('TUSS associado')).toBeInTheDocument()
    expect(screen.getByText('Descrição do código Alice')).toBeInTheDocument()
    expect(screen.getByText('Status precificação')).toBeInTheDocument()
    expect(screen.getByText('Ativo')).toBeInTheDocument()
    expect(screen.getByText('Disponível para')).toBeInTheDocument()
    expect(screen.getByText('Classificação')).toBeInTheDocument()
    expect(screen.getByText('Tipo')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.queryByText('Carregando...')).not.toBeInTheDocument()
    })

    expect(screen.getByText('80674504')).toBeInTheDocument()
    expect(screen.getByText('50000756')).toBeInTheDocument()
    expect(
      screen.getByText(
        'Atendimento fisioterapeutico ambulatorial individual ao paciente com disfuncao decorrente de alteracoes do sistema cardiovascular'
      )
    ).toBeInTheDocument()
    expect(screen.getAllByText('Unitário')).toHaveLength(3)
    expect(screen.getAllByText('Exame')).toHaveLength(3)
    expect(screen.getAllByText('Possui pendências')).toHaveLength(3)
    expect(screen.getAllByText('1 especialidade')).toHaveLength(1)
    expect(screen.getAllByText('3 especialidades')).toHaveLength(2)
  })
})
