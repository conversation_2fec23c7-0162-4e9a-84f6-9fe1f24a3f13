import { customRender } from '@procedures/tests'
import { screen } from '@testing-library/vue'

import CodeTypeSelections from '@procedures/views/Create/CodeTypeSelection.vue'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'

describe('Code Type Selections', () => {
  it('renders properly', async () => {
    customRender(CodeTypeSelections, {
      props: {
        step: 1,
        numberSteps: 3
      }
    })

    await waitForWonderlandComponents(expect)

    expect(await screen.findByText('Consulta')).toBeInTheDocument()
    expect(screen.getByText('Exame')).toBeInTheDocument()
    expect(screen.getByText('Procedimento')).toBeInTheDocument()
  })
})
