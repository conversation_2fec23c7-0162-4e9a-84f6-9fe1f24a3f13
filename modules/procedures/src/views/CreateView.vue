<script setup lang="ts">
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'

import { useCreateFormStore } from '@procedures/store/useCreateFormStore'

import { RouterView, useRoute } from 'vue-router'
import { WLink } from '@alice-health/wonderland-vue'

import ViewLayout from '@commons/layout/ViewLayout.vue'

const route = useRoute()
const store = useCreateFormStore()

const { currentViewTitle, pageStepTitle } = storeToRefs(store)

onMounted(() => {
  store.setId(route?.params?.id as string)

  store.navigateByStep(store.currentStep)
})
</script>

<template>
  <ViewLayout :title="currentViewTitle" :aboveTitle="pageStepTitle">
    <template #navigation>
      <WLink href="#" @click="store.prevStep">Voltar</WLink>
    </template>

    <template #content>
      <RouterView />
    </template>
  </ViewLayout>
</template>
