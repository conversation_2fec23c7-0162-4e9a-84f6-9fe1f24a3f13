<!-- eslint-disable @typescript-eslint/no-explicit-any -->
<script setup lang="ts">
import type { ViewLayoutStyles } from '@commons/types'
import { CodeServiceType, ExecutionEnvironment } from '@procedures/models/CodesConfig'

import { computed, onBeforeMount, type CSSProperties } from 'vue'

import { useRoute, useRouter } from 'vue-router'
import { useQuery, useQueryClient } from '@tanstack/vue-query'

import { getProcedure } from '@procedures/api/queries'
import { codesServices, executionEnvironments } from '@procedures/constants'
import { readCodeSkeletonLine1, readCodeSkeletonLine2 } from '@procedures/constants/read-code'

import {
  WButton,
  WLink,
  WParagraph,
  WSkeleton,
  WSwitch,
  WTooltip
} from '@alice-health/wonderland-vue'

import InfoBlock from '@procedures/components/InfoBlock.vue'
import ViewLayout from '@commons/layout/ViewLayout.vue'
import PricesTable from '@procedures/components/PricesTable.vue'
import SpecialtiesTable from '@procedures/components/SpecialtiesTable.vue'

/**
 * Constants
 */

const footer: CSSProperties = {
  flexDirection: 'column',
  gap: 'var(--gl-spacing-10)'
}

const layoutStyles: ViewLayoutStyles = { footer }

const queryKeysToInvalidate = [
  'procedureId',
  'tussCodes',
  'medicalSpecialties',
  'codeSelectedMedicalSpecialties'
]

/*
 * Hooks
 */

const route = useRoute()
const router = useRouter()
const queryClient = useQueryClient()

/*
 * Computeds & Refs
 */

const id = computed(() => route.params.id as string)

/*
 * Requests
 */

const enabled = computed(() => !!id.value)

const {
  data,
  isLoading: isLoadingProcedure,
  error: errorProcedure,
  refetch: refetchProcedure
} = useQuery({
  queryKey: ['procedureId', id.value],
  queryFn: () => getProcedure(id.value),
  enabled
})

/*
 * Computeds
 */

const showExecEnvBlock = computed(
  () =>
    data?.value?.executionEnvironment &&
    data?.value?.executionEnvironment !== ExecutionEnvironment.DOES_NOT_APPLY
)

/*
 * Functions
 */

function getCodeService(serviceType: CodeServiceType) {
  return codesServices.find((service) => service.name === serviceType)?.label ?? ''
}

function getExecutionEnvironment(executionEnvironment: ExecutionEnvironment) {
  return executionEnvironments[executionEnvironment]
}

function accessPriceTable() {
  router.push({
    name: 'procedures/resources/price-table',
    params: { id: id.value }
  })
}

function handleBack() {
  router.push({
    name: 'procedures/resources/list'
  })
}

function editProcedure() {
  router.push({
    name: 'procedures/resources/edit/code-form',
    params: { id: route.params.id }
  })
}

/*
 * Lifecycle hooks
 */

onBeforeMount(() => {
  queryKeysToInvalidate.forEach((queryKey) =>
    queryClient.invalidateQueries({ queryKey: [queryKey] })
  )
})
</script>

<template>
  <ViewLayout title="Detalhes do código de HS" :styles="layoutStyles">
    <template #navigation>
      <WLink @click="handleBack" key="back-button">Voltar</WLink>
    </template>

    <template #content>
      <div class="read-code">
        <WSkeleton
          v-if="isLoadingProcedure"
          :layout="readCodeSkeletonLine1.layout"
          :gap="readCodeSkeletonLine1.gap"
        />

        <WSkeleton
          v-if="isLoadingProcedure"
          :layout="readCodeSkeletonLine2.layout"
          :gap="readCodeSkeletonLine2.gap"
        />

        <div class="read-code__infos">
          <InfoBlock
            v-if="data?.aliceCode"
            enableCopy
            minWidth="120px"
            label="Código Alice"
            :value="data?.aliceCode"
          />

          <InfoBlock
            v-if="data?.aliceDescription"
            enableCopy
            label="Descrição do código Alice"
            :value="data?.aliceDescription"
          />
        </div>

        <div class="read-code__infos">
          <InfoBlock
            v-if="!isLoadingProcedure"
            minWidth="120px"
            label="Classificação"
            :value="getCodeService(data?.serviceType as CodeServiceType)"
          />

          <InfoBlock
            v-if="showExecEnvBlock"
            label="Ambiente de execução"
            :value="getExecutionEnvironment(data?.executionEnvironment as ExecutionEnvironment)"
          />
        </div>

        <PricesTable
          :id="id"
          :data="data"
          :is-loading="isLoadingProcedure"
          :error="errorProcedure"
          :refetch="refetchProcedure"
        />

        <SpecialtiesTable :id="id" />
      </div>
    </template>

    <template #footer>
      <div class="read-code__activation">
        <div class="read-code__activation-label">
          <WParagraph variant="plain" size="large">Ativar código</WParagraph>

          <WParagraph variant="plain" size="medium" mode="secondary">
            Essa opção permite exibir ou ocultar o código para o especialista, mas ele só será
            exibido após a precificação.
          </WParagraph>
        </div>

        <WSwitch disabled :checked="data?.status === 'ACTIVE'" size="small" />
      </div>

      <div class="read-code__actions">
        <WTooltip label="Funcionalidade em desenvolvimento">
          <WButton disabled variant="cta" size="medium" @click="accessPriceTable">
            Acessar tabela de preços
          </WButton>
        </WTooltip>

        <WButton variant="secondary" size="medium" @click="editProcedure">
          Editar código e especialidades
        </WButton>
      </div>
    </template>
  </ViewLayout>
</template>

<style lang="scss" scoped>
.read-code {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-10);

  width: 100%;

  &__infos {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--gl-spacing-16);
  }

  &__actions {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: var(--gl-spacing-10);

    width: 100%;
  }

  &__activation {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    gap: var(--gl-spacing-10);

    width: 100%;
  }

  &__table {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-05);

    &-cell-date {
      display: flex;
      flex-direction: column;

      gap: var(--gl-spacing-01);
    }

    &-cell-date-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: var(--gl-spacing-02);
    }

    &-cell-date-update {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: var(--gl-spacing-02);
      color: var(--sys-color-content-tertiary);
    }

    &-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      width: 100%;
    }

    &-row-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      align-content: center;

      width: 100%;

      gap: var(--gl-spacing-05);
      padding: var(--gl-spacing-10);
    }
  }
}
</style>
