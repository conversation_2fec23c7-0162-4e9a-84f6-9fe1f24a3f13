<script setup lang="ts">
import type { WIconComponentProps } from '@procedures/models'

import { computed, onBeforeMount, ref, watch } from 'vue'

import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'

import {
  getHealthSpecialistCodes,
  type GetHealthSpecialistCodesResponse
} from '@procedures/api/queries/getHealthSpecialistCodes'

import { ListLayout } from '@commons/index'

import {
  WButton,
  WIcon,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WTag,
  WTitle,
  WSelect,
  WParagraph
} from '@alice-health/wonderland-vue'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'

/**
 * Custom Types
 */

type Status = 'ACTIVE' | 'INACTIVE'

type IconMap = Record<Status, WIconComponentProps>

/**
 * Constants
 */

const limits = [5, 10, 15]
const columnWidthMap = {
  aliceCode: '120px',
  primaryTuss: '130px',
  aliceDescription: '500px',
  pricingStatus: '160px',
  status: '100px',
  specialtiesCount: '200px',
  serviceType: '150px',
  type: '150px',
  action: '100px'
}
type TableHeader = {
  id: number
  title?: string
  width?: string
}

const headers: TableHeader[] = [
  { id: 1, title: 'Código Alice', width: columnWidthMap.aliceCode },
  { id: 2, title: 'TUSS associado', width: columnWidthMap.primaryTuss },
  { id: 3, title: 'Descrição do código Alice', width: columnWidthMap.aliceDescription },
  { id: 4, title: 'Status precificação', width: columnWidthMap.pricingStatus },
  { id: 5, title: 'Ativo', width: columnWidthMap.status },
  { id: 6, title: 'Disponível para', width: columnWidthMap.specialtiesCount },
  { id: 7, title: 'Classificação', width: columnWidthMap.serviceType },
  { id: 8, title: 'Tipo', width: columnWidthMap.type },
  { id: 9, width: columnWidthMap.action }
]

const queryKeysToInvalidate = [
  'tussCodes',
  'procedureId',
  'medicalSpecialties',
  'codeSelectedMedicalSpecialties'
]

/**
 * Computed Variables
 */

const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const filter = computed(() => (term.value ? objectToJsonString({ q: term.value }) : ''))

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

const statusIcon = computed<IconMap>(() => ({
  ACTIVE: { icon: 'icSuccess', size: 'large' },
  INACTIVE: { icon: 'icClose', size: 'medium' }
}))

/**
 * Hooks
 */

const store = useCreateFormStore()

const queryClient = useQueryClient()

const route = useRoute()
const router = useRouter()
const searchTerm = ref('')
const searchTermDebounced = useDebounce(searchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data, isLoading, error, refetch } = useQuery({
  queryKey: ['procedures', getParams],
  queryFn: () => getHealthSpecialistCodes(getParams.value),
  select
})

/**
 * Functions
 */

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function select({ results, pagination }: GetHealthSpecialistCodesResponse) {
  totalPages.value = pagination.totalPages

  return results
}

function onCreate() {
  store.$dispose()
  store.resetStore()

  router.push({ name: 'procedures/resources/create/selection-type' })
}

function goToProcedure(id?: string | null) {
  if (!id) return

  router.push({ name: 'procedures/resources/read', params: { id } })
}

/**
 * Watchers & Lifecycle Hooks
 */

watch(searchTermDebounced, handleSearch)

/*
 * Lifecycle hooks
 */

onBeforeMount(() => {
  store.resetStore()

  queryKeysToInvalidate.forEach((queryKey) =>
    queryClient.invalidateQueries({ queryKey: [queryKey] })
  )
})
</script>

<template>
  <ListLayout>
    <template #actions>
      <div class="heading">
        <WTitle variant="heavy">Procedimento HS</WTitle>

        <WButton icon="icAdd" variant="cta" size="large" @click="onCreate">
          Cadastrar novo código
        </WButton>
      </div>
    </template>

    <template #list>
      <WListControllers
        has-pagination
        margin="none"
        has-items-per-page-select
        input-placeholder="Busque por código ou procedimento"
        :hide-input="false"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WPaginationNext="setNext"
        @WInputChange="searchChange"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
          </WSelect>
        </div>
      </WListControllers>

      <WTable style="width: fit-content">
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            v-for="(header, headerIndex) in headers"
            :width="header.width"
            :key="header.id"
            :left-sticky="headerIndex === 0"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>

        <WTableBody slot="body">
          <WTableRow v-if="isLoading">
            <div class="row-text">
              <WParagraph size="large">Carregando...</WParagraph>
            </div>
          </WTableRow>

          <WTableRow v-else-if="error">
            <div class="row-text">
              <WParagraph size="large">Erro ao carregar os dados</WParagraph>

              <WButton variant="secondary" size="small" @click="refetch">
                Tentar novamente
              </WButton>
            </div>
          </WTableRow>

          <WTableRow v-else-if="data?.length === 0">
            <div class="row-text">
              <WParagraph size="large">Não há códigos para exibir</WParagraph>
            </div>
          </WTableRow>

          <template v-else>
            <WTableRow v-for="item in data" :key="item.id">
              <WTableBodyCell :left-sticky="true" :width="columnWidthMap.aliceCode" size="medium">
                {{ item.aliceCode }}
              </WTableBodyCell>

              <WTableBodyCell
                style="word-break: break-all"
                :width="columnWidthMap.primaryTuss"
                size="medium"
              >
                {{ item.primaryTuss }}
              </WTableBodyCell>

              <WTableBodyCell
                :width="columnWidthMap.aliceDescription"
                size="medium"
                style="word-break: break-all"
              >
                {{ item.aliceDescription }}
              </WTableBodyCell>

              <WTableBodyCell
                :width="columnWidthMap.pricingStatus"
                size="medium"
                style="word-break: break-all"
              >
                <WTag
                  variant="primary"
                  :label="item.pricingStatus?.friendlyName || ''"
                  :color="item.pricingStatus?.color?.toLowerCase() || 'gray'"
                />
              </WTableBodyCell>

              <WTableBodyCell :width="columnWidthMap.status" size="medium">
                <WIcon
                  :icon="statusIcon[item.status as Status].icon"
                  :size="statusIcon[item.status as Status].size"
                />
              </WTableBodyCell>

              <WTableBodyCell :width="columnWidthMap.specialtiesCount" size="medium">
                <WParagraph size="medium" :label="item.specialtiesText">
                  {{ item.specialtiesText }}
                </WParagraph>
              </WTableBodyCell>

              <WTableBodyCell
                style="word-break: break-all"
                :width="columnWidthMap.serviceType"
                size="medium"
              >
                <WTag
                  variant="primary"
                  :label="item?.serviceType?.friendlyName || ''"
                  :color="item?.serviceType?.color?.toLowerCase() || 'gray'"
                />
              </WTableBodyCell>

              <WTableBodyCell :width="columnWidthMap.type" size="medium">
                <WTag
                  variant="primary"
                  :label="item?.type?.friendlyName || ''"
                  :color="item?.type?.color?.toLowerCase() || 'gray'"
                />
              </WTableBodyCell>

              <WTableBodyCell
                :width="columnWidthMap.action"
                :right-sticky="true"
                size="medium"
                align="center"
              >
                <div style="display: inline-block">
                  <WButton
                    :block="false"
                    icon-button
                    variant="secondary"
                    icon="icArrowRight"
                    @click="goToProcedure(item?.id)"
                  />
                </div>
              </WTableBodyCell>
            </WTableRow>
          </template>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<style lang="scss" scoped>
.heading {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.row-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-content: center;

  width: 100%;

  gap: var(--gl-spacing-05);
  padding: var(--gl-spacing-10);
}
</style>
