<script setup lang="ts">
import { CodeServiceType } from '@procedures/models/CodesConfig'

import { computed } from 'vue'

import { useSnackbar } from '@commons/hooks'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'

import { updateSpecialtiesAndStatus } from '@procedures/api/mutations'
import {
  getAllMedicalSpecialties,
  type GetProcedureResponse,
  type MedicalSpecialtyShortResponse
} from '@procedures/api/queries'

import { codesServices } from '@procedures/constants'
import { getFriendlyExecutionEnvironment, sortByAlphabeticalOrder } from '@procedures/utils'

import { WModal, WParagraph } from '@alice-health/wonderland-vue'

/*
 * Stores
 */

const store = useCreateFormStore()

/*
 * Hooks
 */

const route = useRoute()
const router = useRouter()
const queryClient = useQueryClient()

const { notify } = useSnackbar()

/*
 * Computeds
 */

const selectedTypeConfig = computed(() => {
  return codesServices.filter((code) => code.name === store?.form?.serviceType)[0] || {}
})

const id = computed(() => route?.params?.id as string)
const status = computed(() => store.getCurrentStatus()?.value)

/*
 * Requests & Mutations
 */

const { data, isLoading } = useQuery({
  queryKey: ['medicalSpecialties'],
  queryFn: () => getAllMedicalSpecialties()
})

const { mutate: updateCode } = useMutation({
  mutationKey: ['updateHealthSpecialistAliceCode', id.value, status.value, store.specialties],
  mutationFn: updateSpecialtiesAndStatus,
  onSuccess,
  onError
})

/*
 * Computeds
 */

const selectedMedicalSpecialties = computed(() => {
  return filterSelectedSpecialties(data?.value?.medicalSpecialties)
})

const selectedTherapeuticSpecialties = computed(() => {
  return filterSelectedSpecialties(data?.value?.therapySpecialties)
})

const isAllMedicalSpecialtiesSelected = computed(() => {
  return selectedMedicalSpecialties.value.length === data?.value?.medicalSpecialties?.length
})

const isAllTherapeuticSpecialtiesSelected = computed(() => {
  return (
    selectedTherapeuticSpecialties.value.length === data?.value?.therapySpecialties?.length &&
    data?.value?.therapySpecialties?.length > 1
  )
})

const closeOptions = {
  continueCallback: () =>
    updateCode({
      id: id.value,
      form: { status: status.value, medicalSpecialtyIds: store.specialties }
    })
}

/*
 * Methods
 */

async function onError() {
  notify({
    message: 'Erro ao cadastrar especialidades/ativar código',
    icon: 'icAlertCircleOutlined'
  })

  router.push({
    name: route?.params?.id ? 'procedures/resources/read' : 'procedures/resources/list',
    params: { id: route?.params?.id },
    query: route.query
  })
}

async function onSuccess(responseData: GetProcedureResponse) {
  if (responseData.id) {
    router.push({ params: { id: responseData.id } })
  }

  if (responseData.aliceCode) {
    store.setAliceCode(responseData.aliceCode)
  }

  await queryClient.invalidateQueries({ queryKey: ['procedures'] })

  router.push({
    name: 'procedures/resources/read',
    params: { id: route?.params?.id },
    query: route.query
  })
}

function filterSelectedSpecialties(codeSpecialties?: MedicalSpecialtyShortResponse[]) {
  if (!codeSpecialties) return []

  const sortedList = sortByAlphabeticalOrder(codeSpecialties, 'name')
  const filteredList = sortedList?.filter(({ id }) => store.specialties.includes(id))

  return filteredList
}
</script>

<template>
  <WModal
    id="my-modal"
    title="Revisão das informações"
    icon="icAlertCircleFilled"
    confirmLabel="Cadastrar e gerar código Alice"
    cancelLabel="Voltar para edição"
    :opened="store.showSummaryModal"
    @w-closed="store.handleCloseSummaryModal($event, closeOptions)"
  >
    <div class="confirmation-modal-content">
      <div>
        <WParagraph variant="heavy" size="large">Código TUSS primário</WParagraph>

        <WParagraph size="large" mode="secondary">{{ store?.form?.primaryTuss }}</WParagraph>
      </div>

      <div>
        <WParagraph variant="heavy" size="large">Quantidade de execuções</WParagraph>

        <WParagraph size="large" mode="secondary">{{ store?.form?.executionAmount }}</WParagraph>
      </div>

      <div v-if="store?.form?.secondaryResources?.length">
        <WParagraph variant="heavy" size="large">Código(s) TUSS secundário(s)</WParagraph>

        <div class="confirmation-modal-content__secondary-tuss__list">
          <div
            v-for="tussCode in store?.form?.secondaryResources"
            :key="tussCode.id"
            class="confirmation-modal-content__secondary-tuss__list__item"
          >
            <WParagraph size="large" mode="secondary">
              {{ tussCode?.code }} - {{ tussCode?.description }}
            </WParagraph>
          </div>
        </div>
      </div>

      <div>
        <WParagraph variant="heavy" size="large">Descrição do código Alice</WParagraph>

        <WParagraph size="large" mode="secondary">
          {{ store.form.aliceDescription }}
        </WParagraph>
      </div>

      <div>
        <WParagraph variant="heavy" size="large">Classificação</WParagraph>

        <WParagraph size="large" mode="secondary">{{ selectedTypeConfig?.label }}</WParagraph>
      </div>

      <div
        v-if="
          store?.form?.serviceType === CodeServiceType.PROCEDURE &&
          store?.form?.executionEnvironment
        "
      >
        <WParagraph variant="heavy" size="large">Ambiente de execução</WParagraph>

        <WParagraph size="large" mode="secondary">
          {{ getFriendlyExecutionEnvironment(store?.form?.executionEnvironment) }}
        </WParagraph>
      </div>

      <div class="confirmation-modal-content__box">
        <WParagraph variant="heavy" size="large">Especialidades clínicas e cirúrgicas</WParagraph>

        <WParagraph v-if="isLoading" size="large" mode="secondary">Carregando...</WParagraph>

        <WParagraph v-else-if="isAllMedicalSpecialtiesSelected" size="large" mode="secondary">
          Todas as especialidades
        </WParagraph>

        <WParagraph
          v-else-if="selectedMedicalSpecialties.length === 0"
          size="large"
          mode="secondary"
        >
          Nenhuma especialidade clínica e cirúrgica selecionada
        </WParagraph>

        <div v-else class="confirmation-modal-content__list">
          <WParagraph
            v-for="specialty in selectedMedicalSpecialties"
            :key="specialty.id"
            size="large"
            mode="secondary"
          >
            {{ specialty.name }}
          </WParagraph>
        </div>
      </div>

      <div class="confirmation-modal-content__box">
        <WParagraph variant="heavy" size="large">Especialidades terápicas</WParagraph>

        <WParagraph v-if="isLoading" size="large" mode="secondary">Carregando...</WParagraph>

        <WParagraph v-else-if="isAllTherapeuticSpecialtiesSelected" size="large" mode="secondary">
          Todas as terapias
        </WParagraph>

        <WParagraph
          v-else-if="selectedTherapeuticSpecialties.length === 0"
          size="large"
          mode="secondary"
        >
          Nenhuma terapia selecionada
        </WParagraph>

        <div v-else class="confirmation-modal-content__list">
          <WParagraph
            v-for="specialty in selectedTherapeuticSpecialties"
            :key="specialty.id"
            size="large"
            mode="secondary"
          >
            {{ specialty.name }}
          </WParagraph>
        </div>
      </div>

      <div>
        <WParagraph variant="heavy" size="large">Ativar código (exibir no prontuário)</WParagraph>

        <WParagraph size="large" mode="secondary">
          {{ store.getCurrentStatus()?.label }}
        </WParagraph>
      </div>
    </div>
  </WModal>
</template>

<style lang="scss" scoped>
.confirmation-modal-content {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-08);
  padding-right: var(--gl-spacing-08);
  width: 540px;

  &__box {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-02);
  }

  &__list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: var(--gl-spacing-02);
    column-gap: var(--gl-spacing-02);
    max-width: 500px;
  }

  &__secondary-tuss {
    &__list__item {
      padding: var(--gl-spacing-02) 0;
      border-bottom: var(--gl-border-width-xs) solid var(--sys-color-stroke-active);

      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
