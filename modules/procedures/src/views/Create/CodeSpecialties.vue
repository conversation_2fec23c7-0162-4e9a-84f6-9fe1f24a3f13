<script setup lang="ts">
import { HealthSpecialistCodesForm } from '@procedures/schemas/ProcedureForm'

import { computed, ref } from 'vue'

import { useValidation } from '@alice-health/vue-hooks'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'
import { useQuery } from '@tanstack/vue-query'

import {
  getAllMedicalSpecialties,
  getResourceBundleMedicalSpecialties
} from '@procedures/api/queries'

import { WButton, WParagraph, WSwitch } from '@alice-health/wonderland-vue'

import SpecialtiesSelectionList from '@procedures/components/SpecialtiesSelectionList.vue'
import { useRoute } from 'vue-router'
import CodeSummary from '@procedures/views/Create/CodeSummary.vue'

/*
 * Hooks
 */

const route = useRoute()
const store = useCreateFormStore()

const { getErrors, hasError, validate } = useValidation({
  formSchema: HealthSpecialistCodesForm,
  form: computed(() => store.form)
})

/*
 * Computeds
 */

const hasId = computed(() => !!route?.params?.id)

/*
 * Requests & Mutations
 */

const { isLoading: isLoadingCodeSelectedMedicalSpecialties } = useQuery({
  queryKey: ['codeSelectedMedicalSpecialties', route?.params?.id],
  queryFn: () => getResourceBundleMedicalSpecialties(route?.params?.id as string, {}),
  enabled: hasId,
  refetchOnMount: true,
  select({ results }) {
    store.handleInitializeCodeSpecialties(results)
  }
})

const { data, isLoading } = useQuery({
  queryKey: ['medicalSpecialties'],
  queryFn: getAllMedicalSpecialties
})

const activeCode = ref(store.form?.status === 'ACTIVE')
const showValidationErrors = ref(false)

/*
 * Computeds
 */

const disabledContinueButton = computed(() => {
  return !store.specialties.length
})

const loading = computed(() => {
  return isLoadingCodeSelectedMedicalSpecialties.value || isLoading.value
})

/*
 * Methods
 */

const updateStatus = (e: CustomEvent) => {
  const isActive = e.detail

  store.setActiveStatus(isActive ? 'ACTIVE' : 'INACTIVE')

  showValidationErrors.value = true
}

const shouldShowError = (field: string) => {
  return showValidationErrors.value && hasError(field)
}

const getErrorText = (field: string) => {
  return showValidationErrors.value ? getErrors(field) : ''
}
</script>

<template>
  <div class="code-specialties">
    <div class="code-specialties__list">
      <SpecialtiesSelectionList
        title="Clínicas e cirúrgicas"
        :loading="loading"
        :specialties="data?.medicalSpecialties"
        :all="{ label: 'Todas as especialidades', value: 'allSpecialties' }"
      />

      <SpecialtiesSelectionList
        title="Terapias"
        :loading="loading"
        :specialties="data?.therapySpecialties"
        :all="{ label: 'Todas as terapias', value: 'allTherapies' }"
      />
    </div>

    <div class="code-specialties__footer">
      <div class="code-specialties__footer-activation">
        <div class="code-specialties__footer-activation__title">
          <WParagraph size="large">Ativar código</WParagraph>

          <WParagraph class="code-specialties__footer-subtitle" variant="plain" mode="secondary">
            Essa opção permite exibir ou ocultar o código para o especialista, mas ele só será
            exibido após a precificação.
          </WParagraph>
        </div>

        <WSwitch
          size="small"
          aria-label="Ativar código"
          v-model="activeCode"
          :invalid="shouldShowError('status')"
          :errorText="getErrorText('status')"
          @WChange="updateStatus"
          @WBlur="validate('status')"
        />
      </div>

      <div class="code-specialties__actions">
        <WButton
          size="large"
          variant="cta"
          :disabled="disabledContinueButton"
          @click="store.nextStep"
        >
          Continuar
        </WButton>
      </div>
    </div>
  </div>

  <CodeSummary />
</template>

<style lang="scss" scoped>
.code-specialties {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-10);

  max-width: 600px;

  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-08);
  }

  &__footer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    gap: var(--gl-spacing-10);

    &-activation {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      &-title {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        width: 100%;
      }

      &-subtitle {
        max-width: 488px;
      }
    }

    &__actions {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
    }
  }
}
</style>
