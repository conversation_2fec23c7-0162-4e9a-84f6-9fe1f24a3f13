<script lang="ts" setup>
import type { TussCode } from '../../models'

import { ref, watch, computed } from 'vue'

import { useRoute, useRouter } from 'vue-router'
import { useSuggestions } from '@procedures/hooks'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { useDebounce, useValidation } from '@alice-health/vue-hooks'

import {
  HealthSpecialistCodesForm,
  type SecondaryResource
} from '@procedures/schemas/ProcedureForm'

import { getTussCodes } from '@procedures/api/queries'

import { getProcedure, type GetProcedureResponse } from '@procedures/api/queries'

import { codesServices } from '@procedures/constants'
import { getFriendlyExecutionEnvironment } from '@procedures/utils'
import { CodeServiceType, ExecutionEnvironment } from '@procedures/models/CodesConfig'

import {
  NumericInput,
  LabelWithTooltip,
  SecondaryTussCode,
  SuggestionTag
} from '@procedures/components'
import {
  WAutocomplete,
  WTextfield,
  WButton,
  WParagraph,
  WModal,
  WLabel,
  WRadio,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WTooltip
} from '@alice-health/wonderland-vue'
import {
  createHealthSpecialistAliceCode,
  updateHealthSpecialistAliceCode
} from '@procedures/api/mutations'
import { useSnackbar } from '@commons/hooks'

/**
 * Constants
 */
const columnWidthMap = {
  code: '100px',
  description: '',
  buttons: '100px'
}

const headers = [
  { id: 1, title: 'TUSS', width: columnWidthMap.code },
  { id: 2, title: 'Descrição', width: columnWidthMap.description },
  { id: 3, width: columnWidthMap.buttons }
]

/*
 * Hooks
 */

const store = useCreateFormStore()

const tussCodeField = ref(store?.form?.primaryTuss)
const searchTussCodeDebouced = useDebounce(tussCodeField, 800)

const { getErrors, hasError, validate, validateAll } = useValidation({
  formSchema: HealthSpecialistCodesForm,
  form: computed(() => store.form)
})

/**
 * Refs
 */

const tussCodeSuggestions = ref<TussCode[]>([])
const visibleRemoveTussModal = ref(false)
const visibleExitConfirmationModal = ref(false)
const exitAction = ref<'back' | 'cancel' | null>(null)
const secondaryResourceInvalid = ref(false)
const selectedTussCodeProcedure = ref<TussCode | null>(null)
const changedSomethingWhileEditing = ref(false)
const copyText = ref('Copiar descrição')

const showValidationErrors = ref(false)

/**
 * Computed
 */

const getTussCodeFilterParam = computed(() => {
  return {
    filter: `{"q": "${searchTussCodeDebouced.value}"}`
  }
})

const shouldDisableContinueButton = computed(() => {
  if (route?.params?.id) {
    return false
  } else {
    const schema = validateAll()

    return !schema.success || !!secondaryResourceInvalid.value
  }
})

const handleDescriptionChange = () => {
  changedSomethingWhileEditing.value = true
  showValidationErrors.value = true
}

const selectedTypeConfig = computed(() => {
  return codesServices.filter((code) => code.name === store?.form?.serviceType)[0] || {}
})

/**
 * Hooks
 */

const route = useRoute()
const router = useRouter()
const queryClient = useQueryClient()

const { notify } = useSnackbar()

/*
 * Computeds
 */

const form = computed(() => store.form)
const hasId = computed(() => !!route?.params?.id)

/*
 * Requests & Mutations
 */

const { data: fetchedTussCodes } = useQuery({
  queryKey: ['tussCodes', getTussCodeFilterParam],
  queryFn: () => getTussCodes(getTussCodeFilterParam.value)
})

const { data: healthSpecialistResourceBundle } = useQuery({
  queryKey: ['procedureId', route?.params?.id],
  queryFn: () => getProcedure(route?.params?.id as string),
  enabled: hasId,
  select(data) {
    store.updateFormValue(data)

    return data
  }
})

const { mutate: save, status: saveStatus } = useMutation({
  mutationKey: ['createHealthSpecialistAliceCode'],
  mutationFn: () => createHealthSpecialistAliceCode(store?.form),
  onSuccess,
  onError
})

const { mutate: update, status: updateStatus } = useMutation({
  mutationKey: ['updateHealthSpecialistAliceCode'],
  mutationFn: () => updateHealthSpecialistAliceCode(route.params.id as string, store?.form),
  onSuccess,
  onError
})

/*
 * Computeds
 */

const isSavingOrUpdating = computed(() => {
  return saveStatus.value === 'pending' || updateStatus.value === 'pending'
})

/**
 * Methods
 */

function handleContinue() {
  if (route?.params?.id && changedSomethingWhileEditing.value) {
    update()
  } else {
    save()
  }
}

async function onError() {
  notify({ message: 'Erro ao cadastrar código', icon: 'icAlertCircleOutlined' })
}

async function onSuccess(responseData: GetProcedureResponse) {
  store.updateFormValue(responseData)

  await queryClient.invalidateQueries({ queryKey: ['procedures'] })

  store.nextStep()

  router.push({
    name: 'procedures/resources/edit/code-specialties',
    params: { id: responseData.id },
    query: route.query
  })
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    copyText.value = 'Descrição copiada!'
    setTimeout(() => (copyText.value = 'Copiar descrição'), 3000)
  } catch {
    copyText.value = 'Houve algum erro!'
  }
}

const removePrimaryTuss = () => {
  if (store?.form?.secondaryResources?.length > 0 || store?.form?.aliceDescription) {
    visibleRemoveTussModal.value = true
  } else {
    clearForm()
  }
}

const handleExit = (e: CustomEvent) => {
  if (e.detail === 'confirm') {
    const routeParams = route?.params?.id
      ? { name: 'procedures/resources/list' }
      : { name: 'procedures/resources/create' }

    router.push(routeParams)
  }
  visibleExitConfirmationModal.value = false
  exitAction.value = null
}

const handleRemoveTuss = (e: CustomEvent) => {
  if (e.detail === 'confirm') {
    clearForm()
  }
  visibleRemoveTussModal.value = false
}

const clearForm = () => {
  store.resetForm()

  tussCodeField.value = ''
}

const shouldShowError = (field: string) => {
  return showValidationErrors.value && hasError(field)
}

const getErrorText = (field: string) => {
  return showValidationErrors.value ? getErrors(field) : ''
}

const addSecondaryResource = (resource: SecondaryResource) => {
  secondaryResourceInvalid.value =
    store?.form?.secondaryResources.length === 0 && store?.form?.primaryTuss === resource.code

  if (store?.form?.primaryTuss === resource.code) {
    return
  }

  setSecondaryTussCode([...store.form.secondaryResources, resource])
}

const setSecondaryTussCode = (tussCodeArr: SecondaryResource[]) => {
  store.form.secondaryResources = tussCodeArr
}

const updateSelectedTussCode = (event: CustomEvent) => {
  store.form.primaryTuss = event?.detail.code
  store.form.tussDescription = event?.detail.description
}

const updateQuantity = (quantity: number) => {
  store.form.executionAmount = quantity
  showValidationErrors.value = true
}

const updateTussCodeSearchKey = (event: CustomEvent) => {
  tussCodeField.value = event?.detail
  showValidationErrors.value = true
}

const searchTussCodeChange = async (newValue: string) => {
  tussCodeField.value = searchTussCodeDebouced.value

  if (newValue === '') {
    store.form.tussDescription = ''
    tussCodeSuggestions.value = []

    return
  }

  tussCodeSuggestions.value =
    fetchedTussCodes?.value?.map((tussCode) => ({
      ...tussCode,
      searchDescription: `${tussCode.code} - ${tussCode.description}`
    })) || []
}

const handleSuggestionTagClick = (suggestion: string) => {
  const description = store?.form?.aliceDescription
    ? `${store?.form?.aliceDescription} ${suggestion}`
    : suggestion

  store.form.aliceDescription =
    description.charAt(0).toUpperCase() + description.slice(1).toLowerCase()
}

const suggestions = computed(() =>
  useSuggestions(
    [
      {
        validator: store?.form?.tussDescription !== '',
        value: store?.form?.tussDescription as string,
        order: 2
      },
      {
        validator:
          store?.form?.serviceType !== 'CONSULTATION' && store?.form?.executionAmount === 2,
        value: 'Bilateral',
        order: 3
      },
      {
        validator: store?.form?.executionEnvironment !== ExecutionEnvironment.DOES_NOT_APPLY,
        value: getFriendlyExecutionEnvironment(store?.form?.executionEnvironment as string),
        order: 3
      },
      {
        validator:
          store?.form?.secondaryResources.length > 0 ||
          (store?.form?.serviceType !== 'CONSULTATION' && store?.form?.executionAmount > 1),
        value: 'Pacote',
        order: 1
      },
      {
        validator: store?.form?.serviceType === 'CONSULTATION',
        value: 'Consulta',
        order: 1
      },
      {
        validator: store?.form?.serviceType === 'CONSULTATION' && store?.form?.executionAmount > 1,
        value: `${Number(store?.form?.executionAmount)} horas`,
        order: 3
      }
    ],
    ['de', ' - ', 'para']
  )
)

/*
 * Watchers
 */

watch(searchTussCodeDebouced, searchTussCodeChange)

watch(
  healthSpecialistResourceBundle,
  (newValue) => {
    if (newValue) store.updateFormValue(newValue)

    tussCodeField.value = newValue?.primaryTuss || ''

    selectedTussCodeProcedure.value = {
      id: '',
      tableType: '',
      code: newValue?.primaryTuss || '',
      tussCode: newValue?.primaryTuss || '',
      description: newValue?.tussDescription || ''
    }
  },
  { immediate: true }
)

watch(fetchedTussCodes, (newValue) => {
  if (route?.params?.id && newValue) {
    store.setTussDescription(newValue[0].description)
  }
})
</script>

<template>
  <div class="code-form">
    <WAutocomplete
      v-if="!hasId"
      label="Código TUSS primário"
      leadingIcon="icSearch"
      searchByKey="searchDescription"
      placeholder="Digite o código"
      :value="tussCodeField"
      :items="tussCodeSuggestions"
      :invalid="shouldShowError('primaryTuss')"
      :disabled="form?.secondaryResources && form?.secondaryResources.length > 0"
      :errorText="getErrorText('primaryTuss')"
      @wChange="updateTussCodeSearchKey"
      @wSelect="updateSelectedTussCode"
      @WBlur="validate('primaryTuss')"
    />

    <WTable v-if="store?.form?.tussDescription || store?.form?.aliceDescription">
      <WTableHeader slot="header">
        <WTableHeaderCell
          size="large"
          v-for="header in headers"
          :width="header.width"
          :key="header.id"
        >
          {{ header?.title }}
        </WTableHeaderCell>
      </WTableHeader>

      <WTableBody slot="body">
        <WTableRow>
          <WTableBodyCell :width="columnWidthMap.code">
            <WParagraph mode="tertiary" size="large">
              {{ store?.form?.primaryTuss }}
            </WParagraph>
          </WTableBodyCell>

          <WTableBodyCell style="word-break: break-all" :width="columnWidthMap?.description">
            <WParagraph size="large">
              {{ store?.form?.tussDescription ?? store?.form?.aliceDescription }}
            </WParagraph>
          </WTableBodyCell>

          <WTableBodyCell :width="columnWidthMap?.buttons" size="medium" align="center">
            <div style="display: flex; gap: var(--gl-spacing-02)">
              <WTooltip :label="copyText" position="top">
                <WButton
                  size="small"
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icCopy"
                  @click="
                    copyToClipboard(store?.form?.tussDescription ?? store?.form?.aliceDescription)
                  "
                />
              </WTooltip>

              <WButton
                v-if="!hasId"
                size="small"
                :block="false"
                icon-button
                variant="secondary"
                icon="icTrash"
                @click="removePrimaryTuss"
              />
            </div>
          </WTableBodyCell>
        </WTableRow>
      </WTableBody>
    </WTable>

    <div>
      <LabelWithTooltip
        label="Quantidade de execuções"
        :helperText="selectedTypeConfig?.helperText ?? ''"
      />

      <NumericInput
        :quantity="store?.form?.executionAmount"
        @change="updateQuantity"
        @WBlur="validate('executionAmount')"
        :maximum="selectedTypeConfig?.maxAmount"
        :minimum="selectedTypeConfig?.minAmount"
        :disabled="
          (store?.form?.secondaryResources && store?.form?.secondaryResources.length > 0) || hasId
        "
      />
    </div>

    <SecondaryTussCode
      v-if="selectedTypeConfig?.hasSecondaryTuss && store?.form?.executionAmount === 1"
      @setSecondaryTussCode="setSecondaryTussCode"
      @addSecondaryResource="(selectedTuss) => addSecondaryResource(selectedTuss)"
      @clearAutocompleteInput="secondaryResourceInvalid = false"
    />

    <div
      class="code-form__execution-environment"
      v-if="store?.form?.serviceType === CodeServiceType.PROCEDURE"
    >
      <WLabel size="large">Ambiente de execução do procedimento</WLabel>

      <div class="code-form__execution-environment-options">
        <WRadio
          name="executionEnvironment"
          v-model="store.form.executionEnvironment"
          value="OUTPATIENT"
          label="Ambulatorial"
          :disabled="hasId"
        />

        <WRadio
          name="executionEnvironment"
          v-model="store.form.executionEnvironment"
          value="SURGICAL"
          label="Hospitalar / Cirúrgico"
          :disabled="hasId"
        />
      </div>
    </div>

    <div>
      <LabelWithTooltip
        label="Descrição do código Alice"
        helperText="Insira o nome do procedimento e as características da composição, sempre indicando se é uni ou bilateral, se possui composição por quantidade de horas ou outras características."
      />

      <WTextfield
        v-model="store.form.aliceDescription"
        name="aliceDescription"
        placeholder="Digite a descrição"
        :invalid="shouldShowError('aliceDescription')"
        :errorText="getErrorText('aliceDescription')"
        :onW-change="handleDescriptionChange"
        @WBlur="validate('aliceDescription')"
      />

      <div v-if="!hasId && store?.form?.tussDescription" class="code-form__suggestions">
        <SuggestionTag
          v-for="suggestion in suggestions"
          :key="suggestion"
          :label="suggestion"
          @click="handleSuggestionTagClick"
        />
      </div>
    </div>

    <div class="code-form__actions">
      <WButton
        size="large"
        variant="cta"
        :loading="isSavingOrUpdating"
        :disabled="shouldDisableContinueButton"
        @click="handleContinue"
      >
        Continuar
      </WButton>
    </div>
  </div>

  <WModal
    id="remove-tuss-modal"
    title="Deseja remover o tuss primário?"
    icon="icAlertCircleFilled"
    confirmLabel="Sim, remover"
    cancelLabel="Continuar editando"
    :opened="visibleRemoveTussModal"
    @WClosed="handleRemoveTuss"
  >
    <div class="confirmation-modal-content">
      <WParagraph size="large">
        Ao clicar em 'Sim', todas as informações preenchidas serão apagadas, inclusive os demais
        campos.
      </WParagraph>
    </div>
  </WModal>

  <WModal
    id="exit-confirmation-modal"
    :title="exitAction === 'back' ? 'Deseja mesmo sair?' : 'Deseja mesmo cancelar?'"
    icon="icAlertCircleFilled"
    :confirmLabel="exitAction === 'back' ? 'Sim, sair' : 'Sim, cancelar'"
    cancelLabel="Continuar editando"
    :opened="visibleExitConfirmationModal"
    @WClosed="handleExit"
  >
    <div class="confirmation-modal-content">
      <WParagraph size="large">Clicando em sim, os dados editados não serão salvos.</WParagraph>
    </div>
  </WModal>
</template>

<style lang="scss" scoped>
.code-form {
  display: flex;
  flex-direction: column;

  max-width: 600px;
  gap: var(--gl-spacing-06);

  display: flex;
  flex-direction: column;

  &__label--tooltip {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--gl-spacing-02);
  }

  &__execution-environment {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-05);
    margin-bottom: var(--gl-spacing-06);

    &-options {
      display: flex;
      flex-direction: row;
      gap: var(--gl-spacing-06);
    }
  }

  &__suggestions {
    display: flex;
    flex-wrap: wrap;
    margin-top: var(--gl-spacing-02);
    gap: var(--gl-spacing-02);
    width: 100%;
  }

  &-actions {
    display: flex;
    flex-direction: row;
    gap: var(--gl-spacing-02);
  }
}
</style>
