<script setup lang="ts">
import type { CodeServiceConfig, CodeServiceType } from '@procedures/models/CodesConfig'

import { computed, onMounted } from 'vue'
import { useCreateFormStore } from '@procedures/store/useCreateFormStore'
import { useRoute, useRouter } from 'vue-router'

import { codesServices } from '@procedures/constants'

import { WSegmentedButtonGroup, WSegmentedButton } from '@alice-health/wonderland-vue'

const store = useCreateFormStore()

const route = useRoute()
const router = useRouter()

const procedureTypeOptions = computed(() => codesServices.sort(compare))

function compare(a: CodeServiceConfig, b: CodeServiceConfig) {
  if (a.order < b.order) return -1
  if (a.order > b.order) return 1

  return 0
}

const handleSelection = (e: CustomEvent) => {
  store.setServiceType(e.detail as CodeServiceType)

  router.push({ query: { ...route.query, serviceType: e.detail } })

  store.nextStep()
}

onMounted(() => {
  store.resetStore()
})
</script>

<template>
  <div class="type-selection">
    <WSegmentedButtonGroup direction="vertical" :onW-change="handleSelection">
      <WSegmentedButton
        v-for="(procedureType, procedureTypeIndex) in procedureTypeOptions"
        :key="procedureTypeIndex"
        :value="procedureType.name"
        :selected="procedureType.name === store.form.serviceType"
        :disabled="procedureType.disabled"
      >
        {{ procedureType.label }}
      </WSegmentedButton>
    </WSegmentedButtonGroup>
  </div>
</template>

<style lang="scss" scoped>
.type-selection {
  max-width: 300px;
}
</style>
