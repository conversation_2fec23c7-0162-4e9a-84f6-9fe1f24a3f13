import { string, object, z } from 'zod'

import { codesServices } from '@procedures/constants/hs-code-types'
import { CodeServiceType, ExecutionEnvironment } from '@procedures/models/CodesConfig'

export const PricingStatusEnum = z.enum(['PRICED', 'PENDING'])
export const ProcedureTypeEnum = z.enum(['PROCEDURE', 'EXAM', 'CONSULTATION'])
export const ProcedureStatusEnum = z.enum(['ACTIVE', 'INACTIVE'])

const secondaryResource = object({
  id: string(),
  code: string(),
  description: string(),
  tableType: string()
})

export type SecondaryResource = z.infer<typeof secondaryResource>

export const HealthSpecialistCodesForm = object({
  id: string().optional().nullable(),
  aliceCode: string().optional().nullable(),
  primaryTuss: string().min(1),
  tussDescription: string().min(1).optional(),
  aliceDescription: string().min(1),
  executionAmount: z.number().int().positive(),
  serviceType: z.nativeEnum(CodeServiceType).optional(),
  status: string().optional(),
  type: ProcedureTypeEnum.optional(),
  pricingStatus: PricingStatusEnum.optional(),
  specialtiesCount: z.number().int().optional(),
  executionEnvironment: z
    .nativeEnum(ExecutionEnvironment)
    .default(ExecutionEnvironment.DOES_NOT_APPLY)
    .optional(),
  secondaryResources: secondaryResource.array().optional().default([])
}).superRefine((data, ctx) => {
  const { serviceType, executionAmount, secondaryResources, primaryTuss } = data
  const currentCodeService = codesServices.find((service) => service.name === serviceType)

  // Regras condicionais para `executionAmount` com base em `serviceType`
  if (serviceType === CodeServiceType.CONSULTATION && executionAmount > 14) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['executionAmount'],
      message: 'Para consultas, a quantidade máxima de execuções é 14.'
    })
  } else if (serviceType !== CodeServiceType.CONSULTATION && executionAmount > 5) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['executionAmount'],
      message: `Para ${serviceType}, o valor máximo de executionAmount é 5.`
    })
  }

  // `secondaryTussCode` não deve estar preenchido se a quantidade de execuções for maior que 1
  if (currentCodeService?.hasSecondaryTuss && executionAmount > 1 && secondaryResources?.length) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['secondaryTussCode', 'executionAmount'],
      message: 'O TUSS secundário não deve ser preenchido para execuções maiores que 1.'
    })
  }

  // `secondaryTussCode` não deve estar preenchido com o mesmo valor que `primaryTussCode`

  const primaryTussInSecondaryTussList = secondaryResources?.filter(
    (secondaryResource) => secondaryResource.code === primaryTuss
  )

  if (currentCodeService?.hasSecondaryTuss && primaryTussInSecondaryTussList?.length) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['secondaryTussCode', 'tussCode'],
      message: 'O TUSS secundário deve ser diferente do primário.'
    })
  }

  // `executionEnvironment` deve ser preenchido se `serviceType` for `Procedimento`
  if (
    serviceType === CodeServiceType.PROCEDURE &&
    (!data.executionEnvironment ||
      data.executionEnvironment === ExecutionEnvironment.DOES_NOT_APPLY)
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['executionEnvironment'],
      message: 'O ambiente de execução é obrigatório para procedimentos.'
    })
  }
})
