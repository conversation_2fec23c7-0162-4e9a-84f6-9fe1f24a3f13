export enum CodeServiceType {
  CONSULTATION = 'CONSULTATION',
  EXAM = 'EXAM',
  PROCEDURE = 'PROCEDURE'
}

export enum ExecutionEnvironment {
  DOES_NOT_APPLY = 'DOES_NOT_APPLY',
  SURGICAL = 'SURGICAL',
  OUTPATIENT = 'OUTPATIENT'
}

export type CodeAttributeFriendlyDescription = {
  name: string
  label: string
}

export interface CodeServiceConfig extends CodeAttributeFriendlyDescription {
  order: number
  helperText: string
  minAmount: number
  maxAmount: number
  disabled?: boolean
  hasSecondaryTuss?: boolean
}
