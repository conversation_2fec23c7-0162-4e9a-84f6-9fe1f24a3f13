import { z } from 'zod'

import { HealthSpecialistCodesForm } from '@procedures/schemas/ProcedureForm'
import type { FriendlyEnum } from '@commons/types'

const HealthSpecialistCodesKeys = HealthSpecialistCodesForm._def.schema.shape

export type HealthSpecialistCodes = z.infer<typeof HealthSpecialistCodesForm>
export type HealthSpecialistCodesFields = keyof typeof HealthSpecialistCodesKeys

export type HealthSpecialistCodesList = {
  id: string
  aliceCode: string
  primaryTuss: string
  aliceDescription: string
  type: FriendlyEnum
  serviceType: FriendlyEnum
  status: string
  pricingStatus: FriendlyEnum
  specialtiesText: string
}
