import type { GetProcedureResponse, MedicalSpecialty } from '@procedures/api/queries'
import type { HealthSpecialistCodes } from '@procedures/models'

import { CodeServiceType, ExecutionEnvironment } from '@procedures/models/CodesConfig'
import { ProcedureStatusEnum } from '@procedures/schemas/ProcedureForm'

import { defineStore } from 'pinia'
import { computed, reactive, ref, watch } from 'vue'

import { useRoute, useRouter, type RouteLocationRaw } from 'vue-router'

type Form = Omit<HealthSpecialistCodes, 'id'>

type CloseModalCallbacks = {
  finishCallback?: () => void
  continueCallback?: () => void
}

const formInitialState: Form = {
  aliceCode: '',
  primaryTuss: '',
  aliceDescription: '',
  tussDescription: '',
  executionAmount: 1,
  specialtiesCount: 0,
  secondaryResources: [],
  type: undefined,
  status: ProcedureStatusEnum.enum.INACTIVE,
  serviceType: undefined,
  pricingStatus: undefined,
  executionEnvironment: ExecutionEnvironment.DOES_NOT_APPLY
}

export const useCreateFormStore = defineStore('createProcedureStore', () => {
  /*
   * Custom hooks
   */

  const route = useRoute()
  const router = useRouter()

  /*
   * Computeds
   */

  const isEditMode = computed(() => !!id.value)
  const steps = computed(() => {
    const steps = [
      {
        title: 'Selecione o tipo do código que deseja cadastrar',
        route: 'procedures/resources/create/selection-type'
      },
      {
        title: 'Defina as características do código',
        route: isEditMode.value
          ? 'procedures/resources/edit/code-form'
          : 'procedures/resources/create/code-form'
      },
      {
        title: 'Escolha as especialidades que utilizarão o código',
        route: 'procedures/resources/edit/code-specialties'
      }
    ]

    return isEditMode.value ? steps.slice(1) : steps
  })
  const totalSteps = computed(() => steps.value.length)
  const backRouteName = computed(() =>
    isEditMode.value ? 'procedures/resources/read' : 'procedures/resources/list'
  )
  const pageStepTitle = computed(() => `Etapa ${currentStep.value}/${totalSteps.value}`)
  const currentViewTitle = computed(() => {
    return steps.value[currentStep.value - 1]?.title
  })

  /*
   * Refs & Reactives
   */

  const id = ref(route?.params?.id as string)
  const currentStep = ref(1)
  const specialties = ref<string[]>([])
  const showSummaryModal = ref(false)

  const form = reactive<Form>({
    ...formInitialState
  })

  /*
   * Methods
   */

  function getCurrentStatus() {
    const label = form.status === 'ACTIVE' ? 'Ativado' : 'Desativado'

    return {
      label,
      value: form.status
    }
  }

  function setId(value: string) {
    id.value = value
  }

  function setShowSummaryModal(value: boolean) {
    showSummaryModal.value = value
  }

  function setPrimaryTuss(value: string) {
    form.primaryTuss = value
  }

  function setTussDescription(value: string) {
    form.tussDescription = value
  }

  function setActiveStatus(value: string) {
    form.status = value
  }

  function setAliceCode(value: string) {
    form.aliceCode = value
  }

  function selectAllSpecialties(newSpecialties: string[]) {
    console.log('caiu em selectAllSpecialties')

    specialties.value = newSpecialties
  }

  function unselectAllSpecialties() {
    console.log('caiu em unselectAllSpecialties')

    specialties.value = []
  }

  function handleSelectSpecialty(id: string) {
    const selectedSpecialty = specialties.value?.find((s) => s === id)

    if (selectedSpecialty) {
      specialties.value = specialties.value.filter((s) => s !== id)
    } else {
      specialties.value.push(id)
    }
  }

  function handleInitializeCodeSpecialties(specialtiesData: MedicalSpecialty[]) {
    const specialtiesCopy = [...specialties.value]

    const specialtiesFromApi = specialtiesData
      .filter((x) => !!x.medicalSpecialtyId)
      .map((y) => y.medicalSpecialtyId)

    specialties.value = filterDuplicatedSpecialties([...specialtiesCopy, ...specialtiesFromApi])
  }

  function filterDuplicatedSpecialties(specialties: string[]) {
    return specialties.filter((s, index, self) => self.indexOf(s) === index)
  }

  function resetStore() {
    specialties.value = []
    currentStep.value = 1

    resetForm()
  }

  function resetForm() {
    Object.assign(form, formInitialState)
  }

  function updateFormValue(data: GetProcedureResponse) {
    Object.assign(form, data)
  }

  function setFormField<K extends keyof Form>(field: K, value: Form[K]) {
    form[field] = value
  }

  function setServiceType(value: CodeServiceType) {
    form.serviceType = value
  }

  function nextStep() {
    if (currentStep.value >= totalSteps.value) {
      setShowSummaryModal(true)
      return
    }

    currentStep.value += 1
  }

  async function prevStep() {
    if (currentStep.value <= 1) {
      await router?.push({
        name: backRouteName.value,
        params: isEditMode.value ? { id: route?.params?.id } : {},
        query: route?.query
      })

      return resetStore()
    }

    currentStep.value -= 1
  }

  function navigateByStep(step: number, id?: string) {
    const routeName = steps.value[step - 1].route

    const routeProps: RouteLocationRaw = {
      name: routeName,
      params: { id: id ?? route?.params?.id },
      query: route?.query
    }

    router.push(routeProps)
  }

  async function handleCloseSummaryModal(
    { detail }: CustomEvent,
    { finishCallback, continueCallback }: CloseModalCallbacks
  ) {
    if (detail === 'confirm') {
      continueCallback?.()

      if (route?.params?.id) {
        await router?.push({
          name: 'procedures/resources/read',
          params: { id: route?.params?.id },
          query: route.query
        })
      } else {
        await router?.push({
          name: 'procedures/resources/list',
          query: route.query
        })
      }

      resetStore()
    }

    finishCallback?.()

    setShowSummaryModal(false)
  }

  /*
   * Watchers
   */

  watch(currentStep, (step) => {
    navigateByStep(step)
  })

  /*
   * Return Resources
   */

  return {
    form,
    isEditMode,
    totalSteps,
    currentStep,
    specialties,
    pageStepTitle,
    currentViewTitle,
    showSummaryModal,
    setId,
    nextStep,
    prevStep,
    resetForm,
    resetStore,
    setAliceCode,
    setFormField,
    setServiceType,
    setPrimaryTuss,
    navigateByStep,
    setActiveStatus,
    updateFormValue,
    getCurrentStatus,
    setTussDescription,
    setShowSummaryModal,
    handleSelectSpecialty,
    handleCloseSummaryModal,
    handleInitializeCodeSpecialties,
    selectAllSpecialties,
    unselectAllSpecialties
  }
})
