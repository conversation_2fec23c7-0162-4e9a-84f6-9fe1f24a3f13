import { createP<PERSON>, setActive<PERSON>inia } from 'pinia'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { useCreateFormStore } from '../useCreateFormStore'
import { ProcedureStatusEnum, PricingStatusEnum } from '@procedures/schemas/ProcedureForm'
import { CodeServiceType, ExecutionEnvironment } from '@procedures/models/CodesConfig'
import type { MedicalSpecialty } from '@procedures/api/queries'
import { useRouter } from 'vue-router'
import type { Router } from 'vue-router'

// Mock vue-router
vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => ({
    params: {},
    query: {}
  })),
  useRouter: vi.fn(() => ({
    push: vi.fn()
  }))
}))

describe('useCreateFormStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('Store Initialization', () => {
    it('should initialize with default values', () => {
      const store = useCreateFormStore()

      expect(store.form).toEqual({
        aliceCode: '',
        primaryTuss: '',
        aliceDescription: '',
        tussDescription: '',
        executionAmount: 1,
        specialtiesCount: 0,
        secondaryResources: [],
        type: undefined,
        status: ProcedureStatusEnum.enum.INACTIVE,
        serviceType: undefined,
        pricingStatus: undefined,
        executionEnvironment: ExecutionEnvironment.DOES_NOT_APPLY
      })

      expect(store.currentStep).toBe(1)
      expect(store.specialties).toEqual([])
      expect(store.showSummaryModal).toBe(false)
      expect(store.isEditMode).toBe(false)
    })
  })

  describe('Form Methods', () => {
    it('should update form values correctly', () => {
      const store = useCreateFormStore()

      store.setAliceCode('TEST123')
      store.setPrimaryTuss('TUSS123')
      store.setTussDescription('Test Description')
      store.setActiveStatus(ProcedureStatusEnum.enum.ACTIVE)
      store.setServiceType(CodeServiceType.CONSULTATION)

      expect(store.form.aliceCode).toBe('TEST123')
      expect(store.form.primaryTuss).toBe('TUSS123')
      expect(store.form.tussDescription).toBe('Test Description')
      expect(store.form.status).toBe(ProcedureStatusEnum.enum.ACTIVE)
      expect(store.form.serviceType).toBe(CodeServiceType.CONSULTATION)
    })

    it('should reset form to initial state', () => {
      const store = useCreateFormStore()

      store.setAliceCode('TEST123')
      store.resetForm()

      expect(store.form.aliceCode).toBe('')
    })
  })

  describe('Specialty Management', () => {
    it('should handle specialty selection correctly', () => {
      const store = useCreateFormStore()
      const specialtyId = 'specialty1'

      store.handleSelectSpecialty(specialtyId)
      expect(store.specialties).toContain(specialtyId)

      store.handleSelectSpecialty(specialtyId)
      expect(store.specialties).not.toContain(specialtyId)
    })

    it('should initialize specialties from API data', () => {
      const store = useCreateFormStore()
      const apiSpecialties: MedicalSpecialty[] = [
        {
          id: '1',
          medicalSpecialtyId: 'spec1',
          name: 'Specialty 1',
          isTherapy: false,
          currentEndAt: '',
          currentBeginAt: '',
          pricingStatus: {
            value: PricingStatusEnum.enum.PENDING,
            friendlyName: 'Pendente'
          },
          hasScheduledPriceChange: false
        },
        {
          id: '2',
          medicalSpecialtyId: 'spec2',
          name: 'Specialty 2',
          isTherapy: false,
          currentEndAt: '',
          currentBeginAt: '',
          pricingStatus: {
            value: PricingStatusEnum.enum.PRICED,
            friendlyName: 'Precificado'
          },
          hasScheduledPriceChange: false
        }
      ]

      store.handleInitializeCodeSpecialties(apiSpecialties)
      expect(store.specialties).toEqual(['spec1', 'spec2'])
    })
  })

  describe('Navigation and Steps', () => {
    it('should handle step navigation correctly', () => {
      const store = useCreateFormStore()

      expect(store.currentStep).toBe(1)
      store.nextStep()
      expect(store.currentStep).toBe(2)
      store.prevStep()
      expect(store.currentStep).toBe(1)
    })

    it('should show summary modal on last step', () => {
      const store = useCreateFormStore()

      // Navigate to last step
      while (store.currentStep < store.totalSteps) {
        store.nextStep()
      }
      store.nextStep()

      expect(store.showSummaryModal).toBe(true)
    })
  })

  describe('Modal Management', () => {
    it('should handle modal state correctly', () => {
      const store = useCreateFormStore()

      store.setShowSummaryModal(true)
      expect(store.showSummaryModal).toBe(true)

      store.setShowSummaryModal(false)
      expect(store.showSummaryModal).toBe(false)
    })

    it('should handle modal close with callbacks', async () => {
      const store = useCreateFormStore()
      const finishCallback = vi.fn()
      const continueCallback = vi.fn()

      // Mock the router push to resolve immediately
      const mockPush = vi.fn().mockResolvedValue(undefined)
      vi.mocked(useRouter).mockReturnValue({
        push: mockPush
      } as Partial<Router> as Router)

      await store.handleCloseSummaryModal({ detail: 'confirm' } as CustomEvent, {
        finishCallback,
        continueCallback
      })

      expect(continueCallback).toHaveBeenCalled()
      expect(finishCallback).toHaveBeenCalled()
      expect(store.showSummaryModal).toBe(false)
    })
  })

  describe('Status Management', () => {
    it('should return correct status label and value', () => {
      const store = useCreateFormStore()

      store.setActiveStatus('ACTIVE')
      expect(store.getCurrentStatus()).toEqual({
        label: 'Ativado',
        value: 'ACTIVE'
      })

      store.setActiveStatus('INACTIVE')
      expect(store.getCurrentStatus()).toEqual({
        label: 'Desativado',
        value: 'INACTIVE'
      })
    })
  })
})
