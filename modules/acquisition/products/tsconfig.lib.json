{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../../dist/out-tsc", "types": ["vite/client"]}, "exclude": ["src/**/__tests__/*", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx", "src/**/*.spec.vue", "src/**/*.test.vue"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]}