import type { NavigationGuardWithThis, RouteRecordRaw } from 'vue-router'

const productsRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/acquisition/products',
      name: 'acquisition/products',
      beforeEnter,
      component: async () => await import('@acquisition-products/views/ProductsView.vue')
    }
  ]
}

export default productsRoutes
