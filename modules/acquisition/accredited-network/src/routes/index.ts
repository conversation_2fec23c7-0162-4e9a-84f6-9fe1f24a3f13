import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const accreditedNetworkRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/acquisition/accredited-network',
      name: 'acquisition/accredited-network',
      beforeEnter,
      component: async () => await import('@acquisition-accredited-network/views/ListView.vue')
    },
    {
      path: '/acquisition/accredited-network/:id',
      name: 'acquisition/accredited-network-edit',
      beforeEnter,
      component: async () => await import('@acquisition-accredited-network/views/EditView.vue')
    }
  ]
}

export default accreditedNetworkRoutes
