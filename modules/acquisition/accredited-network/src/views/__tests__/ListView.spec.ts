import ListView from '@acquisition-accredited-network/views/ListView.vue'
import { customRender, screen } from '@acquisition-accredited-network/tests'
import { server } from '@commons/services/mockServer'
import { getAccreditedNetwork } from '@acquisition-accredited-network/tests/mocks'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getAccreditedNetwork))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(ListView)

    expect(screen.getByText('Nome')).toBeInTheDocument()
    expect(screen.getByText('Ativo')).toBeInTheDocument()
    expect(screen.getByText('Editar')).toBeInTheDocument()
  })
})
