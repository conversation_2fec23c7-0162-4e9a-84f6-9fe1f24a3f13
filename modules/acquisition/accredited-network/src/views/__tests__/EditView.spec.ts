import EditView from '../EditView.vue'
import { customRender, screen } from '@acquisition-accredited-network/tests'
import { server } from '@commons/services/mockServer'
import { getAccreditedNetworkById } from '@acquisition-accredited-network/tests/mocks'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getAccreditedNetworkById))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('heading', {
        name: 'Editar'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('textbox', { name: 'Nome' })).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })
})
