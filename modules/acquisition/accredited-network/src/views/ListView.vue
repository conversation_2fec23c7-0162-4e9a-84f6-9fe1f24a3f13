<template>
  <ListLayout>
    <template #list>
      <div class="filters">
        <WListControllers
          has-pagination
          margin="none"
          has-items-per-page-select
          input-placeholder="Buscar"
          :hide-input="false"
          :value="term"
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WInputChange="searchChange"
          @WPaginationPrevious="setPrev"
        >
          <div slot="select-items-per-page">
            <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
              <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
            </WSelect>
          </div>
        </WListControllers>
      </div>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.name" size="medium">
              {{ item.title }}
            </WTableBodyCell>
            <WTableBodyCell
              style="word-break: break-all"
              :width="columnWidthMap.active"
              size="medium"
            >
              <WIcon :icon="item.active ? 'icSuccess' : 'icClose'" />
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="start">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="goToEditAccreditedNetwork(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>
<script setup lang="ts">
import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect,
  WIcon
} from '@alice-health/wonderland-vue'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuery } from '@tanstack/vue-query'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { ListLayout } from '@commons/index'
import { getAccreditedNetwork } from '@acquisition-accredited-network/api/queries/getAccreditedNetwork'
import { SITE_ACCREDITED_NETWORKS } from '@acquisition-accredited-network/constants/queryKeys'

/**
 * Constants
 */

const limits = [5, 10, 15]
const columnWidthMap = {
  name: '800px',
  active: '500px',
  edit: ''
}

const headers = [
  { id: 1, title: 'Nome', width: columnWidthMap.name },
  { id: 2, title: 'Ativo', width: columnWidthMap.active },
  { id: 3, title: 'Editar', width: columnWidthMap.edit }
]

/**
 * Computed Variables
 */

const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const filter = computed(() => (term.value ? objectToJsonString({ q: term.value }) : ''))

const getAccreditedNetworkParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()
const searchTerm = ref('')
const searchTermDebounced = useDebounce(searchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data } = useQuery({
  queryKey: [...SITE_ACCREDITED_NETWORKS, getAccreditedNetworkParams],
  queryFn: () => getAccreditedNetwork(getAccreditedNetworkParams.value),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages
    return results
  }
})

/**
 * Functions
 */

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function goToEditAccreditedNetwork(id: string) {
  router.push({
    name: 'acquisition/accredited-network-edit',
    params: { id }
  })
}

/**
 * Watchers
 */

watch(searchTermDebounced, handleSearch)
</script>
