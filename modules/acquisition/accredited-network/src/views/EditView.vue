<template>
  <div>
    <FormLayout title="Editar">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'acquisition/accredited-network' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>

      <template #form>
        <WTextfield label="ID" :value="id" disabled />
        <WTextfield
          label="Nome"
          v-model="form.title"
          @WBlur="validate('title')"
          :invalid="hasError('title')"
          :errorText="getErrors('title')"
        />
        <WSwitch label="Ativo" v-model="form.active" />
        <WParagraph mode="tertiary" size="large">IDs dos bundles:</WParagraph>
        <div v-for="bundle in data?.bundles" :key="bundle.id">
          <WParagraph size="medium">Id: {{ bundle.id }}</WParagraph>
          <WParagraph size="medium">Nome: {{ bundle.title }}</WParagraph>
        </div>
        <WParagraph mode="tertiary" size="large">Produto:</WParagraph>
        <WTextfield
          label="Id"
          v-model="form.productId"
          @WBlur="validate('productId')"
          :invalid="hasError('productId')"
          :errorText="getErrors('productId')"
        />
        <WParagraph v-if="data?.product?.title" size="medium"
          >Nome: {{ data?.product?.title }}</WParagraph
        >
      </template>
      <template #actions>
        <WButton
          variant="cta"
          size="large"
          :loading="isUpdatePending"
          :disabled="invalid"
          @click="validateAndSave"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watchEffect } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { WTextfield, WButton, WSwitch, WParagraph, WLink } from '@alice-health/wonderland-vue'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'
import getAccreditedNetworkById from '@acquisition-accredited-network/api/queries/getAccreditedNetworkById'
import { updateAccreditedNetwork } from '@acquisition-accredited-network/api/mutations'
import { SITE_ACCREDITED_NETWORK } from '@acquisition-accredited-network/constants/queryKeys'
import type { UpdateSiteAccreditedNetworkForm } from '@acquisition-accredited-network/models/UpdateAccreditedNetwork'
import { useValidation } from '@alice-health/vue-hooks'
import { updateAccreditedNetworkSchema } from '@acquisition-accredited-network/schemas/accreditedNetworkSchema'

const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form = ref<UpdateSiteAccreditedNetworkForm>({
  title: '',
  active: false,
  productId: ''
})

/**
 * Computed
 */
const id = computed(() => route.params.id as string)

/**
 * Hooks
 */
const queryClient = useQueryClient()

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()

const { data } = useQuery({
  queryKey: [...SITE_ACCREDITED_NETWORK, id],
  queryFn: () => getAccreditedNetworkById(id.value)
})

const { mutate: update, isPending: isUpdatePending } = useMutation({
  mutationFn: () =>
    updateAccreditedNetwork(id.value, {
      title: form.value.title,
      active: form.value?.active,
      productId: form.value?.productId
    }),
  onSuccess: () => onSuccess(),
  onError: () => onError()
})

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: updateAccreditedNetworkSchema,
  form,
  invalidInitialValue: false
})

/**
 * Functions
 */
function validateAndSave() {
  const schema = validateAll()
  if (schema.success) update()
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: SITE_ACCREDITED_NETWORK })

  router.push({ name: 'acquisition/accredited-network' })

  snackbar?.value?.$el.add({
    message: 'Atualizado com sucesso',
    icon: 'icAlertCircleOutlined'
  })
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao atualizar',
    icon: 'icAlertCircleOutlined'
  })
}

/**
 * Watchers
 */
watchEffect(() => {
  if (data.value) {
    form.value = {
      title: data.value.title,
      active: data.value.active,
      productId: data?.value?.product?.id
    }
  }
})
</script>
