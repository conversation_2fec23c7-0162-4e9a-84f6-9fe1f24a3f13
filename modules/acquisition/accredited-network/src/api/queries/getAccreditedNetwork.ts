import { useQueryString } from '@alice-health/vue-hooks'

import type { AccreditedNetwork } from '@acquisition-accredited-network/models/AccreditedNetwork'
import type { PaginatedResponse } from '@commons/index'

import http from '@http/index'

export type accreditedNetworkResponse = PaginatedResponse<AccreditedNetwork[]>

export const getAccreditedNetwork = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<accreditedNetworkResponse>(`/siteAccreditedNetwork${qs}`)

  return data
}
