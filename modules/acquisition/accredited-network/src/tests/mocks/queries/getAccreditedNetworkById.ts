import { HttpResponse, http } from 'msw'
import type { AccreditedNetworkById } from '@acquisition-accredited-network/models/AccreditedNetworkById'

const getAccreditedNetworkById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/siteAccreditedNetwork/:id`,
  ({ params }) => {
    const id = params.id as string

    const accreditedNetworkById: AccreditedNetworkById = {
      id,
      title: 'Exclusivo +',
      active: true,
      bundles: [
        {
          id: 'cdaa7235-c5a3-483e-8ee8-1e7139ff1300',
          title: 'Tier 1 Especialistas - 2309'
        },
        {
          id: '83109980-14b4-44f7-b0d0-4403ad2a3500',
          title: 'PME - Tier 1 Apto'
        },
        {
          id: '338b5199-2866-4766-a504-138d995cc800',
          title: 'Maternidade Tier 1 e 2'
        },
        {
          id: 'f57b3d87-98c4-4140-92bf-d354e969ea00',
          title: 'Labs CN via Rede Cassi'
        },
        {
          id: 'f65ec43e-ae6d-4a7b-8219-081e901ff100',
          title: 'Especs CN via Rede Cassi'
        },
        {
          id: '57c2f20c-130e-4a9f-b8de-112da99bf100',
          title: 'Hospitais CN via Rede Cassi'
        },
        {
          id: '2f4b391b-1e66-4be4-b96a-fd3855131c00',
          title: 'Labs upgrade Fleury'
        },
        {
          id: '36cf7cc1-b0af-4894-b558-f41d712b8900',
          title: 'NP Tier 1 de Especialistas'
        },
        {
          id: '1bbc92da-4d08-4e1c-bc69-391198d77100',
          title: 'Clínicas Tier 1'
        },
        {
          id: 'fc88b7ff-9533-4830-8a0e-49aeea211300',
          title: 'Clinicas Cassi'
        }
      ],
      products: [
        {
          id: 'cdaa7235-c5a3-483e-8ee8-1e7139ff1300',
          title: 'Alice EN0908'
        }
      ]
    }

    return HttpResponse.json(accreditedNetworkById)
  }
)

export default getAccreditedNetworkById
