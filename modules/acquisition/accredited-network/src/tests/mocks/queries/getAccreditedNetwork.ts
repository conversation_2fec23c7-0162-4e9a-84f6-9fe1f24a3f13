import { HttpResponse, http } from 'msw'
import type { AccreditedNetwork } from '@acquisition-accredited-network/models/AccreditedNetwork'

const getAccreditedNetwork = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/siteAccreditedNetwork`,
  () => {
    const getAccreditedNetwork: AccreditedNetwork[] = [
      {
        id: '1',
        title: 'Equilíbrio Enf. +',
        active: false
      },
      {
        id: '2',
        title: 'Exclusivo ++',
        active: false
      },
      {
        id: '3',
        title: 'Equilíbrio Enf.',
        active: true
      },
      {
        id: '4',
        title: 'Exclusivo +',
        active: false
      },
      {
        id: '5',
        title: 'Conforto',
        active: true
      },
      {
        id: '6',
        title: 'Exclusivo',
        active: true
      },
      {
        id: '7',
        title: 'Equilíbrio',
        active: true
      },
      {
        id: '8',
        title: 'Equilíbrio +',
        active: false
      },
      {
        id: '9',
        title: 'Conforto +',
        active: false
      },
      {
        id: '10',
        title: 'Conforto ++',
        active: true
      },
      {
        id: '11',
        title: 'Exclusivo ++',
        active: true
      },
      {
        id: '12',
        title: 'Equilibrio Apto',
        active: true
      },
      {
        id: '13',
        title: 'Exclusivo Apto',
        active: true
      }
    ]

    return HttpResponse.json({
      pagination: {
        totalPages: 1,
        pageSize: 10
      },
      results: getAccreditedNetwork
    })
  }
)

export default getAccreditedNetwork
