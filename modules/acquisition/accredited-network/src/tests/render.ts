import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'

export * from '@alice-health/unit-presets/src/testing-library'
import user from '@testing-library/user-event'

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  const routerInstance = createRouter({
    history: createWebHistory('/'),
    routes: [
      { path: '/', name: 'home', component: async () => ({}) },
      {
        path: '/accredited-network',
        name: 'acquisition/accredited-network',
        component: async () => ({})
      },
      {
        path: '/accredited-network/:id',
        name: 'acquisition/accredited-network-edit',
        component: async () => ({})
      }
    ]
  })

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}

export { user }
