{"compilerOptions": {"allowJs": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "jsx": "preserve", "jsxImportSource": "vue", "moduleResolution": "node", "resolveJsonModule": true, "verbatimModuleSyntax": true, "paths": {"@acquisition-accredited-network/*": ["./modules/acquisition/accredited-network/src/*"], "@http/*": ["./modules/core/http/src/*"], "@commons/*": ["./modules/core/commons/src/*"]}}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../tsconfig.base.json"}