<script setup lang="tsx">
import { computed, ref, toRefs } from 'vue'
import { useDebounce, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { getProviders } from '@acquisition-vic-product-option/api/queries'
import { PROVIDERS } from '@acquisition-vic-product-option/constants/queryKeys'
import type { Provider, ProviderType } from '@acquisition-vic-product-option/models'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'
import { WAutocomplete } from '@alice-health/wonderland-vue'

/**
 * Props
 */

interface ProviderSearchProps {
  providerType: ProviderType
  modelValue: string
  initialData?: Provider[]
  label?: string
  queryKeys?: string[]
}

const props = defineProps<ProviderSearchProps>()

const { initialData = [] } = toRefs(props)

/**
 * Emits
 */

interface Events {
  (e: 'update:modelValue', value: string): void
}

const emit = defineEmits<Events>()

/**
 * Refs
 */

const searchTerm = ref('')

/**
 * Hooks
 */

const { objectToJsonString } = useQueryString()
const searchTermDebounced = useDebounce(searchTerm, 800)

/**
 * Computeds
 */

const queryParams = computed(() => {
  const search = searchTermDebounced.value
  const filter = search
    ? objectToJsonString({ q: search.trim(), providerType: props.providerType })
    : objectToJsonString({ providerType: props.providerType })

  return { filter, page: '1', pageSize: '6' }
})

const providerItems = computed(
  () => providers.value?.map((provider) => ({ id: provider.id, name: provider.name })) || []
)

const selectedProviderItem = computed(() =>
  providerItems.value.find((provider) => props.modelValue === provider.id)
)

/**
 * Queries
 */

const { data: providers, isRefetching: loadingProviders } = useQuery({
  queryKey: [...PROVIDERS, ...(props.queryKeys || []), queryParams],
  queryFn: () => getProviders({ ...queryParams.value }),
  placeholderData: initialData
})

/**
 * Functions
 */
const onChange = (event: WAutocompleteCustomEvent<string>) => {
  searchTerm.value = event.detail

  if (!event.detail) {
    emit('update:modelValue', '')
  }
}

const onSelect = (event: WAutocompleteCustomEvent<{ id: string; name: string }>) => {
  emit('update:modelValue', event.detail.id)
}
</script>

<template>
  <WAutocomplete
    :value="selectedProviderItem"
    :label="label"
    :items="providerItems"
    :loading="loadingProviders"
    :placeholder="label"
    search-by-key="name"
    required
    @WChange="onChange"
    @WSelect="onSelect"
  />
</template>
