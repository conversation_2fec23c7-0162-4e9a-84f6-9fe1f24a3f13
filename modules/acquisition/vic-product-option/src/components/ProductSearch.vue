<script setup lang="tsx">
import { useDebounce, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { getProducts } from '@acquisition-vic-product-option/api/queries'
import { PRODUCTS } from '@acquisition-vic-product-option/constants/queryKeys'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'
import { computed, ref } from 'vue'
import { WAutocomplete } from '@alice-health/wonderland-vue'

/**
 * Props
 */

interface ProductSearchProps {
  modelValue: { id: string; name: string }
  label?: string
  queryKeys?: string[]
}

const props = defineProps<ProductSearchProps>()

/**
 * Emits
 */

interface Events {
  (e: 'update:modelValue', value: { id: string; name: string }): void
}

const emit = defineEmits<Events>()

/**
 * Refs
 */

const searchTerm = ref(props.modelValue.name || '')

/**
 * Hooks
 */

const { objectToJsonString } = useQueryString()
const searchTermDebounced = useDebounce(searchTerm, 800)

/**
 * Computeds
 */

const getproductsParams = computed(() => {
  const search = searchTermDebounced.value
  const filter = search ? objectToJsonString({ q: search.trim() }) : objectToJsonString({})

  return { filter, page: '1', pageSize: '6' }
})

const productItems = computed(
  () => products.value?.map((product) => ({ id: product.id, name: product.title })) || []
)

const selectedProductItem = computed(() =>
  productItems.value.find((item) => props.modelValue.id === item.id)
)

const placeholderData = computed(() =>
  props.modelValue.id ? [{ id: props.modelValue.id, title: props.modelValue.name }] : []
)

/**
 * Queries
 */

const { data: products, isRefetching: loadingProducts } = useQuery({
  queryKey: [...PRODUCTS, getproductsParams],
  queryFn: () => getProducts(getproductsParams.value),
  placeholderData
})

/**
 * Functions
 */

function onChange(event: WAutocompleteCustomEvent<string>) {
  searchTerm.value = event.detail

  if (!event.detail) {
    emit('update:modelValue', { id: '', name: '' })
  }
}

function onSelect(event: WAutocompleteCustomEvent<{ id: string; name: string }>) {
  emit('update:modelValue', event.detail)
}
</script>

<template>
  <WAutocomplete
    :label="label"
    :value="selectedProductItem"
    :items="productItems"
    :loading="loadingProducts"
    :placeholder="label"
    search-by-key="name"
    required
    @WChange="onChange"
    @WSelect="onSelect"
  />
</template>
