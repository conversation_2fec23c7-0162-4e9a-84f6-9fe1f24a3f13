<script setup lang="tsx">
import { useDebounce, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { getSiteAccreditedNetworks } from '@acquisition-vic-product-option/api/queries/getSiteAccreditedNetworks'
import { SITE_ACCREDITED_NETWORKS } from '@acquisition-vic-product-option/constants/queryKeys'
import { computed, ref } from 'vue'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'
import { WAutocomplete } from '@alice-health/wonderland-vue'

/**
 * Props
 */

interface ProductSearchProps {
  modelValue: { id: string; name: string }
  label?: string
  queryKeys?: string[]
}

const props = defineProps<ProductSearchProps>()

/**
 * Emits
 */

interface Events {
  (e: 'update:modelValue', value: { id: string; name: string }): void
}

const emit = defineEmits<Events>()

/**
 * Refs
 */

const searchTerm = ref('')

/**
 * Computeds
 */

const getSiteAccreditedNetworksParams = computed(() => {
  const search = siteAccreditedNetworkSearchDebounced.value
  const filter = search ? objectToJsonString({ q: search.trim() }) : objectToJsonString({})

  return { filter, page: '1', pageSize: '6' }
})

const siteAccreditedNetworkItems = computed(
  () => siteAccreditedNetworks.value?.map((item) => ({ id: item.id, name: item.title })) || []
)

const selectedSiteAccreditedNetworkItem = computed(() =>
  siteAccreditedNetworkItems.value.find((item) => item.id === props.modelValue.id)
)

const placeholderData = computed(() =>
  props.modelValue ? [{ id: props.modelValue.id, title: props.modelValue.name }] : []
)

/**
 * Hooks
 */

const { objectToJsonString } = useQueryString()

const siteAccreditedNetworkSearchDebounced = useDebounce(searchTerm, 800)

/**
 * Queries
 */

const { data: siteAccreditedNetworks, isRefetching: loadingSiteAccreditedNetworks } = useQuery({
  queryKey: [...SITE_ACCREDITED_NETWORKS, getSiteAccreditedNetworksParams],
  queryFn: () => getSiteAccreditedNetworks(getSiteAccreditedNetworksParams.value),
  placeholderData
})

/**
 * Functions
 */

const onChange = (event: WAutocompleteCustomEvent<string>) => {
  searchTerm.value = event.detail

  if (!event.detail) {
    emit('update:modelValue', { id: '', name: '' })
  }
}

const onSelect = (event: WAutocompleteCustomEvent<{ id: string; name: string }>) => {
  emit('update:modelValue', event.detail)
}
</script>

<template>
  <WAutocomplete
    :label="label"
    :value="selectedSiteAccreditedNetworkItem"
    :items="siteAccreditedNetworkItems"
    :loading="loadingSiteAccreditedNetworks"
    :placeholder="label"
    search-by-key="name"
    required
    @WChange="onChange"
    @WSelect="onSelect"
  />
</template>
