<script setup lang="tsx">
import { WTextfield } from '@alice-health/wonderland-vue'
import { computed, ref, toRefs, watch } from 'vue'
import { useValidation } from '@alice-health/vue-hooks'
import { ProviderRepresentationForm } from '@acquisition-vic-product-option/schemas/providerRepresentationForm'
import ProviderSearch from './ProviderSearch.vue'
import { useQuery } from '@tanstack/vue-query'
import { PROVIDER } from '@acquisition-vic-product-option/constants/queryKeys'
import { getProviderById } from '@acquisition-vic-product-option/api/queries'
import type { ProviderRepresentation, ProviderType } from '@acquisition-vic-product-option/models'

/**
 * Props
 */

type ProviderRepresentationFormProps = {
  queryKey: string
  providerRepresentation: ProviderRepresentation
  providerType: ProviderType
}

const props = defineProps<ProviderRepresentationFormProps>()

const { providerRepresentation, providerType, queryKey } = toRefs(props)

/**
 * Emits
 */

type Events = {
  (e: 'onChange', value: ProviderRepresentation): void
}

const emit = defineEmits<Events>()

/**
 * Refs
 */

const form = ref<ProviderRepresentation>({
  uniqueId: providerRepresentation.value.uniqueId,
  id: providerRepresentation.value.id,
  name: providerRepresentation.value.name,
  icon: providerRepresentation.value.icon
})

/**
 * Computeds
 */

const label = computed(() => {
  switch (providerType.value) {
    case 'HOSPITAL':
      return 'Hospital'
    case 'LABORATORY':
      return 'Laboratório'
    default:
      return ''
  }
})

const initialData = computed(() => (currentProvider.value ? [currentProvider.value] : []))

const shouldFetchCurrentProvider = computed(() => !!providerRepresentation.value.id)

/**
 * Hooks
 */

const { getErrors, hasError, validate } = useValidation({
  formSchema: ProviderRepresentationForm,
  form,
  invalidInitialValue: false
})

/**
 * Queries
 */

const { data: currentProvider } = useQuery({
  queryKey: [...PROVIDER, providerRepresentation.value.uniqueId],
  queryFn: () => getProviderById(providerRepresentation.value.id),
  enabled: shouldFetchCurrentProvider
})

/**
 * Watchers
 */
watch(form, (formValue) => emit('onChange', formValue), { deep: true })
</script>

<template>
  <div class="provider-representation" data-testid="provider-representation-form">
    <ProviderSearch
      v-model="form.id"
      :initialData="initialData"
      :providerType="providerType"
      :label="label"
      :queryKeys="[queryKey]"
    />

    <WTextfield
      label="Nome de exibição"
      v-model="form.name"
      @WBlur="validate('name')"
      :invalid="hasError('name')"
      :errorText="getErrors('name')"
    />

    <WTextfield
      label="Url do ícone (não utilizar links do Drive)"
      v-model="form.icon"
      @WBlur="validate('icon')"
      :invalid="hasError('icon')"
      :errorText="getErrors('icon')"
    />
  </div>
</template>

<style lang="scss" scoped>
.provider-representation {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-04);
}
</style>
