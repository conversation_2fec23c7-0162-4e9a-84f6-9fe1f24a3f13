<script setup lang="tsx">
import {
  type ProviderRepresentation,
  type ProviderType
} from '@acquisition-vic-product-option/models'
import ProviderRepresentationForm from './ProviderRepresentationForm.vue'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WParagraph, WTitle } from '@alice-health/wonderland-vue'
import { computed, toRefs } from 'vue'

/**
 * Props
 */

const props = defineProps<{
  title: string
  subtitle: string
  modelValue: ProviderRepresentation[]
  providerType: ProviderType
}>()

const { modelValue: providers, providerType } = toRefs(props)

/**
 * Emits
 */

type Events = {
  (e: 'update:modelValue', value: ProviderRepresentation[]): void
}

const emit = defineEmits<Events>()

/**
 * Computeds
 */

const emptyListText = computed(() =>
  providerType.value === 'HOSPITAL' ? 'Nenhum hospital adicionado' : 'Nenhum laboratório adicionado'
)

/**
 * Functions
 */

const moveUp = (index: number) => {
  if (index === 0) return

  const items = [...providers.value]

  ;[items[index - 1], items[index]] = [items[index], items[index - 1]]

  emit('update:modelValue', items)
}

const moveDown = (index: number) => {
  if (index === providers.value.length - 1) return

  const items = [...providers.value]

  ;[items[index], items[index + 1]] = [items[index + 1], items[index]]

  emit('update:modelValue', items)
}

const add = () => {
  const items = [...providers.value]

  items.unshift({
    uniqueId: crypto.randomUUID(),
    id: '',
    name: '',
    icon: ''
  })

  emit('update:modelValue', items)
}

const remove = (index: number) => {
  const items = [...providers.value]

  items.splice(index, 1)

  emit('update:modelValue', items)
}

const onChange = (provider: ProviderRepresentation, index: number) => {
  const items = [...providers.value]

  items[index] = provider

  emit('update:modelValue', items)
}

const isFirst = (index: number) => index === 0
const isLast = (index: number) => index === providers.value.length - 1
</script>

<template>
  <section class="provider-representation-selector" data-testid="provider-representation-selector">
    <div class="provider-representation-selector__header">
      <div>
        <WTitle size="small">{{ title }}</WTitle>
        <WHelper>{{ subtitle }}</WHelper>
      </div>

      <WButton
        @click="add"
        :disabled="modelValue.length >= 3"
        variant="cta"
        iconPosition="trailing"
        icon="icAdd"
      >
        Adicionar
      </WButton>
    </div>

    <TransitionGroup
      name="list"
      tag="div"
      class="provider-representation-selector__transition-container"
    >
      <div v-if="providers.length === 0">
        <WParagraph>{{ emptyListText }}</WParagraph>
      </div>
      <div
        v-else
        class="provider-representation-selector__container"
        v-for="(provider, index) in providers"
        :key="provider.uniqueId"
      >
        <WButton
          @click="remove(index)"
          class="provider-representation-selector__container__delete-button"
          iconButton
          variant="danger"
          icon="icDelete"
          size="small"
        />

        <ProviderRepresentationForm
          class="provider-representation-selector__container__item"
          @onChange="(providerRepresentation) => onChange(providerRepresentation, index)"
          :queryKey="`${providerType}-${index}`"
          :providerRepresentation="provider"
          :providerType="providerType"
        />

        <div class="provider-representation-selector__container__control">
          <WButton
            v-if="!isFirst(index)"
            @click="() => moveUp(index)"
            iconButton
            icon="icArrowUp"
          />
          <WButton
            v-if="!isLast(index)"
            @click="() => moveDown(index)"
            iconButton
            icon="icArrowDown"
          />
        </div>
      </div>
    </TransitionGroup>
  </section>
</template>

<style lang="scss" scoped>
.provider-representation-selector {
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--gl-spacing-04);
  }

  &__transition-container {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-02);
  }

  &__container {
    position: relative;
    width: 520px;

    &__delete-button {
      position: absolute;
      right: 0;
      top: -0;
    }

    &__item {
      padding: var(--gl-spacing-05);
      margin: var(--gl-spacing-04);
      background-color: var(--gl-color-shades-gray-10);
      border: 1px solid #ddd;
      border-radius: var(--gl-border-radius-xs);
      transition: all 0.2s ease;
      box-shadow: var(--gl-shadow-level-02);
    }

    &__control {
      display: flex;
      flex-direction: column;
      gap: var(--gl-spacing-03);
      position: absolute;
      top: 35%;
      right: -45px;
    }
  }

  .list-move,
  .list-enter-active {
    transition: all 0.5s ease;
  }

  .list-leave-active {
    transition: opacity 0.2s ease;
  }

  .list-enter-from,
  .list-leave-to {
    opacity: 0;
  }

  .list-enter-from {
    transform: translateX(30px);
  }

  .list-leave-active {
    position: absolute;
  }
}
</style>
