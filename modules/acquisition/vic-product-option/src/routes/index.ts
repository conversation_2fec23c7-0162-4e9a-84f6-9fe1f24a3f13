import type { NavigationGuardWithThis, RouteRecordRaw } from 'vue-router'

const vicProductOptionRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/acquisition/vic-product-option',
      name: 'acquisition/vic-product-option',
      beforeEnter,
      component: async () => await import('@acquisition-vic-product-option/views/ListView.vue')
    },
    {
      path: '/acquisition/vic-product-option/create',
      name: 'acquisition/vic-product-option-create',
      beforeEnter,
      component: async () => await import('@acquisition-vic-product-option/views/CreateView.vue')
    },
    {
      path: '/acquisition/vic-product-option/:id',
      name: 'acquisition/vic-product-option-edit',
      beforeEnter,
      component: async () => await import('@acquisition-vic-product-option/views/EditView.vue')
    }
  ]
}

export default vicProductOptionRoutes
