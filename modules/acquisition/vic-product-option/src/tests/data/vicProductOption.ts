import { type VicProductOption } from '@acquisition-vic-product-option/models'
import { hospitals } from './hospitals'
import { laboratories } from './laboratories'
import { products } from './products'
import { siteAccreditedNetworks } from './siteAccreditedNetworks'

const [product] = products
const [siteAccreditedNetwork] = siteAccreditedNetworks

export const vicProductOption: VicProductOption = {
  id: '08185562-946a-4d0c-868d-38f2759c5100',
  productId: product.id,
  productName: product.title,
  productCategory: 'EXCLUSIVE',
  accommodation: 'NURSERY',
  coverage: 'NATIONAL',
  siteAccreditedNetworkId: siteAccreditedNetwork.id,
  siteAccreditedNetworkName: siteAccreditedNetwork.title,
  coPayment: false,
  type: 'ME',
  hospitals,
  laboratories
}
