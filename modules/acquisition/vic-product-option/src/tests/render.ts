import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'

export * from '@alice-health/unit-presets/src/testing-library'
import user from '@testing-library/user-event'

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  const routerInstance = createRouter({
    history: createWebHistory('/'),
    routes: [
      { path: '/', name: 'home', component: async () => ({}) },
      { path: '/acquisition/vic-product-option', name: 'acquisition/vic-product-option', component: async () => ({}) },
      { path: '/acquisition/vic-product-option/:id', name: 'acquisition/vic-product-option-edit', component: async () => ({}) },
      { path: '/acquisition/vic-product-option/create', name: 'acquisition/vic-product-option-create', component: async () => ({}) },
    ]
  })

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}

export { user }
