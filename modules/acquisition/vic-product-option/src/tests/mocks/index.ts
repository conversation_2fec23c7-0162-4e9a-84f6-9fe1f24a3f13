import { createVicProductOption } from './mutations/createVicProductOption'
import { updateVicProductOption } from './mutations/updateVicProductOption'
import { getAccommodations } from './queries/getAccommodations'
import { getCoverages } from './queries/getCoverages'
import { getProductCategories } from './queries/getProductCategories'
import { getProductOptionTypes } from './queries/getProductOptionTypes'
import { getProducts } from './queries/getProducts'
import { getProvidersById } from './queries/getProviderById'
import { getProviders } from './queries/getProviders'
import { getSiteAccreditedNetworks } from './queries/getSiteAccreditedNetworks'
import { getVicProductOptionById } from './queries/getVicProductOptionById'
import { getVicProductOptions } from './queries/getVicProductOptions'

const handlers = [
  createVicProductOption,
  updateVicProductOption,
  getSiteAccreditedNetworks,
  getProducts,
  getProviders,
  getAccommodations,
  getCoverages,
  getProductCategories,
  getProductOptionTypes,
  getProvidersById,
  getVicProductOptionById,
  getVicProductOptions
]

export {
  handlers,
  getVicProductOptionById,
  getVicProductOptions,
  createVicProductOption,
  updateVicProductOption,
  getSiteAccreditedNetworks,
  getProducts,
  getProviders,
  getProvidersById,
  getAccommodations,
  getCoverages,
  getProductCategories,
  getProductOptionTypes
}
