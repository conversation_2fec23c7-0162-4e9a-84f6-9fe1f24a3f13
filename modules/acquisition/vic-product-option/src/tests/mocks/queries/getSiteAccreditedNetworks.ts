import { siteAccreditedNetworks } from '@acquisition-vic-product-option/tests/data/siteAccreditedNetworks'
import { HttpResponse, http } from 'msw'

const endpoint = `${
  import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL
}/vicProductOption/siteAccreditedNetwork`

const getSiteAccreditedNetworks = http.get(endpoint, ({ request }) => {
  const url = new URL(request.url)

  const filters = url.searchParams.get('filter')

  const { q: search } = (filters ? JSON.parse(filters) : {}) as {
    q?: string
  }

  const results = search
    ? siteAccreditedNetworks.filter(({ title }) =>
        title.toLowerCase().includes(search.toLowerCase())
      )
    : siteAccreditedNetworks

  return HttpResponse.json(results)
})

export { getSiteAccreditedNetworks }
