import { hospitals } from '@acquisition-vic-product-option/tests/data/hospitals'
import { laboratories } from '@acquisition-vic-product-option/tests/data/laboratories'
import { HttpResponse, http } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/vicProductOption/provider/:id`

const getProvidersById = http.get(endpoint, ({ params }) => {
  const { id } = params

  const provider = [...hospitals, ...laboratories].find(({ id: providerId }) => providerId === id)

  if (!provider) return HttpResponse.error()

  return HttpResponse.json(provider)
})

export { getProvidersById }
