import { http, HttpResponse } from 'msw'

const endpoint = `${
  import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL
}/vicProductOption/productCategory`

const getProductCategories = http.get(endpoint, () => {
  return HttpResponse.json([
    {
      id: 'BALANCE',
      value: 'BALANCE'
    },
    {
      id: 'COMFORT',
      value: 'COMFORT'
    },
    {
      id: 'EXCLUSIVE',
      value: 'EXCLUSIVE'
    }
  ])
})

export { getProductCategories }
