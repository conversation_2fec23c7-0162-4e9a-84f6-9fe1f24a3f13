import type { ProviderType } from '@acquisition-vic-product-option/models'
import { hospitals } from '@acquisition-vic-product-option/tests/data/hospitals'
import { laboratories } from '@acquisition-vic-product-option/tests/data/laboratories'
import { HttpResponse, http } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/vicProductOption/provider`

const getProviders = http.get(endpoint, ({ request }) => {
  const url = new URL(request.url)

  const filters = url.searchParams.get('filter')

  const { q: search, providerType } = (filters ? JSON.parse(filters) : {}) as {
    q?: string
    providerType?: ProviderType
  }

  if (providerType === 'HOSPITAL') {
    const results = search
      ? hospitals.filter(({ name }) => name.toLowerCase().includes(search.toLowerCase()))
      : hospitals

    return HttpResponse.json(results)
  }

  if (providerType === 'LABORATORY') {
    const results = search
      ? laboratories.filter(({ name }) => name.toLowerCase().includes(search.toLowerCase()))
      : laboratories

    return HttpResponse.json(results)
  }

  return HttpResponse.json([])
})

export { getProviders }
