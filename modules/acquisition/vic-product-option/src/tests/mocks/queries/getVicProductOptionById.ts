import { HttpResponse, http } from 'msw'
import { vicProductOption } from '@acquisition-vic-product-option/tests/data/vicProductOption'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/vicProductOption/:id`

const getVicProductOptionById = http.get(endpoint, ({ params }) => {
  const id = params.id as string

  return HttpResponse.json({ ...vicProductOption, id })
})

export { getVicProductOptionById }
