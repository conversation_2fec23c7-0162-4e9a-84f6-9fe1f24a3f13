import { products } from '@acquisition-vic-product-option/tests/data/products'
import { HttpResponse, http } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/vicProductOption/product`

const getProducts = http.get(endpoint, ({ request }) => {
  const url = new URL(request.url)

  const filters = url.searchParams.get('filter')

  const { q: search } = (filters ? JSON.parse(filters) : {}) as {
    q?: string
  }

  const results = search
    ? products.filter(({ title }) => title.toLowerCase().includes(search.toLowerCase()))
    : products

  return HttpResponse.json(results)
})

export { getProducts }
