import { HttpResponse, http } from 'msw'
import { vicProductOption } from '@acquisition-vic-product-option/tests/data/vicProductOption'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/vicProductOption`

const getVicProductOptions = http.get(endpoint, () => {
  return HttpResponse.json({
    pagination: {
      totalPages: 1,
      pageSize: 10
    },
    results: [vicProductOption]
  })
})

export { getVicProductOptions }
