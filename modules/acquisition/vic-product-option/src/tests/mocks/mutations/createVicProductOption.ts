import type { VicProductOption } from '@acquisition-vic-product-option/models'
import { http, HttpResponse } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/vicProductOption`

const createVicProductOption = http.post<never, VicProductOption>(endpoint, async ({ request }) => {
  const payload = await request.json()

  return HttpResponse.json(payload)
})

export { createVicProductOption }
