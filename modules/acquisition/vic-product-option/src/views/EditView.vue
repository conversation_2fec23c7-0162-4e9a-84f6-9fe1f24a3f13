<template>
  <div v-if="!loadingVicProductOptions">
    <FormLayout title="Editar opção de produto">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'acquisition/vic-product-option' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>

      <template #form>
        <WTextfield label="ID" :value="vicProductOption?.id" disabled />

        <ProductSearch label="Produto" v-model="product" />

        <SiteAccreditedNetworkSearch
          label="Agrupamento (Site Accredited Network)"
          v-model="siteAccreditedNetwork"
        />

        <WSelect
          v-if="categories"
          label="Categoria de produto"
          v-model="form.productCategory"
          @WBlur="validate('productCategory')"
          :invalid="hasError('productCategory')"
          :errorText="getErrors('productCategory')"
        >
          <option value="">Selecione uma categoria</option>

          <option v-for="category in categories" :key="category.id" :value="category.value">
            {{ category.value }}
          </option>
        </WSelect>

        <WSelect
          v-if="productOptionTypes"
          label="Tipo do produto"
          v-model="form.type"
          @WBlur="validate('type')"
          :invalid="hasError('type')"
          :errorText="getErrors('type')"
        >
          <option value="">Selecione o tipo do produto</option>

          <option
            v-for="productOptionType in productOptionTypes"
            :key="productOptionType.id"
            :value="productOptionType.value"
          >
            {{ productOptionType.value }}
          </option>
        </WSelect>

        <WSelect
          v-if="accommodations"
          label="Acomodação"
          v-model="form.accommodation"
          @WBlur="validate('accommodation')"
          :invalid="hasError('accommodation')"
          :errorText="getErrors('accommodation')"
        >
          <option value="">Selecione acomodação</option>

          <option
            v-for="accommodation in accommodations"
            :key="accommodation.id"
            :value="accommodation.value"
          >
            {{ accommodation.value }}
          </option>
        </WSelect>

        <WSelect
          v-if="coverages"
          label="Cobertura"
          v-model="form.coverage"
          @WBlur="validate('coverage')"
          :invalid="hasError('coverage')"
          :errorText="getErrors('coverage')"
        >
          <option value="">Selecione a cobertura</option>

          <option v-for="coverage in coverages" :key="coverage.id" :value="coverage.value">
            {{ coverage.value }}
          </option>
        </WSelect>

        <WSwitch v-model="form.coPayment" label="Coparticipação" />

        <ProviderRepresentationSelector
          title="Top 3 Hospitais"
          subtitle="adicionar na ordem em que devem aparecer"
          v-model="form.hospitals"
          providerType="HOSPITAL"
        />

        <ProviderRepresentationSelector
          title="Top 3 Laboratórios"
          subtitle="adicionar na ordem em que devem aparecer"
          v-model="form.laboratories"
          providerType="LABORATORY"
        />
      </template>

      <template #actions>
        <WButton
          variant="cta"
          size="large"
          :disabled="saveDisabled"
          :loading="isUpdatePending"
          @click="validateAndSave"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watch, type Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { WTextfield, WButton, WLink, WSelect, WSwitch } from '@alice-health/wonderland-vue'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'
import {
  ACCOMMODATIONS,
  COVERAGES,
  PRODUCT_CATEGORIES,
  PRODUCT_OPTION_TYPES,
  VIC_PRODUCT_OPTION,
  VIC_PRODUCT_OPTIONS
} from '@acquisition-vic-product-option/constants/queryKeys'
import { getVicProductOptionById } from '@acquisition-vic-product-option/api/queries'
import { VicProductOptionForm } from '@acquisition-vic-product-option/schemas/vicProductOptionForm'
import ProviderRepresentationSelector from '@acquisition-vic-product-option/components/ProviderRepresentationSelector.vue'
import ProductSearch from '@acquisition-vic-product-option/components/ProductSearch.vue'
import SiteAccreditedNetworkSearch from '@acquisition-vic-product-option/components/SiteAccreditedNetworkSearch.vue'
import { updateVicProductOption } from '@acquisition-vic-product-option/api/mutations'
import type { Form } from '@acquisition-vic-product-option/types'
import { getProductOptionTypes } from '@acquisition-vic-product-option/api/queries/getProductOptionTypes'
import { getProductCategories } from '@acquisition-vic-product-option/api/queries/getProductCategories'
import { getAccommodations } from '@acquisition-vic-product-option/api/queries/getAccommodations'
import { getCoverages } from '@acquisition-vic-product-option/api/queries/getCoverages'

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<Form> = ref({
  productId: '',
  productName: '',
  siteAccreditedNetworkId: '',
  siteAccreditedNetworkName: '',
  productCategory: '',
  accommodation: '',
  coverage: '',
  type: '',
  hospitals: [],
  laboratories: [],
  coPayment: false
})

/**
 * Computed Variables
 */
const id = computed(() => route.params.id as string)

const saveDisabled = computed(() => !validateAll().success)

const product = computed({
  get: () => ({ id: form.value.productId, name: form.value.productName }),
  set({ id, name }) {
    form.value.productId = id
    form.value.productName = name
  }
})

const siteAccreditedNetwork = computed({
  get: () => ({
    id: form.value.siteAccreditedNetworkId,
    name: form.value.siteAccreditedNetworkName
  }),
  set({ id, name }) {
    form.value.siteAccreditedNetworkId = id
    form.value.siteAccreditedNetworkName = name
  }
})

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()

const queryClient = useQueryClient()

const { getErrors, hasError, validate, validateAll } = useValidation({
  formSchema: VicProductOptionForm,
  form,
  invalidInitialValue: false
})

/**
 * Queries
 */

const { data: vicProductOption, isLoading: loadingVicProductOptions } = useQuery({
  queryKey: [...VIC_PRODUCT_OPTION, id.value],
  queryFn: () => getVicProductOptionById(id.value),
  select: (data) => (form.value = data)
})

const { data: productOptionTypes } = useQuery({
  queryKey: [...PRODUCT_OPTION_TYPES],
  queryFn: () => getProductOptionTypes()
})

const { data: categories } = useQuery({
  queryKey: [...PRODUCT_CATEGORIES],
  queryFn: () => getProductCategories()
})

const { data: accommodations } = useQuery({
  queryKey: [...ACCOMMODATIONS],
  queryFn: () => getAccommodations()
})

const { data: coverages } = useQuery({
  queryKey: [...COVERAGES],
  queryFn: () => getCoverages()
})

/**
 * Mutations
 */

const { mutate: update, isPending: isUpdatePending } = useMutation({
  mutationFn: () => updateVicProductOption(id.value, form.value),
  onSuccess: () => onSuccess('atualizado'),
  onError: () => onError('atualizar')
})

/**
 * Functions
 */
function validateAndSave() {
  const schema = validateAll()

  if (schema.success) update()
}

async function onSuccess(action: string) {
  await Promise.all([
    queryClient.invalidateQueries({ queryKey: VIC_PRODUCT_OPTIONS }),
    queryClient.invalidateQueries({ queryKey: [...VIC_PRODUCT_OPTION, id.value] })
  ])

  router.push({ name: 'acquisition/vic-product-option' })

  snackbar?.value?.$el.add({
    message: `Opção de produto ${action} com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} opção de produto`,
    icon: 'icAlertCircleOutlined'
  })
}

/**
 * Watchers
 */
watch(form, () => validateAll(), { deep: true })
</script>
