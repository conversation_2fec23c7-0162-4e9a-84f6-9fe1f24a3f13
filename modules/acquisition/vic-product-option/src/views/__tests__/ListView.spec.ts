import ListView from '@acquisition-vic-product-option/views/ListView.vue'
import { customRender, screen } from '@acquisition-vic-product-option/tests'
import { server } from '@commons/services/mockServer'
import {
  getProductCategories,
  getVicProductOptions
} from '@acquisition-vic-product-option/tests/mocks'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getProductCategories, getVicProductOptions))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(ListView)

    const createButton = await screen.findByRole('button', {
      name: 'Criar'
    })

    expect(createButton).toBeInTheDocument()

    const [, exclusiveValue] = await screen.findAllByText('EXCLUSIVE')

    expect(screen.getByText('Nome do produto')).toBeInTheDocument()
    expect(screen.getByText('Categoria do produto')).toBeInTheDocument()
    expect(screen.getByText('Tipo do produto')).toBeInTheDocument()
    expect(screen.getByText('Tipo da acomodação')).toBeInTheDocument()
    expect(screen.getByText('Nome do agrupamento')).toBeInTheDocument()
    expect(screen.getByText('Coparticipação')).toBeInTheDocument()

    expect(screen.getByText('B2B Company Balance')).toBeInTheDocument()
    expect(exclusiveValue).toBeInTheDocument()
    expect(screen.getByText('ME')).toBeInTheDocument()
    expect(screen.getByText('NURSERY')).toBeInTheDocument()
    expect(screen.getByText('Rede 1')).toBeInTheDocument()
    expect(screen.getByText('NÃO')).toBeInTheDocument()
  })
})
