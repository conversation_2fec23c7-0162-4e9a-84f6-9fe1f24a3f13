import EditView from '@acquisition-vic-product-option/views/EditView.vue'
import { server } from '@commons/services/mockServer'
import { customRender, screen } from '@acquisition-vic-product-option/tests'
import {
  getAccommodations,
  getCoverages,
  getProductCategories,
  getProductOptionTypes,
  getProducts,
  getProviders,
  getProvidersById,
  getSiteAccreditedNetworks,
  getVicProductOptionById,
  updateVicProductOption
} from '@acquisition-vic-product-option/tests/mocks'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() =>
    server.use(
      updateVicProductOption,
      getProducts,
      getSiteAccreditedNetworks,
      getProviders,
      getProvidersById,
      getAccommodations,
      getCoverages,
      getProductCategories,
      getProductOptionTypes,
      getVicProductOptionById
    )
  )

  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(EditView)

    const heading = await screen.findByText('Editar opção de produto')

    const id = await screen.findByRole('textbox', { name: 'ID' })

    const product = await screen.findByPlaceholderText('Produto')
    const siteAccreditedNetwork = await screen.findByPlaceholderText(
      'Agrupamento (Site Accredited Network)'
    )
    const category = await screen.findByRole('combobox', { name: 'Categoria de produto' })
    const productType = await screen.findByRole('combobox', { name: 'Tipo do produto' })
    const accomodation = await screen.findByRole('combobox', { name: 'Acomodação' })
    const coverage = await screen.findByRole('combobox', { name: 'Cobertura' })
    const coPayment = await screen.findByRole('checkbox', { name: 'Coparticipação' })
    const [hospitalsSection, laboratoriesSection] = await screen.findAllByTestId(
      'provider-representation-form'
    )

    expect(heading).toBeInTheDocument()
    expect(id).toBeDisabled()
    expect(product).toBeInTheDocument()
    expect(siteAccreditedNetwork).toBeInTheDocument()
    expect(category).toBeInTheDocument()
    expect(productType).toBeInTheDocument()
    expect(accomodation).toBeInTheDocument()
    expect(coverage).toBeInTheDocument()
    expect(coPayment).toBeInTheDocument()
    expect(hospitalsSection).toBeInTheDocument()
    expect(laboratoriesSection).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  }, 10000)
})
