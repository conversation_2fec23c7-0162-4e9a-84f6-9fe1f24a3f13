import CreateView from '@acquisition-vic-product-option/views/CreateView.vue'
import { server } from '@commons/services/mockServer'
import { customRender, fireEvent, screen } from '@acquisition-vic-product-option/tests'
import {
  createVicProductOption,
  getAccommodations,
  getCoverages,
  getProductCategories,
  getProductOptionTypes,
  getProducts,
  getProviders,
  getProvidersById,
  getSiteAccreditedNetworks,
  getVicProductOptionById,
  getVicProductOptions
} from '@acquisition-vic-product-option/tests/mocks'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() =>
    server.use(
      createVicProductOption,
      getProducts,
      getSiteAccreditedNetworks,
      getProviders,
      getProvidersById,
      getAccommodations,
      getCoverages,
      getProductCategories,
      getProductOptionTypes,
      getVicProductOptionById,
      getVicProductOptions
    )
  )

  afterEach(() => server.resetHandlers())

  it.skip('renders properly', async () => {
    const { user } = customRender(CreateView)

    const heading = await screen.findByRole('heading', {
      name: 'Criar opção de produto'
    })

    expect(heading).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: 'Criar'
      })
    ).toBeDisabled()

    // Fill product

    await user.type(await screen.findByPlaceholderText('Produto'), 'B2B')
    const productOption = await screen.findByText('B2B Company Balance')
    await user.click(productOption)

    // Fill site accredited network

    await user.type(screen.getByPlaceholderText('Agrupamento (Site Accredited Network)'), 'Rede')
    const siteAccreditedNetworkOption = await screen.findByText('Rede 2')
    await user.click(siteAccreditedNetworkOption)

    // Fill product category

    await user.selectOptions(
      screen.getByRole('combobox', { name: 'Categoria de produto' }),
      'COMFORT'
    )

    // Fill product type

    await user.selectOptions(screen.getByRole('combobox', { name: 'Tipo do produto' }), 'ME')

    // Fill accomodation

    await user.selectOptions(screen.getByRole('combobox', { name: 'Acomodação' }), 'NURSERY')

    // Fill coverage

    await user.selectOptions(screen.getByRole('combobox', { name: 'Cobertura' }), 'NATIONAL')

    // Add providers

    const [addHospital, addLaboratory] = screen.getAllByRole('button', { name: 'Adicionar' })

    await user.click(addHospital)
    await user.click(addHospital)
    await user.click(addHospital)

    await user.click(addLaboratory)
    await user.click(addLaboratory)
    await user.click(addLaboratory)

    const hospitalNames = screen.getAllByPlaceholderText('Hospital')

    const labNames = screen.getAllByPlaceholderText('Laboratório')

    const [
      hosp1DisplayName,
      hosp2DisplayName,
      hosp3DisplayName,
      lab1DisplayName,
      lab2DisplayName,
      lab3DisplayName
    ] = screen.getAllByRole('textbox', {
      name: 'Nome de exibição'
    })

    const [hosp1Icon, hosp2Icon, hosp3Icon, lab1Icon, lab2Icon, lab3Icon] = screen.getAllByRole(
      'textbox',
      {
        name: 'Url do ícone (não utilizar links do Drive)'
      }
    )

    // Fill hospital

    const fillHospital = async (index: number) => {
      const displayNames = [hosp1DisplayName, hosp2DisplayName, hosp3DisplayName]
      const icons = [hosp1Icon, hosp2Icon, hosp3Icon]

      await user.type(hospitalNames[index], 'Hosp')
      await user.click(await screen.findByText(`Hosp. ${index + 1}`))

      await user.type(displayNames[index], `Hosp ${index + 1}`)
      await user.type(icons[index], 'http://example.com')
    }

    await fillHospital(0)
    await fillHospital(1)
    await fillHospital(2)

    // Fill laboratory

    const fillLab = async (index: number) => {
      const displayNames = [lab1DisplayName, lab2DisplayName, lab3DisplayName]
      const icons = [lab1Icon, lab2Icon, lab3Icon]

      await fireEvent.update(labNames[index], 'Lab')
      await user.click(await screen.findByText(`Lab ${index + 1}`))

      await user.type(displayNames[index], `Lab ${index + 1}`)
      await user.type(icons[index], 'http://example.com')
    }

    await fillLab(0)
    await fillLab(1)
    await fillLab(2)

    expect(
      await screen.findByRole('button', {
        name: 'Criar'
      })
    ).toBeEnabled()
  }, 20000)
})
