<template>
  <ListLayout title="Corretores">
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="goToCreateVicProductOption"
        >Criar</WButton
      >
    </template>

    <template #list>
      <div class="filters">
        <WSelect v-if="categories" @WChange="setCategory" :model-value="selectedCategory">
          <option value="" selected>Selecione uma categoria</option>

          <option v-for="category in categories" :key="category.id" :value="category.value">
            {{ category.value }}
          </option>
        </WSelect>

        <WListControllers
          has-pagination
          margin="none"
          has-items-per-page-select
          input-placeholder="Busca pelo nome do produto"
          :hide-input="false"
          :value="searchTerm"
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WInputChange="searchTermChange"
          @WPaginationPrevious="setPrev"
        >
          <template v-slot:select-items-per-page>
            <div>
              <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
                <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
              </WSelect>
            </div>
          </template>
        </WListControllers>
      </div>

      <WTable>
        <template slot="header">
          <WTableHeader>
            <WTableHeaderCell
              size="large"
              :width="header.width"
              v-for="header in headers"
              :key="header.id"
            >
              {{ header?.title }}
            </WTableHeaderCell>
          </WTableHeader>
        </template>

        <template slot="body">
          <WTableBody>
            <WTableRow v-for="item in vicProductOptions" :key="item.id">
              <WTableBodyCell :width="columnWidthMap.name" size="medium">
                {{ item.productName }}
              </WTableBodyCell>
              <WTableBodyCell :width="columnWidthMap.category" size="medium">
                {{ item.productCategory }}
              </WTableBodyCell>
              <WTableBodyCell :width="columnWidthMap.productType" size="medium">
                {{ item.type }}
              </WTableBodyCell>
              <WTableBodyCell :width="columnWidthMap.accomodationType" size="medium">
                {{ item.accommodation }}
              </WTableBodyCell>
              <WTableBodyCell :width="columnWidthMap.siteAccreditedNetworkName" size="medium">
                {{ item.siteAccreditedNetworkName }}
              </WTableBodyCell>
              <WTableBodyCell :width="columnWidthMap.coPayment" size="medium" align="center">
                {{ item.coPayment ? 'SIM' : 'NÃO' }}
              </WTableBodyCell>
              <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="end">
                <div style="display: inline-block">
                  <WButton
                    :block="false"
                    icon-button
                    variant="secondary"
                    icon="icEdit"
                    @click="goToEditVicProductOption(item.id)"
                  />
                </div>
              </WTableBodyCell>
            </WTableRow>
          </WTableBody>
        </template>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import {
  WButton,
  WListControllers,
  WSelect,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow
} from '@alice-health/wonderland-vue'

import { ListLayout } from '@commons/index'
import {
  PRODUCT_CATEGORIES,
  VIC_PRODUCT_OPTIONS
} from '@acquisition-vic-product-option/constants/queryKeys'
import {
  getProductCategories,
  getVicProductOptions
} from '@acquisition-vic-product-option/api/queries'

/**
 * Constants
 */
const limits = [5, 10, 15]

const columnWidthMap = {
  name: '20%',
  category: '15%',
  productType: '15%',
  accomodationType: '15%',
  siteAccreditedNetworkName: '15%',
  coPayment: '10%',
  edit: '10%'
}

const headers = [
  { id: 1, title: 'Nome do produto', width: columnWidthMap.name },
  { id: 2, title: 'Categoria do produto', width: columnWidthMap.category },
  { id: 3, title: 'Tipo do produto', width: columnWidthMap.productType },
  { id: 4, title: 'Tipo da acomodação', width: columnWidthMap.accomodationType },
  { id: 5, title: 'Nome do agrupamento', width: columnWidthMap.siteAccreditedNetworkName },
  { id: 6, title: 'Coparticipação', width: columnWidthMap.coPayment },
  { id: 7, width: columnWidthMap.edit }
]

/**
 * Refs
 */
const searchTerm = ref('')
const selectedCategory = ref('')

/**
 * Computed Variables
 */
const getProductNameFilter = computed(() => {
  const filterObject = {
    ...(searchTermDebounced.value ? { productName: searchTermDebounced.value } : {}),
    ...(selectedCategory.value ? { productCategory: selectedCategory.value } : {})
  }

  return objectToJsonString(filterObject)
})

const getVicProductOptionsParams = computed(() => ({
  filter: getProductNameFilter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()

const { objectToJsonString } = useQueryString()

const searchTermDebounced = useDebounce(searchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data: vicProductOptions } = useQuery({
  queryKey: [...VIC_PRODUCT_OPTIONS, getVicProductOptionsParams],
  queryFn: () => getVicProductOptions(getVicProductOptionsParams.value),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages
    return results
  }
})

const { data: categories } = useQuery({
  queryKey: [...PRODUCT_CATEGORIES],
  queryFn: getProductCategories
})

/**
 * Functions
 */
function goToCreateVicProductOption() {
  router.push({ name: 'acquisition/vic-product-option-create' })
}

function goToEditVicProductOption(id: string) {
  router.push({
    name: 'acquisition/vic-product-option-edit',
    params: { id }
  })
}

function searchTermChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function setCategory(event: CustomEvent) {
  selectedCategory.value = event.detail
}

function updateRouteQuery() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

/**
 * Watchers
 */
watch(searchTermDebounced, updateRouteQuery)
watch(selectedCategory, updateRouteQuery)
</script>

<style scoped lang="scss">
.filters {
  display: grid;
  grid-template-columns: auto auto;
  gap: var(--gl-spacing-04);
}
</style>
