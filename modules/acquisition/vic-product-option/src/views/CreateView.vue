<template>
  <FormLayout title="Criar opção de produto">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'acquisition/vic-product-option' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>

    <template #form>
      <ProductSearch label="Produto" v-model="product" />

      <SiteAccreditedNetworkSearch
        label="Agrupamento (Site Accredited Network)"
        v-model="siteAccreditedNetwork"
      />

      <WSelect
        v-if="categories"
        label="Categoria de produto"
        v-model="form.productCategory"
        @WBlur="validate('productCategory')"
        :invalid="hasError('productCategory')"
        :errorText="getErrors('productCategory')"
      >
        <option value="" selected>Selecione uma categoria</option>
        <option v-for="category in categories" :key="category.id" :value="category.value">
          {{ category.value }}
        </option>
      </WSelect>

      <WSelect
        v-if="productOptionTypes"
        label="Tipo do produto"
        v-model="form.type"
        @WBlur="validate('type')"
        :invalid="hasError('type')"
        :errorText="getErrors('type')"
      >
        <option value="">Selecione o tipo do produto</option>

        <option
          v-for="productOptionType in productOptionTypes"
          :key="productOptionType.id"
          :value="productOptionType.value"
        >
          {{ productOptionType.value }}
        </option>
      </WSelect>

      <WSelect
        v-if="accommodations"
        label="Acomodação"
        v-model="form.accommodation"
        @WBlur="validate('accommodation')"
        :invalid="hasError('accommodation')"
        :errorText="getErrors('accommodation')"
      >
        <option value="">Selecione acomodação</option>

        <option
          v-for="accommodation in accommodations"
          :key="accommodation.id"
          :value="accommodation.value"
        >
          {{ accommodation.value }}
        </option>
      </WSelect>

      <WSelect
        v-if="coverages"
        label="Cobertura"
        v-model="form.coverage"
        @WBlur="validate('coverage')"
        :invalid="hasError('coverage')"
        :errorText="getErrors('coverage')"
      >
        <option value="">Selecione a cobertura</option>

        <option v-for="coverage in coverages" :key="coverage.id" :value="coverage.value">
          {{ coverage.value }}
        </option>
      </WSelect>

      <WSwitch v-model="form.coPayment" label="Coparticipação" />

      <ProviderRepresentationSelector
        title="Top 3 Hospitais"
        subtitle="adicionar na ordem em que devem aparecer"
        v-model="form.hospitals"
        providerType="HOSPITAL"
      />

      <ProviderRepresentationSelector
        title="Top 3 Laboratórios"
        subtitle="adicionar na ordem em que devem aparecer"
        v-model="form.laboratories"
        providerType="LABORATORY"
      />
    </template>

    <template #actions>
      <WButton
        variant="cta"
        size="large"
        :disabled="saveDisabled"
        :loading="isUpdatePending"
        @click="validateAndSave"
      >
        Criar
      </WButton>
    </template>
  </FormLayout>
</template>

<script setup lang="ts">
import { computed, inject, ref, watch, type Ref } from 'vue'
import { useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { WButton, WLink, WSelect, WSwitch } from '@alice-health/wonderland-vue'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'
import { VicProductOptionForm } from '@acquisition-vic-product-option/schemas/vicProductOptionForm'
import ProviderRepresentationSelector from '@acquisition-vic-product-option/components/ProviderRepresentationSelector.vue'
import ProductSearch from '@acquisition-vic-product-option/components/ProductSearch.vue'
import SiteAccreditedNetworkSearch from '@acquisition-vic-product-option/components/SiteAccreditedNetworkSearch.vue'
import { createVicProductOption } from '@acquisition-vic-product-option/api/mutations'
import type { Form } from '@acquisition-vic-product-option/types'
import { getAccommodations } from '@acquisition-vic-product-option/api/queries/getAccommodations'
import { getCoverages } from '@acquisition-vic-product-option/api/queries/getCoverages'
import { getProductCategories } from '@acquisition-vic-product-option/api/queries/getProductCategories'
import { getProductOptionTypes } from '@acquisition-vic-product-option/api/queries/getProductOptionTypes'
import {
  PRODUCT_OPTION_TYPES,
  PRODUCT_CATEGORIES,
  ACCOMMODATIONS,
  COVERAGES
} from '@acquisition-vic-product-option/constants/queryKeys'

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<Form> = ref({
  productId: '',
  productName: '',
  siteAccreditedNetworkId: '',
  siteAccreditedNetworkName: '',
  productCategory: '',
  accommodation: '',
  coverage: '',
  type: '',
  hospitals: [],
  laboratories: [],
  coPayment: false
})

/**
 * Computed Variables
 */
const saveDisabled = computed(() => !validateAll().success)

const product = computed({
  get: () => ({ id: form.value.productId, name: form.value.productName }),
  set({ id, name }) {
    form.value.productId = id
    form.value.productName = name
  }
})

const siteAccreditedNetwork = computed({
  get: () => ({
    id: form.value.siteAccreditedNetworkId,
    name: form.value.siteAccreditedNetworkName
  }),
  set({ id, name }) {
    form.value.siteAccreditedNetworkId = id
    form.value.siteAccreditedNetworkName = name
  }
})

/**
 * Hooks
 */
const router = useRouter()

const { getErrors, hasError, validate, validateAll } = useValidation({
  formSchema: VicProductOptionForm,
  form,
  invalidInitialValue: false
})

/**
 * Queries
 */

const { data: productOptionTypes } = useQuery({
  queryKey: [...PRODUCT_OPTION_TYPES],
  queryFn: getProductOptionTypes
})

const { data: categories } = useQuery({
  queryKey: [...PRODUCT_CATEGORIES],
  queryFn: getProductCategories
})

const { data: accommodations } = useQuery({
  queryKey: [...ACCOMMODATIONS],
  queryFn: getAccommodations
})

const { data: coverages } = useQuery({
  queryKey: [...COVERAGES],
  queryFn: getCoverages
})

/**
 * Mutations
 */

const { mutate: update, isPending: isUpdatePending } = useMutation({
  mutationFn: () => createVicProductOption(form.value),
  onSuccess: () => onSuccess('criada'),
  onError: () => onError('criar')
})

/**
 * Functions
 */
function validateAndSave() {
  const schema = validateAll()

  if (schema.success) update()
}

async function onSuccess(action: string) {
  router.push({ name: 'acquisition/vic-product-option' })

  snackbar?.value?.$el.add({
    message: `Opção de produto ${action} com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} opção de produto`,
    icon: 'icAlertCircleOutlined'
  })
}

/**
 * Watchers
 */
watch(form, () => validateAll(), { deep: true })
</script>
