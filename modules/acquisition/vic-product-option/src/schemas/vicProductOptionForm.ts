import { z } from 'zod'
import { ProviderRepresentationForm } from './providerRepresentationForm'

const ProductOptionTypeSchema = z.enum(['ME', 'P'], { message: 'Selecione o tipo do produto' })
const ProductCategorySchema = z.enum(['BALANCE', 'COMFORT', 'EXCLUSIVE'], {
  message: 'Selecione uma categoria'
})
const AccommodationSchema = z.enum(['NURSERY', 'APARTMENT'], {
  message: 'Selecione a acomodação'
})
const CoverageSchema = z.enum(['NATIONAL'], { message: 'Selecione a cobertura' })

const VicProductOptionForm = z.object({
  productId: z.string().min(1),
  productName: z.string().min(1),
  productCategory: ProductCategorySchema,
  accommodation: AccommodationSchema,
  coverage: CoverageSchema,
  hospitals: z.array(ProviderRepresentationForm).length(3),
  laboratories: z.array(ProviderRepresentationForm).length(3),
  siteAccreditedNetworkId: z.string().min(1),
  coPayment: z.boolean(),
  type: ProductOptionTypeSchema
})

export { VicProductOptionForm }
