import { useQueryString } from '@alice-health/vue-hooks'
import http from '@http/index'
import type { SiteAccreditedNetwork } from '@acquisition-vic-product-option/models/SiteAccreditedNetwork'

export const getSiteAccreditedNetworks = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<SiteAccreditedNetwork[]>(
    `/vicProductOption/siteAccreditedNetwork${qs}`
  )

  return data
}
