import { useQueryString } from '@alice-health/vue-hooks'
import http from '@http/index'
import type { Product } from '@acquisition-vic-product-option/models/Product'

export const getProducts = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<Product[]>(`/vicProductOption/product${qs}`)

  return data
}
