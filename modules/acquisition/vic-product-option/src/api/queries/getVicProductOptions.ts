import { useQueryString } from '@alice-health/vue-hooks'

import type { PaginatedResponse } from '@commons/index'
import http from '@http/index'
import type { VicProductOption } from '@acquisition-vic-product-option/models'

export type VicProductOptionResponse = PaginatedResponse<VicProductOption[]>

export const getVicProductOptions = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<VicProductOptionResponse>(`/vicProductOption${qs}`)

  return data
}
