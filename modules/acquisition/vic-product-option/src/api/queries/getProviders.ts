import { useQueryString } from '@alice-health/vue-hooks'
import http from '@http/index'
import type { Provider } from '@acquisition-vic-product-option/models/Provider'

export const getProviders = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<Provider[]>(`/vicProductOption/provider${qs}`)

  return data
}
