import type { RouteR<PERSON>ordRaw, NavigationGuardWithThis } from 'vue-router'

const featureFlagsRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/feature-flags',
      name: 'feature-flags',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'feature-flags-list',
          component: async () => await import('@feature-flags/views/ListView.vue')
        },
        {
          path: 'create',
          name: 'feature-flags-create',
          component: async () => await import('@feature-flags/views/CreateView.vue')
        },
        {
          path: ':id',
          name: 'feature-flags-edit',
          component: async () => await import('@feature-flags/views/EditView.vue')
        }
      ]
    }
  ]
}

export default featureFlagsRoutes
