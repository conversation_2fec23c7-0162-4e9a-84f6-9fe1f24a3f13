import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { mapRoutesToTests } from '@commons/index'

import featureFlagsRoutes from '@feature-flags/routes'

export * from '@alice-health/unit-presets/src/testing-library'

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  const routerInstance = createRouter({
    history: createWebHistory('/'),
    routes: mapRoutesToTests(featureFlagsRoutes(() => {}))
  })

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}
