import type { ShortFlag } from '@feature-flags/api/queries/getFeatureFlags'

import { HttpResponse, http } from 'msw'

const listOfFlags: ShortFlag[] = [
  {
    id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
    namespace: 'ALICE_APP',
    active: true,
    key: 'feature key',
    isPublic: true,
    value: 'feature value',
    type: 'BOOLEAN'
  }
]

const list = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/featureConfig`, () => {
  return HttpResponse.json({
    pagination: {
      totalPages: 1,
      pageSize: 1
    },
    results: listOfFlags
  })
})

export default list
