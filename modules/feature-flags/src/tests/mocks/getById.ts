import type { Flag } from '@feature-flags/models'

import { HttpResponse, http } from 'msw'

const getById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/featureConfig/:id`,
  ({ params }) => {
    const id = params.id as string

    const flag: Flag = {
      id,
      namespace: 'ALICE_APP',
      active: true,
      key: 'feature key',
      isPublic: true,
      value: 'feature value',
      description: 'feature description',
      type: 'BOOLEAN',
      createdAt: new Date(2000, 1, 1).toISOString(),
      updatedAt: new Date(2001, 1, 1).toISOString()
    }

    return HttpResponse.json(flag)
  }
)

export default getById
