import type { PaginatedResponse } from '@commons/index'
import type { Flag } from '@feature-flags/models'

import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'

export type ShortFlag = Omit<Flag, 'createdAt' | 'updatedAt' | 'description'>
export type getFeatureFlagsResponse = PaginatedResponse<ShortFlag[]>

export const getFeatureFlags = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getFeatureFlagsResponse>(`/featureConfig${qs}`)
  return data
}
