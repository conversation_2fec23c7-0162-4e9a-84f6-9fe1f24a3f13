<template>
  <div v-if="!isLoading">
    <FormLayout>
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'feature-flags' }">
          <WLink :href="href" class="ff-edit__back">Voltar</WLink>
        </router-link>
      </template>
      <template #form>
        <WTextfield label="ID" :value="data?.id" disabled />
        <WTextfield label="Namespace" :value="data?.namespace" disabled />
        <WTextfield label="Key" :value="data?.key" disabled />
        <WTextfield label="Data de criação" :value="formatDate(data?.createdAt)" disabled />
        <WTextfield label="Última atualização" :value="formatDate(data?.updatedAt)" disabled />
        <WTextarea
          label="Description"
          v-model="form.description"
          @WBlur="validate('description')"
          :invalid="hasError('description')"
          :errorText="getErrors('description')"
        />
        <WSelect
          label="Tipo"
          v-model="form.type"
          @WBlur="validate('type')"
          :invalid="hasError('type')"
          :errorText="getErrors('type')"
          v-if="featureTypes"
        >
          <option value="" selected>Selecione um tipo</option>
          <option v-for="item in featureTypes" :key="item.id" :value="item.value">
            {{ item.value }}
          </option>
        </WSelect>
        <WTextfield
          label="Valor"
          v-model="form.value"
          @WBlur="validate('value')"
          :invalid="hasError('value')"
          :errorText="getErrors('value')"
        />

        <WSwitch v-model="form.active" label="Active" />
        <WSwitch v-model="form.isPublic" label="Public Flag" />
      </template>
      <template #actions>
        <WButton
          variant="cta"
          size="large"
          :disabled="invalid"
          @click="validateAndSave"
          :loading="saveIsPending"
        >
          Salvar
        </WButton>
        <WButton variant="secondary" size="large" @click="openDialog" :loading="removeIsPending">
          Deletar
        </WButton>
      </template>
    </FormLayout>

    <WDialog
      ref="confirm"
      dialog-title="Deletar feature flag"
      description="Tem certeza que deseja deletar?"
      dialog-id="delete"
      confirm-label="Deletar"
      cancel-label="Voltar"
      @WClosed="submit"
    />
  </div>
</template>
<script setup lang="ts">
import type { Flag } from '@feature-flags/models'
import type { Ref } from 'vue'
import type { SnackbarComponentProps } from '@commons/index'

import { computed, ref, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import {
  WTextarea,
  WTextfield,
  WSelect,
  WSwitch,
  WButton,
  WLink,
  WDialog
} from '@alice-health/wonderland-vue'

import { FormLayout } from '@commons/index'
import { getFeatureFlagById, getFeatureTypes } from '@feature-flags/api/queries'
import { updateFeatureFlag, deleteFeatureFlag } from '@feature-flags/api/mutations'
import { useValidation } from '@alice-health/vue-hooks'
import { FeatureFlagForm } from '@feature-flags/schemas/featureFlagForm'

/**
 * Custom types
 */
type Form = Omit<Flag, 'id' | 'createdAt' | 'updatedAt' | 'id' | 'namespace' | 'key'>

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<Form> = ref({
  description: '',
  type: '',
  value: '',
  active: false,
  isPublic: false
})
const confirm = ref()
const id = computed(() => route.params.id as string)

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()
const queryClient = useQueryClient()
const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: FeatureFlagForm,
  form,
  invalidInitialValue: false
})

const { data: featureTypes } = useQuery({
  queryKey: ['featureTypes'],
  queryFn: getFeatureTypes
})

const { data, isLoading } = useQuery({
  queryKey: ['featureFlagById', id.value],
  queryFn: () => getFeatureFlagById(id.value),
  select: (data) => (form.value = data)
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['updateFeatureFlag'],
  mutationFn: () => updateFeatureFlag(id.value, form.value),
  onSuccess: () => onSuccess('atualizada'),
  onError: () => onError('atualizar')
})

const { mutate: remove, isPending: removeIsPending } = useMutation({
  mutationKey: ['deleteFeatureFlag'],
  mutationFn: () => deleteFeatureFlag(id.value),
  onSuccess: () => onSuccess('deletada'),
  onError: () => onError('deletar')
})

/**
 * Functions
 */
function validateAndSave() {
  const schema = validateAll()

  if (schema.success) save()
}

function formatDate(date: string | undefined) {
  if (!date) return ''
  return new Date(date).toLocaleDateString('pt-br', { dateStyle: 'short' })
}

function openDialog() {
  confirm.value.$el.open()
}

function submit(event: CustomEvent) {
  if (event.detail === 'confirm') remove()
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} a feature flag`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess(action: string) {
  await queryClient.invalidateQueries({ queryKey: ['featureFlags'] })
  router.push({ name: 'feature-flags' })

  snackbar?.value?.$el.add({
    message: `Feature flag ${action} com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}
</script>
