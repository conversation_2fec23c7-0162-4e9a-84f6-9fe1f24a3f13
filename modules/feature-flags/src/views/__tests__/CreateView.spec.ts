import CreateView from '@feature-flags/views/CreateView.vue'
import { customRender, screen } from '@feature-flags/tests'
import { server } from '@commons/services/mockServer'
import { getNamespaces, getTypes } from '@feature-flags/tests/mocks'

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getNamespaces, getTypes))

  it('renders properly', async () => {
    const { user } = customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Nova Feature Flag'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: '<PERSON>var'
      })
    ).toBeDisabled()

    await user.selectOptions(await screen.findByRole('combobox', { name: 'Namespace' }), [
      'ALICE_APP'
    ])
    await user.type(await screen.findByRole('textbox', { name: 'Description' }), 'Teste')
    await user.type(await screen.findByRole('textbox', { name: 'Key' }), 'Teste')
    await user.type(await screen.findByRole('textbox', { name: 'Valor' }), 'Teste')
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Tipo' }), ['BOOLEAN'])
    await user.click(await screen.findByRole('checkbox', { name: 'Active' }))

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })
})
