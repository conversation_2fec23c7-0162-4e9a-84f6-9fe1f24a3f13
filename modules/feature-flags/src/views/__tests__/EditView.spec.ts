import { customRender, screen } from '@feature-flags/tests'
import { server } from '@commons/services/mockServer'
import EditView from '@feature-flags/views/EditView.vue'
import { getById, getNamespaces, getTypes } from '@feature-flags/tests/mocks'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getNamespaces, getTypes, getById))

  it('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON>'
      })
    ).toBeEnabled()

    expect(await screen.findByRole('textbox', { name: 'Namespace' })).toHaveValue('ALICE_APP')
    expect(await screen.findByRole('textbox', { name: 'ID' })).toHaveValue(id)
    expect(await screen.findByRole('textbox', { name: 'Data de criação' })).toHaveValue(
      '01/02/2000'
    )
    expect(await screen.findByRole('textbox', { name: 'Última atualização' })).toHaveValue(
      '01/02/2001'
    )
    expect(await screen.findByRole('textbox', { name: 'Description' })).toHaveValue(
      'feature description'
    )
    expect(await screen.findByRole('textbox', { name: 'Key' })).toHaveValue('feature key')
    expect(await screen.findByRole('textbox', { name: 'Valor' })).toHaveValue('feature value')
    expect(await screen.findByRole('combobox', { name: 'Tipo' })).toHaveValue('BOOLEAN')
  })
})
