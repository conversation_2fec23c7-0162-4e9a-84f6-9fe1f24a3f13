import { customRender, screen } from '@feature-flags/tests'
import { server } from '@commons/services/mockServer'
import { list } from '@feature-flags/tests/mocks'

import ListView from '@feature-flags/views/ListView.vue'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(list))

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: 'Criar'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()
    expect(await screen.findByRole('textbox')).toBeInTheDocument()

    expect(screen.getByText('ALICE_APP')).toBeInTheDocument()
    expect(screen.getByText('feature key')).toBeInTheDocument()
    expect(screen.getByText('feature value')).toBeInTheDocument()
  })
})
