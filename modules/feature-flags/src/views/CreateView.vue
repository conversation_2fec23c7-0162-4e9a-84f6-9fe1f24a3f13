<template>
  <FormLayout title="Nova Feature Flag">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'feature-flags' }">
        <WLink :href="href" class="ff-create__back">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <WSelect
        label="Namespace"
        v-model="form.namespace"
        @WChange="validate('namespace')"
        :invalid="hasError('namespace')"
        :errorText="getErrors('namespace')"
        v-if="namespaces"
      >
        <option value="" selected>Selecione uma opção</option>
        <option v-for="item in namespaces" :key="item.id" :value="item.value">
          {{ item.value }}
        </option>
      </WSelect>
      <WTextfield
        label="Key"
        v-model="form.key"
        placeholder="Escrever uma chave"
        @WBlur="validate('key')"
        :invalid="hasError('key')"
        :errorText="getErrors('key')"
      />
      <WTextarea
        label="Description"
        v-model="form.description"
        placeholder="Escrever uma descrição"
        @WBlur="validate('description')"
        :invalid="hasError('description')"
        :errorText="getErrors('description')"
      />
      <WSelect
        label="Tipo"
        v-model="form.type"
        @WChange="validate('type')"
        :invalid="hasError('type')"
        :errorText="getErrors('type')"
        v-if="featureTypes"
      >
        <option value="" selected>Selecione um tipo</option>
        <option v-for="item in featureTypes" :key="item.id" :value="item.value">
          {{ item.value }}
        </option>
      </WSelect>
      <WTextfield
        label="Valor"
        v-model="form.value"
        placeholder="Escrever um valor"
        @WBlur="validate('value')"
        :invalid="hasError('value')"
        :errorText="getErrors('value')"
      />

      <WSwitch v-model="form.active" label="Active" />
      <WSwitch v-model="form.isPublic" label="Public Flag" />
    </template>
    <template #actions>
      <WButton
        variant="cta"
        size="large"
        @click="validateAndSave"
        :loading="saveIsPending"
        :disabled="invalid"
      >
        Salvar
      </WButton>
    </template>
  </FormLayout>
</template>
<script setup lang="ts">
import type { Ref } from 'vue'
import type { Flag } from '@feature-flags/models'
import type { SnackbarComponentProps } from '@commons/index'

import { inject, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import {
  WTextarea,
  WTextfield,
  WSelect,
  WSwitch,
  WButton,
  WLink
} from '@alice-health/wonderland-vue'

import { FormLayout } from '@commons/index'
import { createFeatureFlag } from '@feature-flags/api/mutations'
import { FeatureFlagForm } from '@feature-flags/schemas/featureFlagForm'
import { useValidation } from '@alice-health/vue-hooks'
import { getFeatureNamespaces, getFeatureTypes } from '@feature-flags/api/queries'

/**
 * Custom types
 */
type Form = Omit<Flag, 'id' | 'createdAt' | 'updatedAt'>

/**
 * Refs
 */
const form: Ref<Form> = ref({
  namespace: '',
  key: '',
  description: '',
  type: '',
  value: '',
  active: false,
  isPublic: false
})

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: FeatureFlagForm,
  form
})

const router = useRouter()
const queryClient = useQueryClient()

const { data: namespaces } = useQuery({
  queryKey: ['namespaces'],
  queryFn: getFeatureNamespaces
})

const { data: featureTypes } = useQuery({
  queryKey: ['featureTypes'],
  queryFn: getFeatureTypes
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createFeatureFlag'],
  mutationFn: () => createFeatureFlag(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */
function validateAndSave() {
  const schema = validateAll()

  if (schema.success) save()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar a feature flag',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['featureFlags'] })
  router.push({ name: 'feature-flags' })
  snackbar?.value?.$el.add({
    message: 'Feature flag cadastrada com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
