<template>
  <ListLayout>
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="onCreate">Criar</WButton>
    </template>

    <template #list>
      <WListControllers
        has-pagination
        margin="none"
        has-items-per-page-select
        input-placeholder="Busca pelo Namespace ou Key"
        :hide-input="false"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WPaginationNext="setNext"
        @WInputChange="searchChange"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
          </WSelect>
        </div>
      </WListControllers>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.namespace" size="medium">
              {{ item.namespace }}
            </WTableBodyCell>
            <WTableBodyCell style="word-break: break-all" :width="columnWidthMap.key" size="medium">
              {{ item.key }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.type" size="medium">
              <WTag variant="primary" :label="item.type" color="gray" />
            </WTableBodyCell>
            <WTableBodyCell
              style="word-break: break-all"
              :width="columnWidthMap.value"
              size="medium"
            >
              {{ item.value }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.active" size="medium">
              <WIcon :icon="item.active ? 'icSuccess' : 'icClose'" />
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.isPublic" size="medium">
              <WIcon :icon="item.isPublic ? 'icSuccess' : 'icClose'" />
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="editFeatureFlag(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>
<script setup lang="ts">
import { getFeatureFlags, type getFeatureFlagsResponse } from '@feature-flags/api/queries'

import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WTag,
  WIcon,
  WSelect
} from '@alice-health/wonderland-vue'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { ListLayout } from '@commons/index'

/**
 * Constants
 */
const limits = [5, 10, 15]
const columnWidthMap = {
  namespace: '200px',
  key: '400px',
  type: '150px',
  value: '500px',
  active: '',
  isPublic: '',
  edit: ''
}

const headers = [
  { id: 1, title: 'Namespace', width: columnWidthMap.namespace },
  { id: 2, title: 'Key', width: columnWidthMap.key },
  { id: 3, title: 'Type', width: columnWidthMap.type },
  { id: 4, title: 'Value', width: columnWidthMap.value },
  { id: 5, title: 'Active', width: columnWidthMap.active },
  { id: 6, title: 'isPublic', width: columnWidthMap.isPublic },
  { id: 7, width: columnWidthMap.edit }
]

/**
 * Computed Variables
 */
const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const filter = computed(() => (term.value ? objectToJsonString({ q: term.value }) : ''))

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()
const searchTerm = ref('')
const searchTermDebounced = useDebounce(searchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data } = useQuery({
  queryKey: ['featureFlags', getParams],
  queryFn: () => getFeatureFlags(getParams.value),
  select
})

/**
 * Functions
 */

const onCreate = () => {
  router.push('/feature-flags/create')
}

function editFeatureFlag(id: string) {
  router.push({
    name: 'feature-flags-edit',
    params: { id }
  })
}

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function select({ results, pagination }: getFeatureFlagsResponse) {
  totalPages.value = pagination.totalPages

  return results
}

/**
 * Lifecycle
 */

watch(searchTermDebounced, handleSearch)
</script>
