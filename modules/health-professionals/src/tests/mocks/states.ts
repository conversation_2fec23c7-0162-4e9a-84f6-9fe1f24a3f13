import { HttpResponse, http } from 'msw'
import type { ValueItem } from '@commons/index'

const listOfStates: ValueItem[] = [
  {
    value: 'AC',
    id: 'AC'
  },
  {
    value: 'AL',
    id: 'AL'
  },
  {
    value: 'AP',
    id: 'AP'
  },
  {
    value: 'AM',
    id: 'AM'
  },
  {
    value: 'BA',
    id: 'BA'
  },
  {
    value: 'CE',
    id: 'CE'
  },
  {
    value: 'DF',
    id: 'DF'
  },
  {
    value: 'ES',
    id: 'ES'
  },
  {
    value: 'GO',
    id: 'GO'
  },
  {
    value: 'MA',
    id: 'MA'
  },
  {
    value: 'MT',
    id: 'MT'
  },
  {
    value: 'MS',
    id: 'MS'
  },
  {
    value: 'MG',
    id: 'MG'
  },
  {
    value: 'PA',
    id: 'PA'
  },
  {
    value: 'PB',
    id: 'PB'
  },
  {
    value: 'PR',
    id: 'PR'
  },
  {
    value: 'PE',
    id: 'PE'
  },
  {
    value: 'PI',
    id: 'PI'
  },
  {
    value: 'RJ',
    id: 'RJ'
  },
  {
    value: 'RN',
    id: 'RN'
  },
  {
    value: 'RS',
    id: 'RS'
  },
  {
    value: 'RO',
    id: 'RO'
  },
  {
    value: 'RR',
    id: 'RR'
  },
  {
    value: 'SC',
    id: 'SC'
  },
  {
    value: 'SP',
    id: 'SP'
  },
  {
    value: 'SE',
    id: 'SE'
  },
  {
    value: 'TO',
    id: 'TO'
  }
]

const states = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/health-professionals/states`,
  () => HttpResponse.json(listOfStates)
)

export default states
