import { HttpResponse, http } from 'msw'
import type { ValueItem } from '@commons/index'

const listOfCouncilNames: ValueItem[] = [
  {
    value: 'CRBM',
    id: 'CRBM'
  },
  {
    value: 'CREF',
    id: 'CREF'
  },
  {
    value: 'COREN',
    id: 'COREN'
  },
  {
    value: 'CREFITO',
    id: 'CREFITO'
  },
  {
    value: 'CREFONO',
    id: 'CREFONO'
  },
  {
    value: 'CRM',
    id: 'CRM'
  },
  {
    value: 'CRN',
    id: 'CRN'
  },
  {
    value: 'CRO',
    id: 'CRO'
  },
  {
    value: 'CRP',
    id: 'CRP'
  },
  {
    value: 'CRMV',
    id: 'CRMV'
  },
  {
    value: 'CRF',
    id: 'CRF'
  },
  {
    value: 'CRBIO',
    id: 'CRBIO'
  }
]

const councilNames = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/health-professionals/council-names`,
  () => HttpResponse.json(listOfCouncilNames)
)

export default councilNames
