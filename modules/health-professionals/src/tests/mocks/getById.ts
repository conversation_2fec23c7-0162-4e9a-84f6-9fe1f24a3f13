import type { HealthProfessional } from '@health-professionals/models'

import { HttpResponse, http } from 'msw'

const healthProfessional: Omit<HealthProfessional, 'id'> = {
  councilName: 'CRN',
  councilNumber: '25928',
  councilState: 'SP',
  fullName: '<PERSON>. <PERSON>'
}

const getById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/health-professionals/:id`,
  ({ params }) => {
    const id = params.id as string

    return HttpResponse.json({
      id,
      ...healthProfessional
    })
  }
)

export default getById
