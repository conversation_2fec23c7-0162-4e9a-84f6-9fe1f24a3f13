import { HttpResponse, http } from 'msw'

const updateSpecialty = http.put(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/health-professionals/:id`,
  ({ params }) => {
    const id = params.id as string

    return HttpResponse.json({ id })
  }
)

const updateSpecialtyError = http.put(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/health-professionals/:id`,
  () => HttpResponse.error()
)

export { updateSpecialty, updateSpecialtyError }
