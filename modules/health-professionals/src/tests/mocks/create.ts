import { HttpResponse, http } from 'msw'

const createSpecialty = http.post(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/health-professionals`,
  () =>
    HttpResponse.json({
      id: '74ba1014-8ef9-4bef-a938-673787368200'
    })
)

const createSpecialtyError = http.post(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/health-professionals`,
  () => HttpResponse.error()
)

export { createSpecialtyError, createSpecialty }
