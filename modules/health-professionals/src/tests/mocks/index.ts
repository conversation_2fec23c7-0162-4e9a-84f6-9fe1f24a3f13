import list from './list'
import { createSpecialty as create, createSpecialtyError } from './create'
import states from './states'
import counciNames from './council-names'
import getById from './getById'
import { updateSpecialty as update, updateSpecialtyError } from './update'

const handlers = [list, create, states, counciNames, getById, update]

export {
  handlers,
  list,
  create,
  states,
  counciNames,
  getById,
  update,
  createSpecialtyError,
  updateSpecialtyError
}
