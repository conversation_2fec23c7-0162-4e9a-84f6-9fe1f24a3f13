import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const healthProfessionalsRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/health-professionals',
      name: 'health-professionals',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'health-professionals-list',
          component: async () => await import('@health-professionals/views/ListView.vue')
        },
        {
          path: 'create',
          name: 'health-professionals-create',
          component: async () => await import('@health-professionals/views/CreateView.vue')
        },
        {
          path: ':id',
          name: 'health-professionals-edit',
          component: async () => await import('@health-professionals/views/EditView.vue')
        }
      ]
    }
  ]
}

export default healthProfessionalsRoutes
