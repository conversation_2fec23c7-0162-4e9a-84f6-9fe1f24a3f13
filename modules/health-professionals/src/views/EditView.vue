<template>
  <FormView
    v-if="!isLoading"
    @submitForm="submit"
    :saveIsPeding="saveIsPending"
    :data="data"
    title="Editar profissional de saúde"
  />
</template>
<script setup lang="ts">
import type { SnackbarComponentProps } from '@commons/index'
import type { HealthProfessional } from '@health-professionals/models'

import FormView from './FormView.vue'
import { inject, ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { updateHealthProfessional } from '@health-professionals/api/mutations'
import { getHealthProfessionalsById } from '@health-professionals/api/queries'
import { formInitialValue } from '@health-professionals/constants'

const route = useRoute()

/*
 * Refs
 */
const id = computed(() => route.params.id as string)

/*
 * Refs
 */
const form = ref<HealthProfessional>({ ...formInitialValue, id: id.value })

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const router = useRouter()
const queryClient = useQueryClient()

const { data, isLoading } = useQuery({
  queryKey: ['specialtyById', id.value],
  queryFn: () => getHealthProfessionalsById(id.value)
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['updateHealthProfessional'],
  mutationFn: () => updateHealthProfessional(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */
function submit(data: HealthProfessional) {
  form.value = { ...data }

  save()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao atualizar o profisional da saúde',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['health-professionals'] })
  router.push({ name: 'health-professionals-list' })
  snackbar?.value?.$el.add({
    message: 'Profissional da saúde atualizado com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
