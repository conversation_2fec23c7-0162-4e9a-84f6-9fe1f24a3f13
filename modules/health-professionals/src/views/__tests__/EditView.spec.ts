import { customRender, screen, waitFor } from '@health-professionals/tests'
import { server } from '@commons/services/mockServer'
import {
  update,
  getById,
  counciNames,
  states,
  updateSpecialtyError
} from '@health-professionals/tests/mocks'

import EditView from '@health-professionals/views/EditView.vue'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

const mockPush = vi.fn()

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: vi.fn(() => ({
      push: mockPush
    })),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterEach(() => {
    server.resetHandlers()
    vi.clearAllMocks()
  })
  afterAll(() => server.close())
  beforeEach(() => server.use(counciNames, states, getById, update))

  it('should edit health professionals properly', async () => {
    const { user } = customRender(EditView)

    expect(
      await screen.findByRole('heading', {
        name: 'Editar profissional de saúde'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()

    expect(await screen.findByRole('combobox', { name: 'Estado' })).toHaveValue('SP')
    expect(await screen.findByRole('combobox', { name: 'Conselho' })).toHaveValue('CRN')
    expect(await screen.findByRole('textbox', { name: 'Nome completo' })).toHaveValue(
      'Dr. José da Silva'
    )
    expect(await screen.findByRole('textbox', { name: 'Nro conselho' })).toHaveValue('25928')

    await user.click(await screen.findByRole('button', { name: 'Salvar' }))

    await waitFor(() =>
      expect(mockPush).toHaveBeenCalledWith({ name: 'health-professionals-list' })
    )
  })

  it('should stay in health professionals form when update fails', async () => {
    server.use(updateSpecialtyError)
    const { user } = customRender(EditView)

    await user.click(await screen.findByRole('button', { name: 'Salvar' }))

    await waitFor(() => expect(mockPush).not.toHaveBeenCalledWith())
  })
})
