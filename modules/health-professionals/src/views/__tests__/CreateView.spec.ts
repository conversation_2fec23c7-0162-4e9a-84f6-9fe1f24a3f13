import { customRender, screen, waitFor } from '@health-professionals/tests'
import { server } from '@commons/services/mockServer'
import {
  create,
  counciNames,
  states,
  createSpecialtyError
} from '@health-professionals/tests/mocks'

import CreateView from '@health-professionals/views/CreateView.vue'

const mockPush = vi.fn()

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: vi.fn(() => ({
      push: mockPush
    }))
  }
})

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(counciNames, states, create))

  it('should create health professionals properly', async () => {
    const { user } = customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Novo profissional de saúde'
      })
    ).toBeInTheDocument()

    const salvar = await screen.findByRole('button', { name: 'Salvar' })

    expect(salvar).toBeDisabled()

    await user.selectOptions(await screen.findByRole('combobox', { name: 'Estado' }), ['SP'])
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Conselho' }), ['CRN'])
    await user.type(await screen.findByRole('textbox', { name: 'Nome completo' }), 'Teste')
    await user.type(await screen.findByRole('textbox', { name: 'Nro conselho' }), '34325')
    await user.tab()

    await waitFor(async () => expect(salvar).toBeEnabled())

    await user.click(salvar)

    await waitFor(() =>
      expect(mockPush).toHaveBeenCalledWith({ name: 'health-professionals-list' })
    )
  })

  it('should stay in the form when creation fails', async () => {
    server.use(createSpecialtyError)
    const { user } = customRender(CreateView)

    const salvar = await screen.findByRole('button', { name: 'Salvar' })

    await user.selectOptions(await screen.findByRole('combobox', { name: 'Estado' }), ['SP'])
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Conselho' }), ['CRN'])
    await user.type(await screen.findByRole('textbox', { name: 'Nome completo' }), 'Teste')
    await user.type(await screen.findByRole('textbox', { name: 'Nro conselho' }), '34325')
    await user.tab()

    await waitFor(async () => expect(salvar).toBeEnabled())

    await user.click(salvar)

    await waitFor(() => expect(mockPush).not.toHaveBeenCalledWith())
  })
})
