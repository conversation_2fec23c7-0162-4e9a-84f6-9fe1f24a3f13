import { customRender, screen, waitFor } from '@health-professionals/tests'
import { server } from '@commons/services/mockServer'
import { list } from '@health-professionals/tests/mocks'

import ListView from '@health-professionals/views/ListView.vue'

const mockPush = vi.fn()

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: vi.fn(() => ({
      push: mockPush
    }))
  }
})

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(list))

  it('renders health professionals list properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()
    expect(await screen.findByRole('textbox')).toBeInTheDocument()

    expect(screen.getByRole('cell', { name: 'LABIB MURAD' })).toBeInTheDocument()
    expect(screen.getByRole('cell', { name: 'CRM' })).toBeInTheDocument()
    expect(screen.getByRole('cell', { name: 'AC' })).toBeInTheDocument()
    expect(screen.getByRole('cell', { name: '23' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'icEdit' })).toBeInTheDocument()
  })

  it('should go to edit health professional', async () => {
    customRender(ListView)

    const editButton = await screen.findByRole('button', { name: 'icEdit' })

    expect(editButton).toBeInTheDocument()

    editButton.click()

    await waitFor(() =>
      expect(mockPush).toHaveBeenCalledWith({
        name: 'health-professionals-edit',
        params: { id: 'a3847b4d-be2f-40b6-bd14-6742fa98dd00' }
      })
    )
  })

  it('should go to create health professional', async () => {
    customRender(ListView)

    const createButton = await screen.findByRole('button', { name: 'Criar' })

    expect(createButton).toBeInTheDocument()

    createButton.click()

    await waitFor(() =>
      expect(mockPush).toHaveBeenCalledWith({
        name: 'health-professionals-create'
      })
    )
  })
})
