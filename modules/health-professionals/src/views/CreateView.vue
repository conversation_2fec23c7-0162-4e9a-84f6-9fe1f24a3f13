<template>
  <FormView @submitForm="submit" :saveIsPeding="saveIsPending" title="Novo profissional de saúde" />
</template>
<script setup lang="ts">
import type { SnackbarComponentProps } from '@commons/index'
import type { CreateHealthProfessional } from '@health-professionals/models'

import FormView from './FormView.vue'
import { inject, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { createHealthProfessional } from '@health-professionals/api/mutations'
import { formInitialValue } from '@health-professionals/constants'
/*
 * Refs
 */
const form = ref<CreateHealthProfessional>({ ...formInitialValue })

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const router = useRouter()
const queryClient = useQueryClient()

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createHealthProfessional'],
  mutationFn: () => createHealthProfessional(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */
function submit(data: CreateHealthProfessional) {
  form.value = { ...data }

  save()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar o profisional da saúde',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['health-professionals'] })
  router.push({ name: 'health-professionals-list' })
  snackbar?.value?.$el.add({
    message: 'Profissional da saúde cadastrado com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
