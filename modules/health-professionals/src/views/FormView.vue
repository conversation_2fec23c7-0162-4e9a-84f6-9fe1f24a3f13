<template>
  <div>
    <FormLayout :title="props.title">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'health-professionals-list' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>
      <template #form>
        <WTextfield
          label="Nome completo"
          v-model="form.fullName"
          placeholder="Escreva o nome completo"
          @WBlur="validate('fullName')"
          :invalid="hasError('fullName')"
          :errorText="getErrors('fullName')"
        />

        <WSelect
          v-if="!isLoadingCouncilNames"
          label="Conselho"
          v-model="form.councilName"
          placeholder="Conselho"
          @WBlur="validate('councilName')"
        >
          <option selected>Selecione o conselho</option>
          <option
            v-for="councilName in councilNames"
            :key="councilName.id"
            :value="councilName.value"
          >
            {{ councilName.value }}
          </option>
        </WSelect>

        <WSelect
          v-if="!isLoadingStates"
          label="Estado"
          v-model="form.councilState"
          placeholder="Estado"
          @WBlur="validate('councilState')"
        >
          <option selected>Selecione o estado</option>
          <option v-for="state in states" :key="state.id" :value="state.value">
            {{ state.value }}
          </option>
        </WSelect>

        <WTextfield
          label="Nro conselho"
          v-model="form.councilNumber"
          placeholder="Nro conselho"
          @WBlur="validate('councilNumber')"
          :invalid="hasError('councilNumber')"
          :errorText="getErrors('councilNumber')"
        />
      </template>
      <template #actions>
        <WButton
          :disabled="invalid"
          variant="cta"
          size="large"
          @click="validateAndSave"
          :loading="props.saveIsPending"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>
<script setup lang="ts">
import type { Ref } from 'vue'
import type { HealthProfessional } from '@health-professionals/models'

import { computed, ref } from 'vue'
import { WTextfield, WButton, WLink, WSelect } from '@alice-health/wonderland-vue'

import { FormLayout } from '@commons/index'
import { HealthProfessionalForm } from '@health-professionals/schemas/HealthProfessionalForm'
import { useValidation } from '@alice-health/vue-hooks'
import { onMounted } from 'vue'
import { formInitialValue } from '@health-professionals/constants'
import { useRoute } from 'vue-router'
import { useQuery } from '@tanstack/vue-query'
import { getStates, getCouncilNames } from '@health-professionals/api/queries'

const route = useRoute()
/**
 * Props
 */

const props = defineProps(['saveIsPending', 'title', 'data'])

/*
 * computeds
 */
const isEdit = computed(() => route.params.id as string)
/**
 * events
 */
const emit = defineEmits(['submitForm'])

/**
 * Refs
 */
const form: Ref<Partial<HealthProfessional>> = ref({ ...formInitialValue })

/**
 * Hooks
 */

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: HealthProfessionalForm,
  form,
  invalidInitialValue: !isEdit.value
})

const { data: states, isLoading: isLoadingStates } = useQuery({
  queryKey: ['get-states'],
  queryFn: () => getStates()
})

const { data: councilNames, isLoading: isLoadingCouncilNames } = useQuery({
  queryKey: ['get-council-names'],
  queryFn: () => getCouncilNames()
})

/**
 * Functions
 */

function validateAndSave() {
  const schema = validateAll()

  if (schema.success) return emit('submitForm', { ...form.value })
}

onMounted(() => {
  if (props.data) {
    form.value = { ...props.data }
  }
})
</script>
