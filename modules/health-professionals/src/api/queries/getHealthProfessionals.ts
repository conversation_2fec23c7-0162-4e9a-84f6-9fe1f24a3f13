import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'
import type { HealthProfessional } from '@health-professionals/models'

export type getHealthProfessionalsResponse = {
  results: HealthProfessional[]
  pagination: {
    totalPages: number
  }
}

export const getHealthProfessionals = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get(`/health-professionals${qs}`)
  return data
}
