<template>
  <div>
    <FormLayout :title="props.title">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'specialty-list' }">
          <WLink :href="href" class="specialty__back">Voltar</WLink>
        </router-link>
      </template>
      <template #form>
        <WTextfield
          label="Nome"
          v-model="form.name"
          placeholder="Escreva o nome da especialidade"
          @WBlur="validate('name')"
          :invalid="hasError('name')"
          :errorText="getErrors('name')"
        />

        <WTextfield
          label="URL slug"
          v-model="form.urlSlug"
          placeholder="URL slug"
          @WBlur="validate('urlSlug')"
          :invalid="hasError('urlSlug')"
          :errorText="getErrors('urlSlug')"
        />

        <section class="specialty__switchs">
          <WSwitch v-model="form.active" label="Especialidade ativa" />
          <WSwitch v-model="form.internal" label="Especialidade interna" />
          <WSwitch v-model="form.requireSpecialist" label="Exige Especialista" />
          <WSwitch
            v-model="form.generateGeneralistSubSpecialty"
            label="Gerar subespecialidade generalista"
          />
          <WSwitch v-model="form.isTherapy" label="Necessário encaminhamento (terapia)" />
        </section>

        <section class="specialty__sub" v-if="isEdit">
          <header class="specialty__header">
            <WTitle>Subespecialidades</WTitle>
            <WButton variant="primary" @click="addSubSpecialty">Adicionar</WButton>
          </header>

          <div v-for="(item, index) in form.subSpecialties" :key="item.id || index">
            <hr class="specialty__divider" />
            <div class="specialty__add-sub">
              <WTextfield
                label="Nome"
                v-model="form.subSpecialties[index].name"
                placeholder="Escreva um nome"
                @WBlur="validate(`subSpecialties.${index}.name`)"
                :invalid="hasError(`subSpecialties.${index}.name`)"
                :errorText="getErrors(`subSpecialties.${index}.name`)"
              />

              <WTextfield
                label="URL slug"
                v-model="form.subSpecialties[index].urlSlug"
                placeholder="URL slug"
                @WBlur="validate(`subSpecialties.${index}.urlSlug`)"
                :invalid="hasError(`subSpecialties.${index}.urlSlug`)"
                :errorText="getErrors(`subSpecialties.${index}.urlSlug`)"
              />
              <div class="specialty__sub-switch">
                <WSwitch v-model="form.subSpecialties[index].active" label="Ativo" />
                <WSwitch
                  v-model="form.subSpecialties[index].isAdvancedAccess"
                  label="Acesso avançado"
                />
              </div>
            </div>
          </div>
        </section>
      </template>
      <template #actions>
        <WButton
          :disabled="invalid"
          variant="cta"
          size="large"
          @click="validateAndSave"
          :loading="props.saveIsPending"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>

    <WModal
      ref="confirmModal"
      modal-title="Atenção"
      confirm-label="Confirmar"
      cancel-label="Cancelar"
      @WClosed="close"
    >
      <div>
        <WParagraph>
          Ao editar, a mesma será aplicada à todas as tarefas do PDA que utilizam essa
          especialidade.
        </WParagraph>
      </div>
    </WModal>
  </div>
</template>
<script setup lang="ts">
import type { Ref } from 'vue'
import type { SpecialtyCreate, SpecialtyEdit } from '@specialties/models'

import { computed, ref } from 'vue'
import {
  WTextfield,
  WSwitch,
  WButton,
  WLink,
  WTitle,
  WModal,
  WParagraph
} from '@alice-health/wonderland-vue'

import { FormLayout } from '@commons/index'
import { SpecialtiesForm } from '@specialties/schemas/specialtyForm'
import { useValidation } from '@alice-health/vue-hooks'
import { onMounted } from 'vue'
import { formInitialValue } from '@specialties/constants'
import { useRoute } from 'vue-router'

const route = useRoute()
const confirmModal = ref()
/**
 * Props
 */

const props = defineProps(['saveIsPending', 'title', 'data'])

/*
 * computeds
 */
const isEdit = computed(() => route.params.id as string)
/**
 * events
 */
const emit = defineEmits(['submitForm'])

/**
 * Refs
 */
const form: Ref<SpecialtyCreate | SpecialtyEdit> = ref({ ...formInitialValue })

/**
 * Hooks
 */

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: SpecialtiesForm,
  form,
  invalidInitialValue: !isEdit.value
})

/**
 * Functions
 */

function addSubSpecialty() {
  form.value = {
    ...form.value,
    subSpecialties: [
      {
        name: '',
        urlSlug: '',
        active: false,
        id: null,
        isAdvancedAccess: false
      },
      ...form.value.subSpecialties
    ]
  }
}

function validateAndSave() {
  const schema = validateAll()

  if (!schema.success) return

  if (isEdit.value) {
    confirmModal.value.$el.open()
    return
  }

  emit('submitForm', { ...form.value })
}

function close(event: CustomEvent) {
  if (event.detail !== 'confirm') return

  emit('submitForm', { ...form.value })
}

onMounted(() => {
  if (props.data) {
    form.value = JSON.parse(JSON.stringify(props.data))
  }
})
</script>
<style lang="scss">
.specialty {
  &__switchs {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  &__header {
    display: flex;
    justify-content: space-between;
    margin: var(--gl-spacing-04) 0 var(--gl-spacing-12);
  }

  &__sub {
    margin-top: var(--gl-spacing-12);
  }

  &__add-sub {
    display: grid;
    grid-template-columns: 6fr 6fr;
    gap: var(--gl-spacing-02);
    align-items: baseline;
    margin-bottom: var(--gl-spacing-04);
  }

  &__sub-switch {
    display: flex;
    gap: var(--gl-spacing-04);
    margin-top: var(--gl-spacing-02);
  }

  &__divider {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid var(--sys-color-stroke-active);
    margin: 1em 0;
    padding: 0;
  }
}
</style>
