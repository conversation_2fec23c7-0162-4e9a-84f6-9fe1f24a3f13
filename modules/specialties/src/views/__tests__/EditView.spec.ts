import { customRender, screen, waitFor } from '@specialties/tests'
import { server } from '@commons/services/mockServer'
import { getById } from '@specialties/tests/mocks'

import EditView from '@specialties/views/EditView.vue'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getById))

  it('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('heading', {
        name: 'Editar especialidade'
      })
    ).toBeInTheDocument()

    expect((await screen.findAllByRole('textbox', { name: 'Nome' })).at(0)).toHaveValue(
      'Dermatologia'
    )
    expect((await screen.findAllByRole('textbox', { name: 'URL slug' })).at(0)).toHaveValue(
      'dermatologia'
    )
    expect(await screen.findByRole('checkbox', { name: 'Especialidade ativa' })).toBeChecked()
    expect(await screen.findByRole('checkbox', { name: 'Exige Especialista' })).not.toBeChecked()
    expect(
      await screen.findByRole('checkbox', { name: 'Gerar subespecialidade generalista' })
    ).toBeChecked()
    expect(
      await screen.findByRole('checkbox', { name: 'Necessário encaminhamento (terapia)' })
    ).toBeChecked()

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })

  it('renders subspecialties properly', async () => {
    const { user } = customRender(EditView)

    expect(
      await screen.findByRole('heading', {
        name: 'Subespecialidades'
      })
    ).toBeInTheDocument()

    expect((await screen.findAllByRole('textbox', { name: 'Nome' })).at(1)).toHaveValue(
      'Sub Especialidade'
    )
    expect((await screen.findAllByRole('textbox', { name: 'URL slug' })).at(1)).toHaveValue(
      'sub-especialidade'
    )

    await user.click(await screen.findByRole('button', { name: 'Adicionar' }))

    await waitFor(async () =>
      expect(await screen.findAllByRole('textbox', { name: 'Nome' })).toHaveLength(3)
    )
  })
})
