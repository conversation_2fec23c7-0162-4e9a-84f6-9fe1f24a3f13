import { customRender, screen } from '@specialties/tests'
import { server } from '@commons/services/mockServer'
import { list } from '@specialties/tests/mocks'

import ListView from '@specialties/views/ListView.vue'

const mockPush = vi.fn()
vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: () => ({
      push: mockPush
    }),
    useRoute: () => ({
      query: {
        access: null,
        status: null,
        q: ''
      }
    })
  }
})

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(list))

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()
    expect(await screen.findByRole('textbox')).toBeInTheDocument()

    expect(screen.getAllByRole('cell', { name: 'Acompanhamento Psico' }).at(0)).toBeInTheDocument()
    expect(screen.getAllByRole('cell', { name: 'SPECIALTY' }).at(0)).toBeInTheDocument()
  })
  it('should select filters', async () => {
    const { user } = customRender(ListView)

    await user.selectOptions((await screen.findAllByRole('combobox'))[0], 'true')
    expect(mockPush).toHaveBeenCalledWith({
      query: {
        access: null,
        page: 1,
        status: 'true',
        term: '',
        q: ''
      }
    })
    await user.selectOptions((await screen.findAllByRole('combobox'))[1], 'true')
    expect(mockPush).toHaveBeenCalledWith({
      query: {
        access: 'true',
        page: 1,
        status: 'true',
        term: '',
        q: ''
      }
    })
  })
})
