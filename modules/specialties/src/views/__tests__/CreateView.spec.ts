import { customRender, screen } from '@specialties/tests'
import { server } from '@commons/services/mockServer'
import { create } from '@specialties/tests/mocks'

import CreateView from '@specialties/views/CreateView.vue'

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(create))

  it('renders properly', async () => {
    const { user } = customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Nova especialidade'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON>'
      })
    ).toBeDisabled()

    await user.type(await screen.findByRole('textbox', { name: 'Nome' }), 'Teste')
    await user.type(await screen.findByRole('textbox', { name: 'URL slug' }), 'test-test')
    await user.click(await screen.findByRole('checkbox', { name: 'Especialidade ativa' }))
    await user.click(await screen.findByRole('checkbox', { name: 'Exige Especialista' }))

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })
})
