import { customRender, screen } from '@specialties/tests'
import { server } from '@commons/services/mockServer'
import { getSuggestedProceduresSpecialty } from '@specialties/tests/mocks'

import ListSuggestedProceduresSpecialtiesView from '@specialties/views/suggested-procedures/ListSuggestedProceduresSpecialtiesView.vue'
import { http, HttpResponse } from 'msw'

const mockPush = vi.fn()
vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: () => ({
      push: mockPush
    }),
    useRoute: () => ({
      query: {
        q: ''
      }
    })
  }
})

describe('ListSuggestedProceduresSpecialtiesView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getSuggestedProceduresSpecialty))

  it('renders properly', async () => {
    customRender(ListSuggestedProceduresSpecialtiesView)

    expect(await screen.findByRole('table')).toBeInTheDocument()
    expect(await screen.findByRole('textbox')).toBeInTheDocument()

    expect(screen.getByText('Especialidade')).toBeInTheDocument()
  })

  it('should render the list of suggested procedures specialties', async () => {
    customRender(ListSuggestedProceduresSpecialtiesView)

    expect(await screen.findByText('Especialidade 1')).toBeInTheDocument()
  })

  it('should navigate to edit page when edit button is clicked', async () => {
    customRender(ListSuggestedProceduresSpecialtiesView)

    const editButton = await screen.findByRole('button', { name: /icEdit/i })

    expect(editButton).toBeInTheDocument()

    await editButton.click()

    expect(mockPush).toHaveBeenCalledWith({
      name: 'suggested-procedure-specialty-edit',
      params: { id: '1' }
    })
  })

  it('should filter the list when typing in the search box', async () => {
    customRender(ListSuggestedProceduresSpecialtiesView)
    const textbox = await screen.findByRole('textbox')
    const input = textbox as HTMLInputElement

    await input.focus()
    await input.setSelectionRange(0, 0)
    await input.dispatchEvent(new Event('input', { bubbles: true }))

    input.value = 'Especialidade 1'

    await input.dispatchEvent(new Event('input', { bubbles: true }))

    expect(await screen.findByText('Especialidade 1')).toBeInTheDocument()
  })

  it('should show empty state when there are no specialties', async () => {
    server.use(
      http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/suggestedProcedures/specialties`, () => {
        return HttpResponse.json([])
      })
    )
    customRender(ListSuggestedProceduresSpecialtiesView)

    expect(screen.queryByText('Especialidade 1')).not.toBeInTheDocument()
  })

  it('should show loading state while fetching data', async () => {
    server.use(
      http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/suggestedProcedures/specialties`, async () => {
        await new Promise(res => setTimeout(res, 100))
        return HttpResponse.json([
          { id: '1', name: 'Especialidade 1' }
        ])
      })
    )
    customRender(ListSuggestedProceduresSpecialtiesView)

    expect(await screen.findByRole('table')).toBeInTheDocument()
  })

  it('should show error state when API fails', async () => {
    server.use(
      http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/suggestedProcedures/specialties`, () => {
        return HttpResponse.error()
      })
    )
    customRender(ListSuggestedProceduresSpecialtiesView)

    expect(screen.queryByRole('table')).not.toBeInTheDocument()
  })
})
