import { customRender, screen } from '@specialties/tests'

import FormView from '../FormView.vue'

let id = ''

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('FormView', () => {
  beforeEach(() => {
    id = ''
  })

  it('should create specialty', async () => {
    const { emitted, user } = customRender(FormView, {
      props: {
        title: 'Criação'
      }
    })
    expect(
      await screen.findByRole('heading', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: '<PERSON>var'
      })
    ).toBeDisabled()

    await user.type(await screen.findByRole('textbox', { name: 'No<PERSON>' }), 'Teste')
    await user.type(await screen.findByRole('textbox', { name: 'URL slug' }), 'test-test')
    await user.click(await screen.findByRole('checkbox', { name: 'Especialidade ativa' }))
    await user.click(await screen.findByRole('checkbox', { name: 'Exige Especialista' }))

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()

    await user.click(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    )

    expect(emitted().submitForm).toEqual([
      [
        {
          active: true,
          generateGeneralistSubSpecialty: false,
          internal: false,
          isAdvancedAccess: false,
          isTherapy: false,
          name: 'Teste',
          requireSpecialist: true,
          subSpecialties: [],
          type: '',
          urlSlug: 'test-test'
        }
      ]
    ])
  })
  it('should update specialty and sub specialty', async () => {
    id = '020e934f-4864-4dd3-87ad-fb85edc8c600'

    const { emitted, user } = customRender(FormView, {
      props: {
        title: 'Edição',
        data: {
          id: '020e934f-4864-4dd3-87ad-fb85edc8c600',
          name: 'Teste',
          type: 'SPECIALTY',
          active: true,
          urlSlug: 'teste',
          isTherapy: false,
          requireSpecialist: true,
          generateGeneralistSubSpecialty: true,
          internal: true,
          subSpecialties: [
            {
              id: '5167268c-ff39-43f4-a19f-8560b3c13400',
              name: 'Sub-Teste',
              active: true,
              urlSlug: 'sub-teste',
              isAdvancedAccess: false
            },
            {
              id: 'b1b91b9b-2f46-4322-b519-0d76c269c000',
              name: 'Sub-Teste Advanced access',
              active: true,
              urlSlug: 'sub-teste-advanced-access',
              isAdvancedAccess: false
            }
          ],
          isAdvancedAccess: false
        }
      }
    })
    expect(
      await screen.findByRole('heading', {
        name: 'Edição'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()

    const specialtyTextfieldName = await screen.findByPlaceholderText(
      'Escreva o nome da especialidade'
    )
    await user.type(specialtyTextfieldName, 'updated')
    await user.click(document.body)

    await screen.findByText('Subespecialidades')

    const advancedAccessSwitch = await screen.findAllByRole('checkbox', { name: 'Acesso avançado' })
    await user.click(advancedAccessSwitch[0])

    const buttonSave = await screen.findByRole('button', { name: 'Salvar' })
    const buttonConfirm = await screen.findByText('Confirmar')

    expect(await screen.findByRole('dialog')).toBeInTheDocument()
    expect(buttonSave).toBeEnabled()

    await user.click(buttonSave)
    await user.click(buttonConfirm)

    expect(emitted().submitForm).toEqual([
      [
        {
          id: '020e934f-4864-4dd3-87ad-fb85edc8c600',
          name: 'Testeupdated',
          type: 'SPECIALTY',
          active: true,
          urlSlug: 'teste',
          isTherapy: false,
          requireSpecialist: true,
          generateGeneralistSubSpecialty: true,
          internal: true,
          subSpecialties: [
            {
              id: '5167268c-ff39-43f4-a19f-8560b3c13400',
              name: 'Sub-Teste',
              active: true,
              urlSlug: 'sub-teste',
              isAdvancedAccess: true
            },
            {
              id: 'b1b91b9b-2f46-4322-b519-0d76c269c000',
              name: 'Sub-Teste Advanced access',
              active: true,
              urlSlug: 'sub-teste-advanced-access',
              isAdvancedAccess: false
            }
          ],
          isAdvancedAccess: false
        }
      ]
    ])
  })
})
