import { customRender, screen, waitFor } from '@specialties/tests'
import { server } from '@commons/services/mockServer'
import { getSuggestedProcedureSpecialtyById, searchSuggestedProcedureSpecialty } from '@specialties/tests/mocks'
import EditSuggestedProceduresSpecialtyView from '@specialties/views/suggested-procedures/EditSuggestedProceduresSpecialtyView.vue'
import { updateSuggestedProceduresSpecialty } from '@specialties/tests/mocks/suggested-procedures/update'
import { userEvent } from '@testing-library/user-event'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

const mockPush = vi.fn()
vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: () => ({
      push: mockPush
    }),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditSuggestedProceduresSpecialtyView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getSuggestedProcedureSpecialtyById, updateSuggestedProceduresSpecialty, searchSuggestedProcedureSpecialty))

  it('should render', async () => {
    customRender(EditSuggestedProceduresSpecialtyView)

    expect(await screen.findByText('Procedimentos sugeridos para Especialidade 1')).toBeInTheDocument()
    expect(screen.getByText('Procedimento padrão')).toBeInTheDocument()
    expect(screen.getByText('Escolha o procedimento que irá vir pré preenchido no registro clínico')).toBeInTheDocument()
    
    expect(await screen.findByText('Procedimentos sugeridos')).toBeInTheDocument()
    expect(screen.getByText('Escolha até 10 procedimentos. Estes serão exibidos no registro clínico como uma sugestão para o especialista.')).toBeInTheDocument()
  })
  
  it('should select default procedure and disable autocomplete', async () => {
    const user = userEvent.setup()
    customRender(EditSuggestedProceduresSpecialtyView)

    const autocomplete = await screen.findByPlaceholderText('Selecione o procedimento padrão') as HTMLInputElement;
    expect(autocomplete).toBeInTheDocument()

    await user.click(autocomplete)
    await user.click(await screen.findByText(/procedimento 1/i))

    expect(autocomplete).toHaveValue('Procedimento 1')

    await waitFor(() => {
      expect(autocomplete).toBeDisabled()
    })
  })

  it('should render submit button', async () => {
    customRender(EditSuggestedProceduresSpecialtyView)

    await screen.findByText('Procedimentos sugeridos para Especialidade 1')

    const saveButton = await screen.findByRole('button', { name: /salvar/i })

    expect(saveButton).toBeEnabled()
  })

  it('should render return link', async () => {
    customRender(EditSuggestedProceduresSpecialtyView)

    await screen.findByText('Procedimentos sugeridos para Especialidade 1')

    const backLink = await screen.findByText(/voltar/i)

    expect(backLink).toBeInTheDocument()
  })

  it('should add a suggested procedure', async () => {
    const { user } = customRender(EditSuggestedProceduresSpecialtyView);

    await screen.findByText('Procedimentos sugeridos para Especialidade 1');

    const autocomplete = await screen.findByPlaceholderText('Selecione o procedimento sugerido');
    expect(autocomplete).toBeInTheDocument()

    await user.type(autocomplete, 'Procedimento 3');
    await user.click(await screen.findByText(/procedimento 3/i));


    expect(await screen.findByText(/procedimento 3/i)).toBeInTheDocument()
  });
})