<template>
  <FormView @submitForm="submit" :saveIsPeding="saveIsPending" title="Nova especialidade" />
</template>
<script setup lang="ts">
import type { SnackbarComponentProps } from '@commons/index'
import type { SpecialtyCreate, SpecialtyEdit } from '@specialties/models'

import FormView from './FormView.vue'
import { inject, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { createSpecialty } from '@specialties/api/mutations'
import { formInitialValue } from '@specialties/constants'
/*
 * Refs
 */
const form = ref<SpecialtyCreate>({ ...formInitialValue })

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const router = useRouter()
const queryClient = useQueryClient()

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createSpecialty'],
  mutationFn: () => createSpecialty(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */
function submit(data: SpecialtyCreate) {
  form.value = { ...data, type: 'SPECIALTY' }

  save()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar a especialidade',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess(data: SpecialtyEdit) {
  await queryClient.invalidateQueries({ queryKey: ['specialties'] })
  router.push({ name: 'specialty-edit', params: { id: data.id } })
  snackbar?.value?.$el.add({
    message: 'Especialidade cadastrada com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
