<template>
  <FormView
    @submitForm="submit"
    :saveIsPeding="saveIsPending"
    title="Editar especialidade"
    :data="data"
    v-if="!isLoading"
  />
</template>
<script setup lang="ts">
import type { SnackbarComponentProps } from '@commons/index'
import type { SpecialtyEdit } from '@specialties/models'

import FormView from './FormView.vue'
import { computed, inject, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { updateSpecialty } from '@specialties/api/mutations'
import { getSpecialtyById } from '@specialties/api/queries'
import { formInitialValue } from '@specialties/constants'

const route = useRoute()

/*
 * Refs
 */
const id = computed(() => route.params.id as string)
const form = ref<SpecialtyEdit>({ ...formInitialValue, id: id.value })

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */
const router = useRouter()
const queryClient = useQueryClient()

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['updateSpecialty'],
  mutationFn: () => updateSpecialty(form.value),
  onSuccess,
  onError
})

const { data, isLoading } = useQuery({
  queryKey: ['specialtyById', id.value],
  queryFn: () => getSpecialtyById(id.value)
})

/**
 * Functions
 */
function submit(data: SpecialtyEdit) {
  form.value = { ...data }

  save()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao atualizar a especialidade',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['specialties', 'specialtyById', id.value] })
  router.push({ name: 'specialty-list' })
  snackbar?.value?.$el.add({
    message: 'Especialidade atualizada com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
