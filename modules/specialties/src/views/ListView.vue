<template>
  <ListLayout>
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="onCreate">Criar</WButton>
    </template>

    <template #list>
      <div class="filters">
        <WSelect placeholder="Status" v-model="statusFilter">
          <option value="" selected>Status</option>
          <option value="true">Ativo</option>
          <option value="false">Inativo</option>
        </WSelect>
        <WSelect placeholder="Acesso avançado" v-model="isAdvancedAccessFilter">
          <option value="" selected>Acesso avançado</option>
          <option value="false">Sem acesso avançado</option>
          <option value="true">Apenas acesso avançado</option>
        </WSelect>
        <WListControllers
          has-pagination
          margin="none"
          has-items-per-page-select
          input-placeholder="Busca pelo nome"
          :value="term"
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WInputChange="searchChange"
          @WPaginationPrevious="setPrev"
        >
          <div slot="select-items-per-page">
            <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
              <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
            </WSelect>
          </div>
        </WListControllers>
      </div>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell size="large" v-for="header in headers" :key="header.title">
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell size="medium">
              {{ item.name }}
            </WTableBodyCell>
            <WTableBodyCell style="word-break: break-all" size="medium">
              {{ item.type }}
            </WTableBodyCell>
            <WTableBodyCell size="medium">
              <WIcon size="large" :icon="item.active ? 'icSuccess' : 'icClose'" />
            </WTableBodyCell>
            <WTableBodyCell size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="edit(item)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>
<script setup lang="ts">
import { getSpecialties, type getSpecialtiesResponse } from '@specialties/api/queries'
import type { SpecialtyListItem } from '@specialties/models'

import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WIcon,
  WSelect
} from '@alice-health/wonderland-vue'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { ListLayout } from '@commons/index'

const router = useRouter()
const route = useRoute()

/**
 * Constants
 */
const limits = [5, 10, 15]

const headers = [{ title: 'Nome' }, { title: 'Tipo' }, { title: 'Ativo' }, { title: '' }]

/**
 * Computed Variables
 */
const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const access = computed(() => (route.query?.access ? route.query.access === 'true' : null))
const status = computed(() => (route.query?.status ? route.query.status === 'true' : null))
const hasFilter = computed(() => term.value || access.value !== null || status.value !== null)
const filter = computed(() =>
  hasFilter.value
    ? objectToJsonString({ q: term.value, isAdvancedAccess: access.value, active: status.value })
    : ''
)

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const searchTerm = ref(term.value)
const searchTermDebounced = useDebounce(searchTerm, 800)
const statusFilter = ref(status.value)
const isAdvancedAccessFilter = ref(access.value)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data } = useQuery({
  queryKey: ['specialties', getParams],
  queryFn: () => getSpecialties(getParams.value),
  select
})

/**
 * Functions
 */

const onCreate = () => {
  router.push({ name: 'specialty-create' })
}

function edit(specialty: SpecialtyListItem) {
  const id = specialty.type === 'SUBSPECIALTY' ? specialty.parentSpecialtyId : specialty.id

  router.push({
    name: 'specialty-edit',
    params: { id }
  })
}

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  const access = isAdvancedAccessFilter.value
  const status = statusFilter.value
  router.push({ query: { ...route.query, page: initials.page, term, access, status } })
}

function select({ results, pagination }: getSpecialtiesResponse) {
  totalPages.value = pagination.totalPages

  return results
}

/**
 * Lifecycle
 */

watch(searchTermDebounced, handleSearch)
watch(isAdvancedAccessFilter, handleSearch)
watch(statusFilter, handleSearch)
</script>

<style lang="scss" scoped>
.filters {
  display: grid;
  grid-template-columns: 1fr 1fr 5fr;
  gap: var(--gl-spacing-04);
}
</style>
