<template>
  <ListLayout>
    <template #actions>
      <WTitle size="small" variant="heavy">Procedimentos sugeridos por especialidade</WTitle>
    </template>

    <template #list>
      <WListControllers
        has-pagination
        margin="none"
        has-items-per-page-select
        input-placeholder="Buscar pelo nome do protocolo"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WPaginationNext="setNext"
        @WInputChange="searchChange"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
          </WSelect>
        </div>
      </WListControllers>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell size="large" v-for="header in headers" :key="header.title">
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell size="medium">
              {{ item.name }}
            </WTableBodyCell>
            <WTableBodyCell size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="edit(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>
<script setup lang="ts">
import {
  getSuggestedProcedureSpecialties,
  type getSuggestedProceduresResponse
} from '@specialties/api/queries'

import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect,
  WTitle
} from '@alice-health/wonderland-vue'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { ListLayout } from '@commons/index'

const router = useRouter()
const route = useRoute()

/**
 * Constants
 */
const limits = [5, 10, 15]

const headers = [{ title: 'Especialidade' }, { title: '' }]

/**
 * Computed Variables
 */
const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const hasFilter = computed(() => term.value)
const filter = computed(() =>
  hasFilter.value
    ? objectToJsonString({ q: term.value })
    : ''
)

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const searchTerm = ref(term.value)
const searchTermDebounced = useDebounce(searchTerm, 500)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data } = useQuery({
  queryKey: ['suggested-procedure-specialties', getParams],
  queryFn: () => getSuggestedProcedureSpecialties(getParams.value),
  select: (data) => data.results
})

/**
 * Functions
 */

function edit(id: string) {
  router.push({
    name: 'suggested-procedure-specialty-edit',
    params: { id }
  })
}

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function select({ results, pagination }: getSuggestedProceduresResponse) {
  totalPages.value = pagination.totalPages

  return results
}

/**
 * Lifecycle
 */

watch(searchTermDebounced, handleSearch)
</script>
