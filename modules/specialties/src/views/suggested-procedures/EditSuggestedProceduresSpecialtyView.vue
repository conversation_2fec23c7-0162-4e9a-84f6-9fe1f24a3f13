<template>
  <div v-if="!isLoading">
    <FormLayout class="suggested-procedures-specialty">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'suggested-procedures-specialties-list' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>
      <template #form>
        <div class="suggested-procedures-specialty--form">
          <div class="suggested-procedures-specialty--form-header">
            <WTitle variant="heavy" size="small">Procedimentos sugeridos para {{ data?.specialty?.name }}</WTitle>
            <WTag v-if="!data?.specialty?.active" label="Inativo" />
          </div>
          <div class="suggested-procedures-specialty--field">
            <div class="suggested-procedures-specialty--field-title">
              <WTitle level="3" variant="heavy" size="xsmall">Procedimento padrão</WTitle>
              <WParagraph mode="secondary">Escolha o procedimento que irá vir pré preenchido no registro clínico</WParagraph>
            </div>
            <WAutocomplete
              v-model="defaultProcedureId"
              :items="suggestedProcedures"
              placeholder="Selecione o procedimento padrão"
              search-by-key="friendlyDescription"
              :disabled="typeof form.defaultProcedure?.id === 'string'"
              @click="handleProceduresAutocompleteClicked"
              @w-blur="validate('defaultProcedure')"
              @w-select="handleDefaultProcedureSelected"
              @w-change="handleDefaultProcedureChanged"
            />
  
            <ul class="suggested-procedures-specialty--list">
              <li v-if="form.defaultProcedure">
                <div class="suggested-procedures-specialty--list-item">
                  <WParagraph size="large">{{ form.defaultProcedure.friendlyDescription }}</WParagraph>
                  <div class="suggested-procedures-specialty--list-item-actions">
                    <WTag v-if="!form.defaultProcedure.active" label="Inativo" />
                    <WButton variant="tertiary" icon-button icon="icTrash" @click="handleDefaultProcedureRemoved" />
                  </div>
                </div>
              </li>
            </ul>
          </div>
  
          <div class="suggested-procedures-specialty--field">
            <div class="suggested-procedures-specialty--field-title">
              <WTitle level="3" variant="heavy" size="xsmall">Procedimentos sugeridos</WTitle>
              <WParagraph mode="secondary">Escolha até 10 procedimentos. Estes serão exibidos no registro clínico como uma sugestão para o especialista.</WParagraph>
            </div>
            <WAutocomplete
              v-model="selectedSuggestedProcedureId"
              placeholder="Selecione o procedimento sugerido"
              :items="getSuggestedProceduresFiltered"
              :loading="isLoadingSuggestedProcedures"
              search-by-key="friendlyDescription"
              @click="handleProceduresAutocompleteClicked"
              @w-select="handleSuggestedProcedureSelected"
              @w-change="handleSuggestedProcedureChanged"
              @w-blur="validate('suggestedProcedures')"
              :disabled="form.suggestedProcedures ? form?.suggestedProcedures?.length >= 10 : false"
            />
  
            <ul class="suggested-procedures-specialty--list">
              <li v-for="(suggestedProcedure, index) in form.suggestedProcedures" :key="suggestedProcedure.id">
                <div class="suggested-procedures-specialty--list-item">
                  <WParagraph size="large"> {{ index + 1 }}. {{ suggestedProcedure.friendlyDescription }}</WParagraph>
                  <div class="suggested-procedures-specialty--list-item-actions">
                    <WTag v-if="!suggestedProcedure.active" label="Inativo" />
                    <WButton variant="tertiary" icon-button icon="icTrash" @click="handleSuggestedProcedureRemoved(suggestedProcedure)" />
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </template>
      <template #actions>
        <WButton
          size="large"
          @click="validateAndSave"
          :loading="saveIsPending"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>
<script setup lang="ts">
import type { SnackbarComponentProps } from '@commons/index'

import { computed, ref, inject, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import {
  WButton,
  WLink,
  WTitle,
  WParagraph,
  WAutocomplete,
  WTag
} from '@alice-health/wonderland-vue'
import { useDebounce, useQueryString } from '@alice-health/vue-hooks'
import { FormLayout } from '@commons/index'
import { useValidation } from '@alice-health/vue-hooks'
import { SuggestedProceduresForm } from '@specialties/schemas/suggestedProceduresForm'

import { getSuggestedProcedureSpecialtyById, searchSuggestedProcedureSpecialty } from '@specialties/api/queries'
import { updateSuggestedProcedureSpecialty } from '@specialties/api/mutations'
import type { SuggestedProcedureItem, SuggestedProcedureSpecialtyItemRequest } from '@specialties/models'

/**
 * Custom types
 */
type Form = SuggestedProcedureSpecialtyItemRequest

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form = ref<Form>({
  defaultProcedure: {
    id: '',
    friendlyDescription: '',
    active: false
  },
  suggestedProcedures: [
    {
      id: '',
      friendlyDescription: '',
      active: false
    }
  ]
})

const defaultProcedureId = ref<string>('')
const selectedSuggestedProcedureId = ref<string>('')
const searchSuggestedProceduresTerm = ref('')

const { objectToJsonString } = useQueryString()

const id = computed(() => route.params.id as string)
const searchSuggestedProceduresTermDebounced = useDebounce(searchSuggestedProceduresTerm, 500)

const suggestedProceduresFilter = computed(() => {
  const filterObject: Record<string, string> = {
    ...(id.value ? { specialtyId: id.value } : {}),
    ...(searchSuggestedProceduresTermDebounced.value ? { q: searchSuggestedProceduresTermDebounced.value } : {})
  }

  return objectToJsonString(filterObject)
})

const getSuggestedProceduresParams = computed(() => ({
  filter: suggestedProceduresFilter.value
}))

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()
const queryClient = useQueryClient()
const { validateAll, validate } = useValidation({
  formSchema: SuggestedProceduresForm,
  form,
  invalidInitialValue: false
})

const { data, isLoading } = useQuery({
  queryKey: ['suggestedProcedureSpecialtyById', id.value],
  queryFn: () => getSuggestedProcedureSpecialtyById(id.value),
  select: (data) => {
    form.value = {
      defaultProcedure: data?.defaultProcedure,
      suggestedProcedures: data?.suggestedProcedures
    }

    return data
  }
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['updateSuggestedProcedureSpecialty'],
  mutationFn: () => updateSuggestedProcedureSpecialty(id.value, form.value),
  onSuccess: () => onSuccess(),
  onError: () => onError('atualizar')
})

const { data: suggestedProcedures, refetch: refetchSuggestedProcedures, isLoading: isLoadingSuggestedProcedures } = useQuery({
  queryKey: ['searchSuggestedProcedureSpecialty', getSuggestedProceduresParams],
  queryFn: () => searchSuggestedProcedureSpecialty(getSuggestedProceduresParams.value),
  select: (data) => data.results
})

const isDefaultProcedure = (item: SuggestedProcedureItem) => {
  return form.value.defaultProcedure?.id === item.id
}

const existsOnSuggestedProcedures = (item: SuggestedProcedureItem) => {
  return form.value.suggestedProcedures?.some(
    (procedure) => procedure.id === item.id
  )
}

/**
 * Functions
 */
function validateAndSave() {
  const schema = validateAll()

  if (schema.success) {
    save()
  }
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} o procedimento`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['suggested-procedure-specialty'] })
  router.push({ name: 'suggested-procedures-specialties-list' })

  snackbar?.value?.$el.add({
    message: `Procedimentos da especialidade ${data?.value?.specialty?.name} atualizados com sucesso`,
    icon: 'icCheckOutlined'
  })
}

// Default procedure

function handleDefaultProcedureSelected(item: CustomEvent<SuggestedProcedureItem>) {
  form.value.defaultProcedure = item.detail

  if (existsOnSuggestedProcedures(item.detail)) {
    form.value.suggestedProcedures = form.value.suggestedProcedures?.filter(
      (procedure) => procedure.id !== item.detail.id
    )

    form.value.defaultProcedure = item.detail
  }

  defaultProcedureId.value = ''
}

function handleDefaultProcedureChanged(item: CustomEvent<string>) {
  searchSuggestedProceduresTerm.value = item.detail
}

function handleDefaultProcedureRemoved() {
  form.value.defaultProcedure = null
  defaultProcedureId.value = ''
}

// Suggested procedures

function handleSuggestedProcedureSelected(item: CustomEvent<SuggestedProcedureItem>) {
  if (item.detail && (existsOnSuggestedProcedures(item.detail) || isDefaultProcedure(item.detail))) {
    snackbar?.value?.$el.add({
      message: 'Procedimento já adicionado à lista',
      icon: 'icAlertCircleOutlined'
    })

    return
  }
  
  if (item.detail) {
    form.value.suggestedProcedures?.push(item.detail)

  }

  selectedSuggestedProcedureId.value = item.detail.id
}

function handleSuggestedProcedureChanged(event: CustomEvent<string>) {
  if (event.detail) searchSuggestedProceduresTerm.value = event.detail
}

function handleSuggestedProcedureRemoved(item: SuggestedProcedureItem) {
  form.value.suggestedProcedures = form.value.suggestedProcedures?.filter(
    (procedure) => procedure.id !== item.id
  )
}

async function handleProceduresAutocompleteClicked() {
  searchSuggestedProceduresTerm.value = ''
}

const getSuggestedProceduresFiltered = computed(() => {
  return suggestedProcedures.value?.filter((procedure) => !form.value.suggestedProcedures?.some((p) => p.id === procedure.id) && form.value.defaultProcedure?.id !== procedure.id)
})

onMounted(() => {
  defaultProcedureId.value = form.value.defaultProcedure?.id || ''
})

watch(searchSuggestedProceduresTermDebounced, () => refetchSuggestedProcedures())
</script>

<style lang="scss" scoped>
.suggested-procedures-specialty {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-10);

  &--form {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-16);
  }

  &--form-header {
    display: flex;
    flex-direction: column;
    align-items: left;
    gap: var(--gl-spacing-02);
  }

  &--field {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-06);
  }

  &--list {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-06);
    width: 100%;

    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-actions {
        display: flex;
        align-items: center;
        gap: var(--gl-spacing-06);
      }
    }
  }
}
</style>