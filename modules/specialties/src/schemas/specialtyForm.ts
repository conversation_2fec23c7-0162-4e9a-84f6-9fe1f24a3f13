import { string, object, boolean, array } from 'zod'

export const SpecialtiesForm = object({
  name: string().min(1),
  urlSlug: string().min(1),
  id: string().optional().default(''),
  type: string().optional().default(''),
  active: boolean().default(false),
  generateGeneralistSubSpecialty: boolean().default(false),
  internal: boolean().default(false),
  isTherapy: boolean().default(false),
  requireSpecialist: boolean().default(false),
  subSpecialties: array(
    object({
      id: string().nullable(),
      name: string().min(1),
      urlSlug: string().min(1),
      active: boolean().default(false),
      isAdvancedAccess: boolean().optional().default(false)
    })
  )
})
