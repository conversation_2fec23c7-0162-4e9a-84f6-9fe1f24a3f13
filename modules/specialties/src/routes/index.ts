import type { RouteR<PERSON>ordRaw, NavigationGuardWithThis } from 'vue-router'

const specialtiesRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/specialties',
      name: 'specialties',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'specialty-list',
          component: async () => await import('@specialties/views/ListView.vue')
        },
        {
          path: 'create',
          name: 'specialty-create',
          component: async () => await import('@specialties/views/CreateView.vue')
        },
        {
          path: 'edit/:id',
          name: 'specialty-edit',
          component: async () => await import('@specialties/views/EditView.vue')
        }
      ]
    },
    {
      path: '/suggested-procedures',
      name: 'suggested-procedures',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'suggested-procedures-specialties-list',
          component: async () => await import('@specialties/views/suggested-procedures/ListSuggestedProceduresSpecialtiesView.vue')
        },
        {
          path: 'edit/:id',
          name: 'suggested-procedure-specialty-edit',
          component: async () => await import('@specialties/views/suggested-procedures/EditSuggestedProceduresSpecialtyView.vue')
        }
      ]
    }
  ]
}

export default specialtiesRoutes
