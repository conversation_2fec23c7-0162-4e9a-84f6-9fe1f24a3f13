export interface SuggestedProcedureItem {
  id: string,
  friendlyDescription: string,
  active: boolean
}

export interface SuggestedProcedureSpecialty {
  id: string
  name: string
  active: boolean
}

export interface SuggestedProcedureSpecialtyItemRequest {
  defaultProcedure?: SuggestedProcedureItem | null,
  suggestedProcedures?: SuggestedProcedureItem[]
}

export interface SuggestedProcedureSpecialtyItemResponse {
  specialty?: SuggestedProcedureSpecialty,
  defaultProcedure?: SuggestedProcedureItem | null,
  suggestedProcedures?: SuggestedProcedureItem[]
}