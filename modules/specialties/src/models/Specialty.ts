import { z } from 'zod'

import { SpecialtiesForm } from '@specialties/schemas/specialtyForm'
const SpecialtiesFormKeys = SpecialtiesForm.shape

export type Specialty = z.infer<typeof SpecialtiesForm>
export type SpecialtyFields = keyof typeof SpecialtiesFormKeys

export interface SpecialtyListItem {
  id: string
  name: string
  active: boolean
  type: 'SPECIALTY' | 'SUBSPECIALTY'
  parentSpecialtyId?: string
  isAdvancedAccess: boolean
}
export type SpecialtyCreate = Omit<Specialty, 'id' | 'specialty'>
export type SpecialtyEdit = Omit<Specialty, 'type' | 'specialty'>
