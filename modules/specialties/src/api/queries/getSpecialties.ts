import type { PaginatedResponse } from '@commons/index'
import type { SpecialtyListItem } from '@specialties/models'

import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'

export type getSpecialtiesResponse = PaginatedResponse<SpecialtyListItem[]>

export const getSpecialties = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getSpecialtiesResponse>(`/medicalSpecialty${qs}`)
  return data
}
