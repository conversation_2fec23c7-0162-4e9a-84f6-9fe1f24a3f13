import type { PaginatedResponse } from '@commons/index'
import type { SuggestedProcedureSpecialty } from '@specialties/models'

import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'

export type getSuggestedProceduresResponse = PaginatedResponse<SuggestedProcedureSpecialty[]>

export const getSuggestedProcedureSpecialties = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getSuggestedProceduresResponse>(`/suggestedProcedures/specialties${qs}`)
  return data
}
