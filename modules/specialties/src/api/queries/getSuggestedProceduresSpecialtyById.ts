import http from '@http/index'
import type { SuggestedProcedureItem, SuggestedProcedureSpecialtyItemResponse } from '@specialties/models'
import { useQueryString } from '@alice-health/vue-hooks'
import type { PaginatedResponse } from '@commons/types'

export type getSuggestedProcedureSpecialtyItemResponse = PaginatedResponse<SuggestedProcedureItem[]>

export const getSuggestedProcedureSpecialtyById = async (id: string) => {
  const { data } = await http.get<SuggestedProcedureSpecialtyItemResponse>(`/suggestedProcedures/specialty/${id}`)
  return data
}

export const searchSuggestedProcedureSpecialty = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()
  const qs = params ? `?${createQueryString(params)}` : ''
  const { data } = await http.get<getSuggestedProcedureSpecialtyItemResponse>(`/suggestedProcedures${qs}`)
  return data
}
