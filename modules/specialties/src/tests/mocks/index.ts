import list from './list'
import create from './create'
import update from './update'
import getById from './getById'
import { getSuggestedProceduresSpecialty, getSuggestedProcedureSpecialtyById, searchSuggestedProcedureSpecialty } from './suggested-procedures/get'
import { updateSuggestedProceduresSpecialty } from './suggested-procedures/update'

const handlers = [list, create, update, getById, getSuggestedProceduresSpecialty, getSuggestedProcedureSpecialtyById, updateSuggestedProceduresSpecialty]

export { handlers, list, create, update, getById, getSuggestedProceduresSpecialty, getSuggestedProcedureSpecialtyById, updateSuggestedProceduresSpecialty, searchSuggestedProcedureSpecialty }
