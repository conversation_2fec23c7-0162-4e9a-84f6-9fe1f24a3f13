import { HttpResponse, http } from 'msw'

export const getSuggestedProceduresSpecialty = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/suggestedProcedures/specialties`,
  () => {
    return HttpResponse.json({
      results: [
        {
          id: '1',
          name: 'Especialidade 1'
        }
      ]
    })
  }
)

export const getSuggestedProcedureSpecialtyById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/suggestedProcedures/specialty/:id`,
  () => {
    return HttpResponse.json({
      specialty: {
        id: '1',
        name: 'Especialidade 1'
      },
      defaultSuggestedProcedure: {
        id: '1',
        friendlyDescription: 'Procedimento 1',
        active: true
      },
      suggestedProcedures: [
        {
          id: '2',
          friendlyDescription: 'Procedimento 2',
          active: true
        }
      ]
    })
  }
)

export const searchSuggestedProcedureSpecialty = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/suggestedProcedures`,
  () => {
    return HttpResponse.json({
      results: [
        {
          id: '1',
          friendlyDescription: 'Procedimento 1',
          active: true
        },
        {
          id: '2',
          friendlyDescription: 'Procedimento 2',
          active: true
        },
        {
          id: '3',
          friendlyDescription: 'Procedimento 3',
          active: false
        }
      ]
    })
  }
)

