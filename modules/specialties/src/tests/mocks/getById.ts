import { HttpResponse, http } from 'msw'

const list = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/medicalSpecialty/:id`,
  ({ params }) => {
    const id = params.id as string

    return HttpResponse.json({
      id,
      name: 'Dermatologia',
      urlSlug: 'dermatologia',
      active: true,
      internal: false,
      requireSpecialist: false,
      generateGeneralistSubSpecialty: true,
      isTherapy: true,
      subSpecialties: [{ name: 'Sub Especialidade', urlSlug: 'sub-especialidade', active: true }]
    })
  }
)

export default list
