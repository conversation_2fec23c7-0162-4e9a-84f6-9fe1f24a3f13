import type { SpecialtyListItem } from '@specialties/models'

import { HttpResponse, http } from 'msw'

const listOfSpecialties: SpecialtyListItem[] = [
  {
    id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
    name: 'Acompanhamento Psico',
    active: true,
    type: 'SPECIALTY',
    isAdvancedAccess: true
  },
  {
    id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b01',
    name: 'Sub Psico',
    active: true,
    type: 'SUBSPECIALTY',
    parentSpecialtyId: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
    isAdvancedAccess: true
  }
]

const list = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/medicalSpecialty`, () => {
  return HttpResponse.json({
    pagination: {
      totalPages: 1,
      pageSize: 1
    },
    results: listOfSpecialties
  })
})

export default list
