import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'

export * from '@alice-health/unit-presets/src/testing-library'

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  const routerInstance = createRouter({
    history: createWebHistory('/'),
    routes: [
      { path: '/', name: 'home', component: async () => ({}) },
      {
        path: '/specialties',
        name: 'specialties',
        children: [
          {
            path: '',
            name: 'specialty-list',
            component: async () => ({}),
          },
          {
            path: 'create',
            name: 'specialty-create',
            component: async () => ({}),
          },
          {
            path: 'edit/:id',
            name: 'specialty-edit',
            component: async () => ({}),
          }
        ]
      },
      {
        path: '/suggested-procedures',
        name: 'suggested-procedures',
        children: [
          {
            path: '',
            name: 'suggested-procedures-specialties-list',
            component: async () => ({}),
          },
          {
            path: 'edit/:id',
            name: 'suggested-procedure-specialty-edit',
            component: async () => ({}),
          }
        ]
      }
    ]
  })

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}
