<script setup lang="ts">
import { computed } from 'vue'
import { WButton, WIcon, WParagraph } from '@alice-health/wonderland-vue'
import type { Contact } from '@staff/models'

const props = defineProps<{
  contact: Contact
}>()
const emits = defineEmits(['edit', 'delete'])
const icon = computed(() => (props.contact?.modality === 'REMOTE' ? 'icVideoOn' : 'icLocalPin'))
const isRemote = computed(() => props.contact?.modality === 'REMOTE')

const onClickEdit = () => {
  emits('edit', props.contact.id)
}

const onClickDelete = () => {
  emits('delete', props.contact.id)
}
</script>

<template>
  <div class="attendance-card flex-1">
    <WIcon :icon="icon" size="large" />

    <div v-if="isRemote" class="attendance-card__content">
      <WParagraph size="large"><PERSON><PERSON></WParagraph>
    </div>
    <div v-else class="attendance-card__content">
      <WParagraph variant="heavy">Presencial {{ props.contact.address?.neighborhood }}</WParagraph>
      <WParagraph class="attendance-card__content-address" size="large" ellipsis>
        {{ props.contact.address?.street }}, {{ props.contact.address?.number }}
        {{ props.contact.address?.complement }} - {{ props.contact.address?.neighborhood }} -
        {{ props.contact.address?.city }} - {{ props.contact.address?.state }} -
        {{ props.contact.address?.zipcode }}
      </WParagraph>
    </div>
    <WButton variant="secondary" size="small" icon-button icon="icTrash" @click="onClickDelete" />
    <WButton variant="secondary" size="small"  icon="icEdit" icon-button @click="onClickEdit" />
  </div>
</template>

<style scoped lang="scss">
.attendance-card {
  align-items: center;
  background-color: var(--sys-color-surface-container-active);
  border: var(--gl-border-width-xs) solid var(--sys-color-stroke-active);
  border-radius: var(--gl-border-radius-sm);
  display: inline-flex;
  gap: var(--gl-spacing-03);
  padding: var(--gl-spacing-05) var(--gl-spacing-06);

  &__content {
    display: flex;
    flex-flow: column;
    gap: var(--gl-spacing-01);
    flex: 1;

    &-address {
      max-width: 300px;
    }
  }
}
</style>
