<!-- components/AttendanceCard.vue -->
<template>
  <div class="attendance-card">
    <WIcon :icon="icon" size="large" />
    <div v-if="modality === 'REMOTE'" class="attendance-card__content">
      <WParagraph size="large">Remoto</WParagraph>
    </div>
    <div v-else class="attendance-card__content">
      <WParagraph variant="heavy">Presencial {{ address?.neighborhood }}</WParagraph>
      <WParagraph class="attendance-card__content-address" size="large" ellipsis>
        {{ fullAddress }}
      </WParagraph>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  address: {
    type: Object,
    required: false,
    default: () => ({})
  },
  modality: {
    type: String,
    required: false,
    default: 'PRESENTIAL'
  }
})

const icon = computed(() => (modality === 'REMOTE' ? 'icVideoOn' : 'icLocalPin'))

const fullAddress = computed(() => {
  if (!address) return ''
  const {
    street = '',
    number = '',
    complement = '',
    neighborhood = '',
    city = '',
    state = '',
    zipcode = ''
  } = address
  return `${street}, ${number} ${complement} - ${neighborhood} - ${city} - ${state} - ${zipcode}`
})
</script>

<style scoped>
.attendance-card {
  align-items: center;
  background-color: var(--sys-color-surface-container-active);
  border: var(--gl-border-width-xs) solid var(--sys-color-stroke-active);
  border-radius: var(--gl-border-radius-sm);
  display: inline-flex;
  gap: var(--gl-spacing-03);
  padding: var(--gl-spacing-05) var(--gl-spacing-06);
}

.attendance-card__content {
  display: flex;
  flex-flow: column;
  gap: var(--gl-spacing-01);
  flex: 1;
}

.attendance-card__content-address {
  max-width: 300px;
}
</style>
