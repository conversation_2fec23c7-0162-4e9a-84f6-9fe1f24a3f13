import { customRender, screen } from '@staff/tests'
import AttendanceCard from '../AttendanceCard.vue'

describe('AttendanceCard', () => {
  it('should render AttendanceCard with presencial modality', async () => {
    customRender(AttendanceCard, {
      props: {
        contact: {
          modality: 'PRESENTIAL'
        }
      }
    })
    expect(await screen.findByText('Presencial')).toBeInTheDocument()
  })

  it('should render AttendanceCard with remote modality', async () => {
    customRender(AttendanceCard, {
      props: {
        contact: {
          modality: 'REMOTE'
        }
      }
    })
    expect(await screen.findByText('Remoto')).toBeInTheDocument()
  })

  it('should render presential address fields correctly', async () => {
    const contact = {
      modality: 'PRESENTIAL',
      address: {
        street: 'Rua A',
        number: '10',
        complement: 'Apto 1',
        neighborhood: 'Centro',
        city: 'SP',
        state: 'SP',
        zipcode: '00000-000'
      }
    }
    customRender(AttendanceCard, { props: { contact } })
    expect(await screen.findByText(/Presencial Centro/)).toBeInTheDocument()
    expect(await screen.findByText(/Rua A, 10 Apto 1 - Centro - SP - SP - 00000-000/)).toBeInTheDocument()
  })

  it('should handle missing address gracefully', async () => {
    const contact = { modality: 'PRESENTIAL', address: undefined }
    customRender(AttendanceCard, { props: { contact } })
    expect(await screen.findByText(/Presencial/)).toBeInTheDocument()
  })

  it('should render icVideoOn icon for remote modality', async () => {
    const contact = { modality: 'REMOTE' }
    customRender(AttendanceCard, { props: { contact } })

    const icon = await screen.findByRole('img', { name: /icVideoOn/i })
    expect(icon).toBeTruthy()
  })

  it('should render icLocalPin icon for presential modality', async () => {
    const contact = { modality: 'PRESENTIAL', address: {} }
    customRender(AttendanceCard, { props: { contact } })
    const icon = await screen.findByRole('img', { name: /icLocalPin/i })
    expect(icon).toBeTruthy()
  })
}) 