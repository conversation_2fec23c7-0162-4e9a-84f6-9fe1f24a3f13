import { customRender, screen } from '@staff/tests'
import AttendanceLocationModal from '../AttendanceLocationModal.vue'

describe('AttendanceLocationModal', () => {
  it('should render', async () => {
    const { container } = customRender(AttendanceLocationModal, {
        props: {
            opened: true,
            modality: 'PRESENTIAL',
            contact: {
                id: '1',
                name: '<PERSON>',
                email: '<EMAIL>',
                phone: '1234567890',
                address: {
                    id: '1',
                    street: 'Rua A',
                    number: '10',
                    complement: 'Complemento',
                    neighborhood: 'Bairro',
                }
            }
        }
    })

    expect(container).toBeInTheDocument()
    expect(await screen.findByText('Presencial')).toBeInTheDocument()
  })
})