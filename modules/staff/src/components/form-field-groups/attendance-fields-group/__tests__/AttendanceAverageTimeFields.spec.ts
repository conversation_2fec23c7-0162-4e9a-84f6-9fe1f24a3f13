import { customRender, screen, fireEvent } from '@staff/tests'
import AttendanceAverageTimeFields from '../AttendanceAverageTimeFields.vue'

describe('AttendanceAverageTimeFields', () => {
  it('renders average time fields', async () => {
    customRender(AttendanceAverageTimeFields, { props: { scheduleAvailabilityDays: 0 } })
    expect(await screen.findByText('Tempo médio de atendimento')).toBeInTheDocument()
    expect(await screen.findByText('Quantos dias em média é a espera por uma vaga na sua agenda?')).toBeInTheDocument()
    expect(await screen.findByText('Dias')).toBeInTheDocument()
  })

  it('emits change-average-time with "increment" when increment button is clicked', async () => {
    const { emitted } = customRender(AttendanceAverageTimeFields, {
      props: { scheduleAvailabilityDays: 1 }
    })
    const buttons = await screen.findAllByRole('button')

    await fireEvent.click(buttons[1])
    expect(emitted()['change-average-time'][0]).toEqual(['increment'])
  })

  it('emits change-average-time with "decrement" when decrement button is clicked and scheduleAvailabilityDays > 0', async () => {
    const { emitted } = customRender(AttendanceAverageTimeFields, {
      props: { scheduleAvailabilityDays: 2 }
    })
    const buttons = await screen.findAllByRole('button')

    await fireEvent.click(buttons[0])
    expect(emitted()['change-average-time'][0]).toEqual(['decrement'])
  })

  it('does not emit change-average-time when decrement button is clicked and scheduleAvailabilityDays is 0', async () => {
    const { emitted } = customRender(AttendanceAverageTimeFields, {
      props: { scheduleAvailabilityDays: 0 }
    })
    const buttons = await screen.findAllByRole('button')
    await fireEvent.click(buttons[0])
    expect(emitted()['change-average-time']).toBeUndefined()
  })
}) 