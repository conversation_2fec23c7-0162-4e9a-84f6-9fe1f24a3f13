import { customRender, screen } from '@staff/tests'
import AttendanceAddressFields from '../AttendanceAddressFields.vue'

describe('AttendanceAddressFields', () => {
  it('should render', async () => {
    const { container } = customRender(AttendanceAddressFields, {
        props: {
            address: {
                street: 'Rua A',
                number: '10',
                complement: 'Apto 1',
                neighborhood: 'Centro',
            }
        }
    })

    expect(container).toBeInTheDocument()
    expect(await screen.findByRole('textbox', { name: 'Endereço (logradouro)' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: '<PERSON><PERSON><PERSON><PERSON>' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Complemento' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: '<PERSON><PERSON>' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'CEP' })).toBeInTheDocument()
  })
})