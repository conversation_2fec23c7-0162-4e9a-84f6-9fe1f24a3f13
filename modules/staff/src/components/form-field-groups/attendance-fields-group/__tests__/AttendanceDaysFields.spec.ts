import { customRender, screen, fireEvent } from '@staff/tests'
import AttendanceDaysFields from '../AttendanceDaysFields.vue'

describe('AttendanceDaysFields', () => {
  it('renders all days of the week checkboxes', async () => {
    customRender(AttendanceDaysFields, { props: { availableDays: [] } })
    
    // Check if all days are rendered
    expect(await screen.findByText('Segunda')).toBeInTheDocument()
    expect(screen.getByText('Terça')).toBeInTheDocument()
    expect(screen.getByText('Quarta')).toBeInTheDocument()
    expect(screen.getByText('Quinta')).toBeInTheDocument()
    expect(screen.getByText('Sexta')).toBeInTheDocument()
    expect(screen.getByText('Sábado')).toBeInTheDocument()
    expect(screen.getByText('Domingo')).toBeInTheDocument()
  })

  it('shows checked state for selected days', async () => {
    const availableDays = [
      { weekDay: 'MONDAY' },
      { weekDay: 'WEDNESDAY' }
    ]
    
    customRender(AttendanceDaysFields, { props: { availableDays } })
    
    // Check if Monday and Wednesday are checked
    expect(await screen.findByRole('checkbox', { name: 'Segunda' })).toBeChecked()
    expect(await screen.findByRole('checkbox', { name: 'Quarta' })).toBeChecked()
    
    // Check if other days are not checked
    expect(await screen.findByRole('checkbox', { name: 'Terça' })).not.toBeChecked()
    expect(await screen.findByRole('checkbox', { name: 'Quinta' })).not.toBeChecked()
    expect(await screen.findByRole('checkbox', { name: 'Sexta' })).not.toBeChecked()
    expect(await screen.findByRole('checkbox', { name: 'Sábado' })).not.toBeChecked()
    expect(await screen.findByRole('checkbox', { name: 'Domingo' })).not.toBeChecked()
  })

  it('emits toggle-day event when a checkbox is clicked', async () => {
    const { emitted } = customRender(AttendanceDaysFields, { 
      props: { availableDays: [] } 
    })
    
    // Click on Monday checkbox
    await fireEvent.click(await screen.findByRole('checkbox', { name: 'Segunda' }))
    
    // Check if toggle-day event was emitted with correct payload
    expect(emitted('toggle-day')).toBeTruthy()
  })
}) 