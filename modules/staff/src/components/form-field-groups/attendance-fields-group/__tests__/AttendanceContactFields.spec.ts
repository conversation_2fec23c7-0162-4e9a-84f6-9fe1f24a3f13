import { customRender, screen } from '@staff/tests'
import AttendanceContactFields from '../AttendanceContactFields.vue'

describe('AttendanceContactFields', () => {
  it('renders contact type select and info field', async () => {
    customRender(AttendanceContactFields, { props: { phones: [{ title: '', phone: '', type: 'PHONE' }], disabled: false } })
    expect(await screen.findByText('Tipo de contato')).toBeInTheDocument()
    expect(await screen.findByPlaceholderText('Informações do contato')).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: 'Adicionar tipo de contato' })).toBeInTheDocument()
  })

  it('renders multiple phone fields', async () => {
    customRender(AttendanceContactFields, {
      props: {
        phones: [
          { title: 'Residencial', phone: '1111-1111', type: 'PHONE' },
          { title: 'Celular', phone: '99999-9999', type: 'MOBILE' }
        ],
        disabled: false
      }
    })
    expect((await screen.findAllByText('Tipo de contato')).length).toBe(2)
    expect((await screen.findAllByPlaceholderText('Informações do contato')).length).toBe(2)
  })

  it('disables input fields when disabled prop is true', async () => {
    customRender(AttendanceContactFields, {
      props: {
        phones: [{ title: '', phone: '', type: 'PHONE' }],
        disabled: true
      }
    })
    const input = await screen.findByPlaceholderText('Informações do contato')
    expect(input).toBeDisabled()
  })

  it('emits add-contact event when button is clicked', async () => {
    const { emitted } = customRender(AttendanceContactFields, {
      props: { phones: [{ title: '', phone: '', type: 'PHONE' }], disabled: false }
    })
    const button = await screen.findByRole('button', { name: 'Adicionar tipo de contato' })
    await button.click()
    expect(emitted()['add-contact']).toBeTruthy()
  })
}) 