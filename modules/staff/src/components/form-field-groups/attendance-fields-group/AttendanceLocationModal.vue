<script setup lang="ts">
import { WModal } from '@alice-health/wonderland-vue'
import type { Address, AvailableDay, Contact, ModalityType } from '@staff/models'
import AttendanceContactFields from './AttendanceContactFields.vue'
import AttendanceAverageTimeFields from './AttendanceAverageTimeFields.vue'
import AttendanceDaysFields from './AttendanceDaysFields.vue'
import AttendanceAddressFields from './AttendanceAddressFields.vue'

const props = defineProps<{
  contact: Contact
  opened: boolean
  isCreateMode: boolean
  modality: keyof typeof ModalityType
}>()

const emit = defineEmits([
  'delete',
  'add-contact',
  'select-address',
  'created',
  'edited',
  'canceled'
])

const handleCloseModal = (event: CustomEvent) => {
  if (event.detail !== 'confirm') {
    emit('canceled')
    return
  }

  if (props.isCreateMode) {
    emit('created', {
      ...props.contact,
      modality: 'PRESENTIAL'
    })
  } else {
    emit('edited', props.contact.id, props.contact)
  }
}

const addNewPhone = () => {
  props.contact.phones.push({
    title: '',
    type: 'MOBILE',
    phone: ''
  })
}

const toggleDay = (day: AvailableDay) => {
  const isDayAlreadyInList = props.contact.availableDays.some((d) => d.weekDay === day.weekDay)
  if (isDayAlreadyInList) {
    props.contact.availableDays = props.contact.availableDays.filter(
      (d) => d.weekDay !== day.weekDay
    )
  } else {
    props.contact.availableDays.push(day)
  }
}

const changeAverageTime = (value: 'decrement' | 'increment') => {
  if (value === 'decrement') {
    props.contact.scheduleAvailabilityDays = props.contact.scheduleAvailabilityDays - 1
  } else {
    props.contact.scheduleAvailabilityDays = props.contact.scheduleAvailabilityDays + 1
  }
}

const updateAddress = (address: Address) => {
  props.contact.address = address
}
</script>

<template>
  <WModal
    id="in-person-modal"
    size="large"
    icon="icLocalPin"
    :modal-title="props.modality === 'REMOTE' ? 'Remoto' : 'Presencial'"
    confirm-label="Salvar"
    cancel-label="Cancelar"
    :opened="props.opened"
    @w-closed="handleCloseModal"
  >
    <div class="service-location-container">
      <div v-if="props.modality === 'PRESENTIAL'" class="service-location-container--field">
        <AttendanceAddressFields :address="props.contact?.address" @update:address="updateAddress" />
      </div>
      <div class="service-location-container--field">
        <AttendanceContactFields :phones="props.contact?.phones" @add-contact="addNewPhone" />
      </div>
      <div class="service-location-container--field">
        <AttendanceDaysFields
          :availableDays="props.contact?.availableDays"
          @toggle-day="toggleDay"
        />
      </div>
      <div class="service-location-container--field">
        <AttendanceAverageTimeFields
          :scheduleAvailabilityDays="props.contact.scheduleAvailabilityDays"
          @change-average-time="changeAverageTime"
        />
      </div>
    </div>
  </WModal>
</template>

<style scoped lang="scss">
.service-location-container {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-08);
  width: 720px;
  max-width: 720px;

  &--field {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-04);
    flex-wrap: wrap;
    margin-top: var(--gl-spacing-04);
  }

  &--field-item {
    display: flex;
    flex-direction: row;
    gap: var(--gl-spacing-04);
    flex-wrap: wrap;
  }
}

.average-waiting-time-container {
  align-items: baseline;
  gap: var(--gl-spacing-01);
}

.average-waiting-time {
  max-width: 80px;
}
</style>
