<script setup lang="ts">
import { defineProps } from 'vue'
import { WSelect, WTextfield, WButton, WTitle } from '@alice-health/wonderland-vue'
import type { PhoneNumber } from '@staff/models'

const props = defineProps<{
  disabled?: boolean
  phones: PhoneNumber[]
}>()

const emit = defineEmits(['add-contact'])
</script>

<template>
  <div class="attendance-contact-fields--header">
    <WTitle variant="heavy" size="xsmall">Contato</WTitle>
  </div>
  <div v-for="phone in props.phones" :key="phone.type">
    <div class="attendance-contact-fields--field">
      <WSelect class="flex-1" label="Tipo de contato" v-model="phone.type">
        <option selected disabled>Selecione o tipo de contato</option>
        <option value="PHONE">Fixo</option>
        <option value="MOBILE">Móvel</option>
        <option value="WHATSAPP">WhatsApp</option>
      </WSelect>
      <WTextfield
        class="flex-1"
        placeholder="Informações do contato"
        :disabled="props.disabled"
        v-model="phone.phone"
        mask-type="tel"
      />
    </div>
  </div>
  <WButton variant="secondary" icon="icAdd" @click="emit('add-contact')"
    >Adicionar tipo de contato</WButton
  >
</template>

<style scoped lang="scss">
.attendance-contact-fields {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-02);

  &--header {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-02);
  }

  &--field {
    display: flex;
    flex-direction: row;
    align-items: end;
    gap: var(--gl-spacing-02);
    width: 100%;
    flex: 1;
  }
}

.flex-1 {
  flex: 1;
}
</style>
