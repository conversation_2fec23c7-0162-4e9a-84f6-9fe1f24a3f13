<script setup lang="ts">
import { W<PERSON><PERSON>on, WTextfield, WLabel, WTitle } from '@alice-health/wonderland-vue'

const props = defineProps<{ scheduleAvailabilityDays: number }>()

const emit = defineEmits(['change-average-time'])

function increment() {
  if (props.scheduleAvailabilityDays < 30) {
    emit('change-average-time', 'increment')
  }
}
function decrement() {
  if (props.scheduleAvailabilityDays > 0) {
    emit('change-average-time', 'decrement')
  }
}
</script>

<template>
  <div class="attendance-average-time-container">
    <div class="attendance-average-time-container--header">
      <WTitle variant="heavy" size="xsmall">Tempo médio de atendimento</WTitle>
      <WLabel>Quantos dias em média é a espera por uma vaga na sua agenda?</WLabel>
    </div>
    <div class="attendance-average-time-container--input">
      <WButton variant="tertiary" icon-button icon="icDelete" size="small" @click="decrement" />
      <WTextfield
        class="attendance-average-time-container--input-field"
        placeholder="0"
        helper-text="Dias"
        v-model="props.scheduleAvailabilityDays"
        min="0"
        max="99"
      />
      <WButton variant="tertiary" icon-button icon="icAdd" size="small" @click="increment" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.attendance-average-time-container {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-04);

  &--header {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-02);
  }

  &--input {
    display: flex;
    align-items: baseline;
    gap: var(--gl-spacing-02);
    width: 150px;
  }
}
</style>
