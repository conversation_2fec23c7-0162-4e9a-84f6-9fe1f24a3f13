<script setup lang="ts">
import { WGridItem, WTextfield, WSelect } from '@alice-health/wonderland-vue'
import type { z } from 'zod'
import { healthProfessionalSchema } from '@staff/schemas/staffSignupForm'

const props = defineProps<{
  fields: z.infer<typeof healthProfessionalSchema>
}>()
</script>

<template>
  <WGridItem
    class="staff-professional-council"
    :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }"
  >
    <WTextfield
      disabled
      class="flex-1"
      label="Número do conselho"
      placeholder="Digite o número"
      v-model="fields.councilNumber"
    />

    <WSelect disabled class="flex-1" label="Estado do conselho" v-model="fields.councilState">
      <option value="" disabled selected>Selecione o estado</option>
      <option value="AC">Acre</option>
      <option value="AL">Alagoas</option>
      <option value="AP">Amapá</option>
      <option value="AM">Amazonas</option>
      <option value="BA">Bahia</option>
      <option value="CE">Ceará</option>
      <option value="DF">Distrito Federal</option>
      <option value="ES">Espírito Santo</option>
      <option value="GO">Goiás</option>
      <option value="MA">Maranhão</option>
      <option value="MT">Mato Grosso</option>
      <option value="MS">Mato Grosso do Sul</option>
      <option value="MG">Minas Gerais</option>
      <option value="PA">Pará</option>
      <option value="PB">Paraíba</option>
      <option value="PR">Paraná</option>
      <option value="PE">Pernambuco</option>
      <option value="PI">Piauí</option>
      <option value="RJ">Rio de Janeiro</option>
      <option value="RN">Rio Grande do Norte</option>
      <option value="RS">Rio Grande do Sul</option>
      <option value="RO">Rondônia</option>
      <option value="RR">Roraima</option>
      <option value="SC">Santa Catarina</option>
      <option value="SP">São Paulo</option>
      <option value="SE">Sergipe</option>
      <option value="TO">Tocantins</option>
    </WSelect>

    <WTextfield disabled class="flex-1" label="Orgão (tipo)" v-model="fields.councilType" />
  </WGridItem>
</template>

<style scoped lang="scss">
.staff-professional-council {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: var(--gl-spacing-08);
  margin-bottom: var(--gl-spacing-08);
}
</style>
