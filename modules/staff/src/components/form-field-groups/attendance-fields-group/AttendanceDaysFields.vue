<script setup lang="ts">
import { WCheckbox, WTitle } from '@alice-health/wonderland-vue'
import type { AvailableDay } from '@staff/models'

const props = defineProps<{ availableDays: AvailableDay[] }>()

const days = [
  { name: '<PERSON>', value: 'MONDAY' },
  { name: '<PERSON><PERSON><PERSON>', value: 'TUESDAY' },
  { name: 'Quarta', value: 'WEDNESDAY' },
  { name: '<PERSON>uin<PERSON>', value: 'THURSDAY' },
  { name: '<PERSON><PERSON>', value: 'FRIDAY' },
  { name: 'S<PERSON>bad<PERSON>', value: 'SATURDAY' },
  { name: '<PERSON>', value: 'SUNDAY' }
]

const emit = defineEmits(['toggle-day'])

const toggleDay = (day: string) => {
  emit('toggle-day', {
    weekDay: day
  } as AvailableDay)
}
</script>

<template>
  <div class="attendance-days-fields">
    <div class="attendance-days-fields--header">
      <WTitle variant="heavy" size="xsmall">Dias de atendimento</WTitle>
    </div>
    <div class="attendance-days-fields--field">
      <WCheckbox
        v-for="day in days"
        :key="day.value"
        :label="day.name"
        :value="day.value"
        :checked="props.availableDays?.some((d) => d.weekDay === day.value)"
        @change="toggleDay(day.value)"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.attendance-days-fields {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-02);

  &--field {
    display: flex;
    flex-direction: row;
    gap: var(--gl-spacing-02);
  }
}
</style>
