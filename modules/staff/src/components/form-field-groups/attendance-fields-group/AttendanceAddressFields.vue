<script setup lang="ts">
import { WTextfield, WAutocomplete, WTitle } from '@alice-health/wonderland-vue'
import { getAddress } from '@staff/api/queries/getAddress'
import type { AddressSearchResponse, Address } from '@staff/models'
import { getAddressDetails } from '@staff/api/queries/getAddress'
import { getUUID } from '@commons/helpers'
import { ref, watch } from 'vue'
import { useDebounce } from '@alice-health/vue-hooks'

const emit = defineEmits(['update:address'])

const props = defineProps<{
  address: Address
}>()

const addressResults = ref<AddressSearchResponse[]>([])
const addressSearchTerm = ref('')
const addressSearchTermDebounced = useDebounce(addressSearchTerm, 500)

const onSearchAddress = async (event: CustomEvent) => {
  addressSearchTerm.value = event.detail as string
}

const onSelectAddress = async (event: CustomEvent) => {
  const address = event.detail as AddressSearchResponse

  const addressDetails = await getAddressDetails(address.placeId as string)

  const updatedAddress = {
    ...props.address,
    id: getUUID(),
    street: addressDetails.street || '',
    number: addressDetails.number || '',
    complement: addressDetails.complement || '',
    neighborhood: addressDetails.neighbourhood || '',
    state: addressDetails.state || '',
    city: addressDetails.city || '',
    zipcode: addressDetails.postalCode || '',
    label: addressDetails.street || '',
    active: true,
    latitude: addressDetails.lat?.toString() || '',
    longitude: addressDetails.lng?.toString() || ''
  }

  emit('update:address', updatedAddress)
}

const searchAddress = async () => {
  const address = await getAddress({ q: addressSearchTermDebounced.value })
  addressResults.value = address
}

watch(addressSearchTermDebounced, searchAddress)
</script>

<template>
  <div class="attendance-address-fields">
    <div class="attendance-address-fields--header">
      <WTitle variant="heavy" size="xsmall">Endereço</WTitle>
    </div>
    <div class="attendance-address-fields--field">
      <WAutocomplete
        class="flex-1"
        leading-icon="icSearch"
        label="Pesquise um endereço"
        placeholder="Escreva e selecione o endereço"
        :items="addressResults"
        search-by-key="description"
        @w-change="onSearchAddress"
        @w-select="onSelectAddress"
      />
    </div>
    <div class="attendance-address-fields--field">
      <WTextfield
        class="flex-1"
        label="Endereço (logradouro)"
        placeholder="Rua ou logradouro"
        v-model="props.address.street"
        disabled
      />
    </div>
    <div class="attendance-address-fields--field">
      <WTextfield
        class="flex-1"
        label="Número"
        placeholder="Digite o número"
        v-model="props.address.number"
      />
      <WTextfield
        class="flex-1"
        label="Complemento"
        placeholder="Digite o complemento"
        v-model="props.address.complement"
      />
    </div>
    <div class="attendance-address-fields--field">
      <WTextfield
        class="flex-1"
        label="Bairro"
        disabled
        placeholder="Digite o bairro"
        v-model="props.address.neighborhood"
      />
      <WTextfield
        class="flex-1"
        label="CEP"
        disabled
        placeholder="Digite o CEP"
        v-model="props.address.zipcode"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.attendance-address-fields {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-06);
  
  &--header {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-02);
  }
  
  &--field {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: var(--gl-spacing-02);
  }
}

.flex-1 {
  flex: 1;
}
</style>
