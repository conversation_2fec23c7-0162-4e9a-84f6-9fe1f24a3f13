<script setup lang="ts">
import { inject, ref } from 'vue'
import {
  WGridItem,
  WSelect,
  WTextfield,
  WAutocomplete,
  WTitle,
  WModal,
  WButton,
  WParagraph
} from '@alice-health/wonderland-vue'
import SubSpecialtiesList from './SubSpecialtiesList.vue'
import type {
  StaffForm,
  StaffFormSpecialty,
  StaffFormProviderUnit,
  StaffFormCouncilType,
  StaffFormSubSpecialty
} from '@staff/models'
import type { SnackbarComponentProps } from '@commons/index'

const snackbar = inject<SnackbarComponentProps>('snackbar')

const props = withDefaults(
  defineProps<{
    showOrganType?: boolean
    showInternalSpecialty?: boolean
    showMemedStatus?: boolean
    showHospitals?: boolean
    disableOrganType?: boolean
    disableOrganNumber?: boolean
    disableOrganState?: boolean
    fields: StaffForm
    councilTypes: StaffFormCouncilType[]
    specialties: StaffFormSpecialty[]
    hospitals: StaffFormProviderUnit[]
    subSpecialties: StaffFormSubSpecialty[]
    internalSubSpecialties: StaffFormSubSpecialty[]
  }>(),
  {
    showMemedStatus: true,
    showHospitals: true
  }
)

const emit = defineEmits(['change-specialty', 'change-internal-specialty'])

const showSubSpecialtiesModal = ref(false)
const showInternalSubSpecialtiesModal = ref(false)

const onOpenSubSpecialtiesModal = () => {
  showSubSpecialtiesModal.value = true
}

const onCancelSubSpecialtiesModal = () => {
  showSubSpecialtiesModal.value = false
}

const onOpenInternalSubSpecialtiesModal = () => {
  showInternalSubSpecialtiesModal.value = true
}

const onCancelInternalSubSpecialtiesModal = () => {
  showInternalSubSpecialtiesModal.value = false
}

const onSelectHospital = (event: CustomEvent) => {
  const hospital = event.detail as StaffFormProviderUnit

  const alreadyExists = props.fields.providerUnits.some((unit) => unit.id === hospital.id)

  if (!hospital.id) return

  if (!alreadyExists) {
    props.fields.providerUnits.push({
      id: hospital.id,
      name: hospital.name
    })
  } else {
    snackbar?.value?.$el.add({
      message: 'Hospital já adicionado',
      icon: 'icAlertCircleOutlined'
    })
  }
}

const onRemoveHospital = (hospitalId: string) => {
  props.fields.providerUnits = props.fields.providerUnits.filter((unit) => unit.id !== hospitalId)
}

const onUpdateSubSpecialties = (subSpecialties: StaffFormSubSpecialty[]) => {
  props.fields.subSpecialties = subSpecialties
}

const onUpdateInternalSubSpecialties = (subSpecialties: StaffFormSubSpecialty[]) => {
  props.fields.internalSubSpecialties = subSpecialties
}

const onSelectSpecialty = (event: CustomEvent) => {
  const specialty = event.detail as StaffFormSpecialty
  emit('change-specialty', specialty.id)
}

const onSelectInternalSpecialty = (event: CustomEvent) => {
  const specialty = event.detail as StaffFormSpecialty
  emit('change-internal-specialty', specialty.id)
}
</script>
<template>
  <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Dados profissionais</WTitle>
  </WGridItem>
  <WGridItem
    class="staff-professional-council"
    :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }"
  >
    <WTextfield
      :disabled="props.disableOrganNumber"
      class="flex-1"
      label="Número do conselho"
      placeholder="Digite o número"
      v-model="props.fields.council.number"
    />
    <WSelect
      :disabled="props.disableOrganState"
      class="flex-1"
      label="Estado do conselho"
      v-model="props.fields.council.state"
    >
      <option value="" disabled selected>Selecione o estado</option>
      <option value="AC">Acre</option>
      <option value="AL">Alagoas</option>
      <option value="AP">Amapá</option>
      <option value="AM">Amazonas</option>
      <option value="BA">Bahia</option>
      <option value="CE">Ceará</option>
      <option value="DF">Distrito Federal</option>
      <option value="ES">Espírito Santo</option>
      <option value="GO">Goiás</option>
      <option value="MA">Maranhão</option>
      <option value="MT">Mato Grosso</option>
      <option value="MS">Mato Grosso do Sul</option>
      <option value="MG">Minas Gerais</option>
      <option value="PA">Pará</option>
      <option value="PB">Paraíba</option>
      <option value="PR">Paraná</option>
      <option value="PE">Pernambuco</option>
      <option value="PI">Piauí</option>
      <option value="RJ">Rio de Janeiro</option>
      <option value="RN">Rio Grande do Norte</option>
      <option value="RS">Rio Grande do Sul</option>
      <option value="RO">Rondônia</option>
      <option value="RR">Roraima</option>
      <option value="SC">Santa Catarina</option>
      <option value="SP">São Paulo</option>
      <option value="SE">Sergipe</option>
      <option value="TO">Tocantins</option>
    </WSelect>
    <div v-if="props.showOrganType && props.councilTypes">
      <WSelect
        :disabled="props.disableOrganType"
        class="flex-1"
        label="Orgão (tipo)"
        v-model="props.fields.council.type"
      >
        <option value="" disabled selected>Selecione o orgão</option>
        <option
          v-for="councilType in props.councilTypes"
          :key="councilType.id"
          :value="councilType.id"
        >
          {{ councilType.name }}
        </option>
      </WSelect>
    </div>
  </WGridItem>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WAutocomplete
      class="flex-1"
      label="Especialidade"
      placeholder="Selecione a especialidade"
      :items="props.specialties"
      search-by-key="name"
      v-model="props.fields.specialty"
      @w-select="onSelectSpecialty"
    />
  </WGridItem>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WButton
      :disabled="!props.specialties || !props.fields.specialty"
      variant="secondary"
      @click="onOpenSubSpecialtiesModal"
      >Ver subespecialidades</WButton
    >
  </WGridItem>

  <WGridItem
    v-if="props.showInternalSpecialty"
    class="staff-professional-specialty"
    :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }"
  >
    <WAutocomplete
      label="Especialidade interna"
      placeholder="Selecione a especialidade interna"
      :items="props.specialties"
      search-by-key="name"
      v-model="props.fields.internalSpecialty"
      @w-select="onSelectInternalSpecialty"
    />
  </WGridItem>

  <WGridItem
    v-if="props.showInternalSpecialty"
    class="staff-professional-specialty"
    :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }"
  >
    <WButton
      :disabled="!props.specialties || !props.fields.internalSpecialty"
      variant="secondary"
      @click="onOpenInternalSubSpecialtiesModal"
      >Ver subespecialidades interna</WButton
    >
  </WGridItem>

  <WGridItem v-if="props.showHospitals" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WAutocomplete
      label="Hospitais que atua (filiação)"
      placeholder="Selecione os hospitais"
      :items="props.hospitals"
      search-by-key="name"
      @w-select="onSelectHospital"
    />
    <ul class="staff-professional-hospitals">
      <li
        class="staff-professional-hospitals--item"
        v-for="hospital in props.fields.providerUnits"
        :key="hospital.id"
      >
        <WParagraph size="large">{{ hospital.name }}</WParagraph>
        <WButton
          variant="secondary"
          icon-button
          icon="icTrash"
          size="small"
          @click="onRemoveHospital(hospital.id)"
        />
      </li>
    </ul>
  </WGridItem>
  <WGridItem v-if="props.showMemedStatus" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTextfield
      label="Status MEMED"
      placeholder="{conteúdo do campo}"
      v-model="props.fields.memedStatus"
      disabled
    />
  </WGridItem>
  <WModal
    modal-id="sub-specialties-modal"
    modal-title="Subespecialidades"
    :opened="showSubSpecialtiesModal"
    confirm-label="Salvar"
    cancel-label="Cancelar"
    @w-closed="onCancelSubSpecialtiesModal"
  >
    <SubSpecialtiesList
      :current-sub-specialties="props.fields.subSpecialties || []"
      :sub-specialties="props.subSpecialties"
      @update:current-sub-specialties="onUpdateSubSpecialties"
    />
  </WModal>
  <WModal
    modal-id="internal-sub-specialties-modal"
    modal-title="Subespecialidades interna"
    :opened="showInternalSubSpecialtiesModal"
    confirm-label="Salvar"
    cancel-label="Cancelar"
    @w-closed="onCancelInternalSubSpecialtiesModal"
  >
    <SubSpecialtiesList
      :current-sub-specialties="props.fields.internalSubSpecialties || []"
      :sub-specialties="props.internalSubSpecialties"
      @update:current-sub-specialties="onUpdateInternalSubSpecialties"
    />
  </WModal>
</template>
<style scoped lang="scss">
.staff-professional-council {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: var(--gl-spacing-08);
  margin-bottom: var(--gl-spacing-08);
}
.staff-professional-hospitals {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-02);
  padding: var(--gl-spacing-02);
  overflow-y: auto;
  max-height: 200px;

  &--item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: var(--gl-spacing-08);
  }
}
</style>
