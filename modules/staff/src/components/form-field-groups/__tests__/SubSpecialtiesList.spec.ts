import { customRender, screen, fireEvent } from '@staff/tests'
import SubSpecialtiesList from '../SubSpecialtiesList.vue'
import type { StaffFormSubSpecialty } from '@staff/models'

describe('SubSpecialtiesList', () => {
  const subSpecialties: StaffFormSubSpecialty[] = [
    {
      id: '1',
      name: 'Subespecialidade 1',
      type: 'SUBSPECIALTY',
      active: true
    },
    {
      id: '2',
      name: 'Subespecialidade 2',
      type: 'SUBSPECIALTY',
      active: true
    }
  ]

  it('renders all sub-specialties checkboxes', async () => {
    customRender(SubSpecialtiesList, {
      props: { subSpecialties, currentSubSpecialties: [] }
    })
    expect(await screen.findByText('Subespecialidade 1')).toBeInTheDocument()
    expect(screen.getByText('Subespecialidade 2')).toBeInTheDocument()
  })

  it('checks the correct checkboxes based on currentSubSpecialties', async () => {
    customRender(SubSpecialtiesList, {
      props: { subSpecialties, currentSubSpecialties: [subSpecialties[1]] }
    })
    const checkbox1 = await screen.findByRole('checkbox', { name: 'Subespecialidade 1' })
    const checkbox2 = await screen.findByRole('checkbox', { name: 'Subespecialidade 2' })
    expect(checkbox1).not.toBeChecked()
    expect(checkbox2).toBeChecked()
  })

  it('emits update:currentSubSpecialties when a checkbox is clicked', async () => {
    const { emitted } = customRender(SubSpecialtiesList, {
      props: { subSpecialties, currentSubSpecialties: [] }
    })
    const checkbox1 = await screen.findByRole('checkbox', { name: 'Subespecialidade 1' })
    await fireEvent.click(checkbox1)
    const emittedEvents = emitted() as Record<string, unknown[]>
    expect(emittedEvents['update:currentSubSpecialties']).toBeTruthy()
    const firstCall = emittedEvents['update:currentSubSpecialties'][0] as unknown[]
    const payload = firstCall[0] as StaffFormSubSpecialty[]
    expect(payload).toEqual([
      expect.objectContaining({ id: '1', name: 'Subespecialidade 1' })
    ])
  })
}) 