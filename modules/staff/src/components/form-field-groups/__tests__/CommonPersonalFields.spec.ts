import { customRender, screen } from '@staff/tests'
import CommonPersonalFields from '../CommonPersonalFields.vue'

const mockFields = {
  type: 'DOCTOR',
  role: 'role',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  gender: 'MALE',
  active: true,
  nationalId: '123.456.789-00',
  birthdate: '1990-01-01',
  // other fields omitted for brevity
}

describe('CommonPersonalFields', () => {
  it('renders all personal fields', async () => {
    customRender(CommonPersonalFields, {
      props: { fields: mockFields }
    })
    expect(await screen.findByText(/dados pessoais/i)).toBeInTheDocument()
    expect(await screen.findByText(/primeiro nome/i)).toBeInTheDocument()
    expect(await screen.findByText(/sobrenome/i)).toBeInTheDocument()
    expect(await screen.findByText(/e-mail/i)).toBeInTheDocument()
    expect(await screen.findByText(/cpf/i)).toBeInTheDocument()
    expect(await screen.findByText(/data de nascimento/i)).toBeInTheDocument()
    expect(await screen.findByText(/gênero/i)).toBeInTheDocument()
    // Check for select options
    expect(screen.getByRole('option', { name: /masculino/i })).toBeInTheDocument()
    expect(screen.getByRole('option', { name: /feminino/i })).toBeInTheDocument()
    expect(screen.getByRole('option', { name: /não-binário/i })).toBeInTheDocument()
    expect(screen.getByRole('option', { name: /sem resposta/i })).toBeInTheDocument()
  })
}) 