import { customRender, screen, fireEvent } from '@staff/tests'
import StaffCategoryFields from '../StaffCategoryFields.vue'

const baseFields = {
  type: 'PITAYA',
  role: 'role',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  gender: 'MALE',
  active: true,
  attendsToOnCall: false,
  onCallPaymentMethod: '',
  tier: '',
  theoristTier: '',
  deAccreditationDate: '',
}

const staffTiers = [
  { id: 'OPT_IN', name: 'Opt-in', value: 'OPT_IN' },
  { id: 'TALENTED', name: 'Talented', value: 'TALENTED' }
]

describe('StaffCategoryFields', () => {
  it('renders main category fields', async () => {
    customRender(StaffCategoryFields, {
      props: {
        fields: { ...baseFields },
        staffTiers,
      }
    })
    expect(await screen.findByText('Categoria de atuação')).toBeInTheDocument()
    expect(await screen.findByRole('switch', { name: /Ativo no retaguarda/i })).toBeInTheDocument()
    expect(screen.getByText('Via Alice')).toBeInTheDocument()
    expect(screen.getByText('Via HI')).toBeInTheDocument()
    expect(screen.getByText(/data de descrendenciamento/i)).toBeInTheDocument()
  })

  it('renders tier selects when showTierTheoretical and showTierAttendance are true', async () => {
    customRender(StaffCategoryFields, {
      props: {
        fields: { ...baseFields },
        staffTiers,
        showTierTheoretical: true,
        showTierAttendance: true
      }
    })
    expect(await screen.findByLabelText('Tier teórico')).toBeInTheDocument()
    expect(await screen.findByLabelText('Tier de atendimento')).toBeInTheDocument()
  })

  it('does not render tier selects when showTierTheoretical and showTierAttendance are false', async () => {
    customRender(StaffCategoryFields, {
      props: {
        fields: { ...baseFields },
        staffTiers,
        showTierTheoretical: false,
        showTierAttendance: false
      }
    })
    expect(screen.queryByLabelText('Tier teórico')).not.toBeInTheDocument()
    expect(screen.queryByLabelText('Tier de atendimento')).not.toBeInTheDocument()
  })

  it('enables segmented button group when attendsToOnCall is true', async () => {
    customRender(StaffCategoryFields, {
      props: {
        fields: { ...baseFields, attendsToOnCall: true },
        staffTiers,
      }
    })
    const group = await screen.findByRole('group')
    expect(group).not.toHaveAttribute('aria-disabled', 'true')
  })
}) 