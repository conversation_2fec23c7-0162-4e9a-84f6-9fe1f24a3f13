import { customRender, screen } from '@staff/tests'
import CommonProfessionalFields from '../CommonProfessionalFields.vue'

const baseFields = {
  type: 'DOCTOR',
  role: 'role',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  gender: 'MALE',
  active: true,
  council: { number: '12345', state: 'SP', type: '6' },
  specialty: { id: '1', name: 'Cardiologia' },
  subSpecialties: [],
  internalSpecialty: { id: '2', name: '<PERSON><PERSON><PERSON><PERSON>' },
  internalSubSpecialties: [],
  providerUnits: [
    { id: 'h1', name: 'Hospital A' },
    { id: 'h2', name: 'Hospital B' }
  ],
  memedStatus: 'ATIVO'
}
const councilTypes = [
  { id: 6, name: 'CRM' },
  { id: 2, name: 'COREN' }
]
const specialties = [
  { id: '1', name: '<PERSON><PERSON>og<PERSON>' },
  { id: '2', name: '<PERSON><PERSON><PERSON><PERSON>' }
]
const hospitals = [
  { id: 'h1', name: 'Hospital A' },
  { id: 'h2', name: 'Hospital B' }
]

describe('CommonProfessionalFields', () => {
  it('renders main professional fields', async () => {
    customRender(CommonProfessionalFields, {
      props: {
        fields: { ...baseFields },
        councilTypes,
        specialties,
        hospitals,
        subSpecialties: [],
        internalSubSpecialties: []
      }
    })
    expect(await screen.findByText(/dados profissionais/i)).toBeInTheDocument()
    expect(await screen.findByText(/número do conselho/i)).toBeInTheDocument()
    expect(await screen.findByText(/estado do conselho/i)).toBeInTheDocument()
    expect(await screen.findByText(/hospitais que atua \(filiação\)/i)).toBeInTheDocument()
    expect(screen.getByText(/status memed/i)).toBeInTheDocument()
  })

  it('renders organ type select when showOrganType is true', async () => {
    customRender(CommonProfessionalFields, {
      props: {
        fields: { ...baseFields },
        councilTypes,
        specialties,
        hospitals,
        subSpecialties: [],
        internalSubSpecialties: [],
        showOrganType: true
      }
    })
    expect(await screen.findByLabelText(/orgão \(tipo\)/i)).toBeInTheDocument()
    expect(screen.getByText('CRM')).toBeInTheDocument()
    expect(screen.getByText('COREN')).toBeInTheDocument()
  })

  it('renders internal specialty fields when showInternalSpecialty is true', async () => {
    customRender(CommonProfessionalFields, {
      props: {
        fields: { ...baseFields },
        councilTypes,
        specialties,
        hospitals,
        subSpecialties: [],
        internalSubSpecialties: [],
        showInternalSpecialty: true
      }
    })
    expect(await screen.findByLabelText(/especialidade interna/i)).toBeInTheDocument()
    expect(screen.getByText(/ver subespecialidades interna/i)).toBeInTheDocument()
  })

  it('renders hospitals list', async () => {
    customRender(CommonProfessionalFields, {
      props: {
        fields: { ...baseFields },
        councilTypes,
        specialties,
        hospitals,
        subSpecialties: [],
        internalSubSpecialties: []
      }
    })
    expect(await screen.findByText('Hospital A')).toBeInTheDocument()
    expect(screen.getByText('Hospital B')).toBeInTheDocument()
  })

  it('disables subespecialidades button when specialties is empty', async () => {
    customRender(CommonProfessionalFields, {
      props: {
        fields: { ...baseFields, specialty: undefined },
        councilTypes,
        specialties: [],
        hospitals,
        subSpecialties: [],
        internalSubSpecialties: []
      }
    })
    const button = await screen.findByRole('button', { name: /ver subespecialidades/i })
    expect(button).toBeDisabled()
  })
}) 