import { customRender, screen } from '@staff/tests'
import StaffAvatarAndStatus from '../StaffAvatarAndStatus.vue'

describe('StaffAvatarAndStatus', () => {
  it('renders avatar and switches', async () => {
    customRender(StaffAvatarAndStatus, { props: { fields: { profileImageUrl: 'https://placehold.co/150', active: true } } })
    expect(await screen.findByRole('img')).toBeInTheDocument()
    expect(await screen.findByText('Ativo')).toBeInTheDocument()
  })

  it('renders app/site switch when showAppSwitch is true', async () => {
    customRender(StaffAvatarAndStatus, { props: { fields: { profileImageUrl: 'https://placehold.co/150', active: true, showOnApp: true }, showAppSwitch: true } })
    expect(await screen.findByText('Mostrar no App e Site')).toBeInTheDocument()
  })
}) 