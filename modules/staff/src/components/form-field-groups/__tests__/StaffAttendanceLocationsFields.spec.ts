import { customRender, screen } from '@staff/tests'
import StaffAttendanceLocationsFields from '../StaffAttendanceLocationsFields.vue'

const mockStaffForm = {
  type: 'HEALTH_PROFESSIONAL',
  role: 'doctor',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  gender: 'male',
  active: true,
  contacts: []
}

describe('StaffAttendanceLocationsFields', () => {
  it('renders location section and buttons', async () => {
    customRender(StaffAttendanceLocationsFields, { props: { fields: { ...mockStaffForm } } })
    expect(await screen.findByText('Locais de atendimento')).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: 'Adicionar presencial' })).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: '<PERSON><PERSON>ona<PERSON> remoto' })).toBeInTheDocument()
  })

  it('renders cards for each contact', async () => {
    const fieldsWithContacts = {
      ...mockStaffForm,
      contacts: [
        { id: '1', modality: 'REMOTE', address: {}, phones: [], availableDays: [], scheduleAvailabilityDays: 0, website: '' },
        { id: '2', modality: 'PRESENTIAL', address: {}, phones: [], availableDays: [], scheduleAvailabilityDays: 0, website: '' }
      ]
    }
    customRender(StaffAttendanceLocationsFields, { props: { fields: fieldsWithContacts } })

    const editButtons = await screen.findAllByRole('button', { name: 'icEdit' })
    expect(editButtons.length).toBeGreaterThanOrEqual(2)
  })

  it('disables "Adicionar remoto" button if a remote contact exists', async () => {
    const fieldsWithContacts = {
      ...mockStaffForm,
      contacts: [
        { id: '1', modality: 'REMOTE', address: {}, phones: [], availableDays: [], scheduleAvailabilityDays: 0, website: '' }
      ]
    }
    customRender(StaffAttendanceLocationsFields, { props: { fields: fieldsWithContacts } })
    const addRemoto = await screen.findByRole('button', { name: 'Adicionar remoto' })
    expect(addRemoto).toBeDisabled()
  })
}) 