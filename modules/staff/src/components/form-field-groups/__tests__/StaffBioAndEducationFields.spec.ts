import { customRender, screen, fireEvent } from '@staff/tests'
import StaffBioAndEducationFields from '../StaffBioAndEducationFields.vue'

const minimalFields = {
  profileBio: 'Test bio',
  education: ['Test education'],
  qualifications: ['ISO_9001'],
};

describe('StaffBioAndEducationFields', () => {
  it('renders all main fields and labels', async () => {
    customRender(StaffBioAndEducationFields, { props: { fields: minimalFields } })
    expect(await screen.findByRole('textbox', { name: 'Bio' })).toBeInTheDocument()
    expect(await screen.findByRole('textbox', { name: 'Educa<PERSON>' })).toBeInTheDocument()
    expect(await screen.findByRole('combobox', { name: 'Qualificação' })).toBeInTheDocument()
  })

  it('renders the education textarea with value', async () => {
    customRender(StaffBioAndEducationFields, { props: { fields: minimalFields } })
    const educationTextarea = await screen.findByRole('textbox', { name: '<PERSON><PERSON><PERSON>' })
    expect(educationTextarea).toHaveValue('Test education')
  })

  it('renders the qualifications select and list', async () => {
    customRender(StaffBioAndEducationFields, { props: { fields: minimalFields } })
    expect(await screen.findByText('ISO_9001')).toBeInTheDocument()
    expect(await screen.findByText('Qualificação')).toBeInTheDocument()
  })

  it('adds a qualification when selected', async () => {
    const fields = { ...minimalFields, qualifications: [] }
    customRender(StaffBioAndEducationFields, { props: { fields } })
    const select = await screen.findByLabelText('Qualificação')
    await fireEvent.update(select, 'ISO_9001')
    // The qualification should now be in the list
    expect(fields.qualifications).toContain('ISO_9001')
  })

  it('removes a qualification when trash button is clicked', async () => {
    const fields = { ...minimalFields, qualifications: ['ISO_9001'] }
    customRender(StaffBioAndEducationFields, { props: { fields } })
    const removeBtn = await screen.findByRole('button', { name: /icTrash/i })
    await fireEvent.click(removeBtn)
    expect(fields.qualifications).not.toContain('ISO_9001')
  })
}) 