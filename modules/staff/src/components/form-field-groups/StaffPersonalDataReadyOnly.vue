<script setup lang="ts">
import { WGridItem, WTextfield, WTitle, WSelect, WTextarea } from '@alice-health/wonderland-vue'
import type { z } from 'zod'
import { healthProfessionalSchema } from '@staff/schemas/staffSignupForm'

const props = defineProps<{
  fields: z.infer<typeof healthProfessionalSchema>
}>()

const columnSpan = { xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }
</script>

<template>
  <WGridItem class="staff--form__title" :column-span="columnSpan">
    <WTitle variant="heavy" size="small">Dados pessoais</WTitle>
  </WGridItem>

  <WGridItem>
    <WTextfield disabled label="Primeiro nome" v-model="props.fields.firstName" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Sobrenome" v-model="props.fields.lastName" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="E-mail" v-model="props.fields.email" />
  </WGridItem>
  <WGridItem>
    <WTextfield
      disabled
      label="CPF"
      mask-type="cpf"
      :unmask="false"
      v-model="props.fields.nationalId"
    />
  </WGridItem>
  <WGridItem>
    <WTextfield
      disabled
      label="Data de nascimento"
      mask-type="date"
      :unmask="false"
      v-model="props.fields.birthdate"
    />
  </WGridItem>
  <WGridItem>
    <WSelect disabled label="Gênero" v-model="props.fields.gender">
      <option value="" disabled>Selecione</option>
      <option value="MALE">Masculino</option>
      <option value="FEMALE">Feminino</option>
      <option value="NON_BINARY">Não-binário</option>
      <option value="NO_ANSWER">Sem resposta</option>
    </WSelect>
  </WGridItem>

  <WGridItem :column-span="columnSpan">
    <WTextarea disabled label="Bio" v-model="props.fields.profileBio" />
  </WGridItem>
  <WGridItem :column-span="columnSpan">
    <WTextarea disabled label="Educação" :value="props.fields.education" />
  </WGridItem>
</template>
