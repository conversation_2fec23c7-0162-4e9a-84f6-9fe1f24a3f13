<script setup lang="ts">
import { WGridItem, WTextfield, WSelect, WTitle } from '@alice-health/wonderland-vue'
import { type StaffForm } from '@staff/models'

const props = defineProps<{
  fields: StaffForm
}>()
</script>

<template>
  <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Dados pessoais</WTitle>
  </WGridItem>

  <WGridItem>
    <WTextfield label="Primeiro nome" v-model="props.fields.firstName" />
  </WGridItem>
  <WGridItem>
    <WTextfield label="Sobrenome" v-model="props.fields.lastName" />
  </WGridItem>

  <WGridItem>
    <WTextfield label="E-mail" v-model="props.fields.email" />
  </WGridItem>

  <WGridItem>
    <WTextfield label="CPF" mask-type="cpf" :unmask="false" v-model="props.fields.nationalId" />
  </WGridItem>

  <WGridItem>
    <WTextfield
      label="Data de nascimento"
      mask-type="date"
      :unmask="false"
      v-model="props.fields.birthdate"
    />
  </WGridItem>

  <WGridItem>
    <WSelect label="Gênero" v-model="props.fields.gender">
      <option value="" disabled selected>Selecione</option>
      <option value="MALE">Masculino</option>
      <option value="FEMALE">Feminino</option>
      <option value="NON_BINARY">Não-binário</option>
      <option value="NO_ANSWER">Sem resposta</option>
    </WSelect>
  </WGridItem>
</template>
