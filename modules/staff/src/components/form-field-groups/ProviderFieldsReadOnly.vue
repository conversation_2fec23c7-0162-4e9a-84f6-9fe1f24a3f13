<script setup lang="ts">
import { WGridItem, WTextfield, WTitle } from '@alice-health/wonderland-vue'
import { providerSchema } from '@staff/schemas/staffSignupForm'
import type { z } from 'zod'

const props = defineProps<{
  provider: z.infer<typeof providerSchema>
}>()
</script>

<template>
  <!-- Dados da provider -->
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="medium">Dados da provider</WTitle>
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Nome" v-model="props.provider.name" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="CNPJ" v-model="props.provider.cnpj" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="CNES" v-model="props.provider.cnes" />
  </WGridItem>

  <!-- Endereço -->
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Endereço</WTitle>
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Rua" v-model="props.provider.address.street" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Número" v-model="props.provider.address.number" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Complemento" v-model="props.provider.address.complement" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Bairro" v-model="props.provider.address.neighborhood" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Cidade" v-model="props.provider.address.city" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Estado" v-model="props.provider.address.state" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="CEP" v-model="props.provider.address.zipcode" />
  </WGridItem>

  <!-- Dados de pagamento -->
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Dados de pagamento</WTitle>
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Código do banco" v-model="props.provider.bankCode" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Número da agência" v-model="props.provider.agencyNumber" />
  </WGridItem>
  <WGridItem>
    <WTextfield disabled label="Número da conta" v-model="props.provider.accountNumber" />
  </WGridItem>
</template>
