<script setup lang="ts">
import { computed, ref } from 'vue'
import { WGridItem, WTitle, WButton } from '@alice-health/wonderland-vue'
import AttendanceCard from './attendance-fields-group/AttendanceCard.vue'
import {
  type StaffForm,
  ModalityType,
  type Contact,
  type PhoneNumber,
  type AvailableDay,
  type Address
} from '@staff/models'
import { getUUID } from '@commons/helpers'
import AttendanceLocationModal from './attendance-fields-group/AttendanceLocationModal.vue'

const props = defineProps<{
  fields: StaffForm
}>()

const showAttendanceModal = ref(false)
const isCreateMode = ref(false)
const currentModality = ref<keyof typeof ModalityType>('REMOTE')

const getDefaultContact = (): Contact => ({
  id: '',
  address: {
    id: '',
    street: '',
    number: '',
    complement: '',
    neighborhood: '',
    state: '',
    city: '',
    zipcode: '',
    latitude: '',
    longitude: ''
  } as Address,
  phones: [] as PhoneNumber[],
  scheduleAvailabilityDays: 0,
  modality: 'REMOTE' as keyof typeof ModalityType,
  availableDays: [] as AvailableDay[],
  website: ''
})

const contactPayload = ref<Contact>(getDefaultContact())
const contactId = ref('')

const currentContact = computed(() => {
  return isCreateMode.value ? contactPayload.value : getContactById(contactId.value)
})

const handleOnOpenModal = (type: keyof typeof ModalityType, id: string) => {
  contactId.value = id
  isCreateMode.value = false
  currentModality.value = type
  showAttendanceModal.value = true
}

const getContactById = (id: string): Contact => {
  const found = props.fields.contacts?.find((contact) => contact.id === id)
  if (!found) return getDefaultContact()

  return {
    ...getDefaultContact(),
    ...found,
    address: (found.address
      ? { ...getDefaultContact().address, ...found.address }
      : getDefaultContact().address),
    phones: (found.phones ?? []) as PhoneNumber[],
    availableDays: found.availableDays as unknown as AvailableDay[],
    modality: (found.modality ?? 'REMOTE') as keyof typeof ModalityType
  }
}

const isRemoteDisabled = computed(() => {
  return props.fields.contacts?.some((contact) => contact.modality === 'REMOTE')
})

const handleDeleteContact = (id: string) => {
  props.fields.contacts = props.fields.contacts?.filter((contact) => contact.id !== id)
  showAttendanceModal.value = false
}

const handleAddContact = (payload: Partial<Contact>) => {
  props.fields.contacts?.push({
    ...getDefaultContact(),
    ...payload,
    id: getUUID(),
    address: (payload.address
      ? { ...getDefaultContact().address, ...payload.address }
      : getDefaultContact().address) as Address,
    phones: (payload.phones ?? []) as PhoneNumber[],
    availableDays: payload.availableDays ?? [],
    modality: (payload.modality ?? 'REMOTE') as keyof typeof ModalityType
  })
}

const handleCreateNewLocation = (type: keyof typeof ModalityType) => {
  isCreateMode.value = true
  currentModality.value = type
  showAttendanceModal.value = true
}

const handleCreatedContactLocation = (payload: Partial<Contact>) => {
  props.fields.contacts?.push({
    ...getDefaultContact(),
    ...payload,
    id: getUUID(),
    address: (payload.address
      ? { ...getDefaultContact().address, ...payload.address }
      : getDefaultContact().address) as Address,
    phones: payload.phones ?? [],
    availableDays: payload.availableDays,
    modality: payload.modality
  })

  isCreateMode.value = false
  showAttendanceModal.value = false
}

const handleEditedContactLocation = (id: string, payload: Partial<Contact>) => {
  props.fields.contacts = props.fields.contacts?.map((contact) =>
    contact.id === id
      ? {
          ...contact,
          ...payload,
          address: (payload.address
            ? { ...getDefaultContact().address, ...(contact.address ?? {}), ...payload.address }
            : contact.address ?? getDefaultContact().address) as Address,
          phones: (payload.phones ?? contact.phones) as PhoneNumber[],
          availableDays: payload.availableDays ?? contact.availableDays,
          modality: (payload.modality ?? contact.modality ?? 'REMOTE') as keyof typeof ModalityType
        }
      : contact
  )

  showAttendanceModal.value = false
}

const handleCanceled = () => {
  showAttendanceModal.value = false
  isCreateMode.value = false
}
</script>
<template>
  <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Locais de atendimento</WTitle>
  </WGridItem>
  <WGridItem
    class="staff-attendance-locations-fields"
    :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }"
  >
    <WButton variant="secondary" icon="icLocalPin" @click="handleCreateNewLocation('PRESENTIAL')"
      >Adicionar presencial</WButton
    >
    <WButton
      variant="secondary"
      icon="icVideoOn"
      @click="handleCreateNewLocation('REMOTE')"
      :disabled="isRemoteDisabled"
      >Adicionar remoto</WButton
    >
  </WGridItem>
  <WGridItem class="attendance-cards" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <AttendanceCard
      v-for="contact in props.fields.contacts"
      :key="contact.id"
      :contact="contact"
      @edit="handleOnOpenModal(contact.modality as keyof typeof ModalityType, contact.id)"
      @delete="handleDeleteContact(contact.id)"
    />
    <AttendanceLocationModal
      :opened="showAttendanceModal"
      :contact="currentContact"
      :isCreateMode="isCreateMode"
      :modality="currentModality"
      @add-contact="handleAddContact"
      @created="handleCreatedContactLocation"
      @edited="handleEditedContactLocation"
      @canceled="handleCanceled"
      @change-schedule-availability-days="handleChangeScheduleAvailabilityDays"
    />
  </WGridItem>
</template>
<style scoped lang="scss">
.staff-attendance-locations-fields {
  display: flex;
  flex-direction: row;
  gap: var(--gl-spacing-02);
}

.attendance-cards {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-02);
}
</style>
