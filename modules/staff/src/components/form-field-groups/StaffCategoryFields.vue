<script setup lang="ts">
import {
  WGridItem,
  WTitle,
  W<PERSON>extfield,
  WSwitch,
  WSegmentedButtonGroup,
  WSegmentedButton,
  WSelect
} from '@alice-health/wonderland-vue'
import type { StaffForm, StaffFormStaffTier } from '@staff/models'

const props = defineProps<{
  showTierTheoretical?: boolean
  showTierAttendance?: boolean
  fields: StaffForm
  staffTiers: StaffFormStaffTier[]
}>()
</script>
<template>
  <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Categoria de atuação</WTitle>
  </WGridItem>
  <WGridItem class="staff-category-fields" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WSwitch label="Ativo no retaguarda" size="small" v-model="props.fields.attendsToOnCall" />
    <WSegmentedButtonGroup
      :disabled="!props.fields.attendsToOnCall"
      size="small"
      v-model="props.fields.onCallPaymentMethod"
    >
      <WSegmentedButton icon="icCasaAlice" direction="horizontal" size="medium" value="ALICE">
        Via Alice
      </WSegmentedButton>
      <WSegmentedButton
        icon="icHospital"
        direction="horizontal"
        size="medium"
        value="HEALTH_INSTITUTION"
      >
        Via HI
      </WSegmentedButton>
    </WSegmentedButtonGroup>
  </WGridItem>
  <WGridItem class="staff-category-fields" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WSelect
      v-if="props.showTierTheoretical && props.staffTiers"
      class="flex-1"
      label="Tier teórico"
      v-model="props.fields.theoristTier"
    >
      <option value="" disabled selected>Selecione o tier teórico</option>
      <option v-for="tier in props.staffTiers" :key="tier.value" :value="tier.value">
        {{ tier.name }}
      </option>
    </WSelect>
    <WSelect
      v-if="props.showTierAttendance && props.staffTiers"
      class="flex-1"
      label="Tier de atendimento"
      v-model="props.fields.tier"
    >
      <option value="" disabled selected>Selecione o tier de atendimento</option>
      <option v-for="tier in props.staffTiers" :key="tier.value" :value="tier.value">
        {{ tier.name }}
      </option>
    </WSelect>
  </WGridItem>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTextfield
      label="Data de descrendenciamento"
      placeholder="Insira a data"
      mask-type="date"
      leading-icon="icCalendar"
      v-model="props.fields.deAccreditationDate"
    />
  </WGridItem>
</template>
<style scoped lang="scss">
.staff-category-fields {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--gl-spacing-08);
}
</style>
