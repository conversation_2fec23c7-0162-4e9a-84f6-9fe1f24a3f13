<script setup lang="ts">
import {
  WGridItem,
  WTextarea,
  WTitle,
  WSelect,
  WParagraph,
  WButton
} from '@alice-health/wonderland-vue'
import { type StaffForm } from '@staff/models'
import { inject } from 'vue'
import type { SnackbarComponentProps } from '@commons/index'

const props = defineProps<{
  fields: StaffForm
}>()

const snackbar = inject<SnackbarComponentProps>('snackbar')

const removeQualification = (qualification: string) => {
  props.fields.qualifications = props.fields.qualifications?.filter((q) => q !== qualification)
}

const addQualification = (event: CustomEvent) => {
  const value = event.detail
  if (!props.fields.qualifications?.includes(value)) {
    props.fields.qualifications?.push(value)
  } else {
    snackbar?.value?.$el.add({
      message: 'Qualificação já adicionada',
      icon: 'icAlertCircleOutlined'
    })
  }
}

const onChangeEducation = (event: CustomEvent) => {
  const value = event.detail
  props.fields.education = value.split(',')
}
</script>
<template>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTextarea label="Bio" max-length="500" v-model="props.fields.profileBio" />
  </WGridItem>
  <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Educação</WTitle>
  </WGridItem>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTextarea
      label="Educação"
      max-length="500"
      placeholder="Insira aqui"
      :value="props.fields.education?.join(', ')"
      @w-change="onChangeEducation"
    />
  </WGridItem>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WSelect label="Qualificação" placeholder="Selecione" @w-change="addQualification">
      <option value="" disabled selected>Adicionar qualificação</option>
      <option value="ISO_9001">ISO 9001</option>
      <option value="JOINT_COMMISSION_INTERNATIONAL">Joint Commission International</option>
    </WSelect>
    <div class="staff--qualifications">
      <ul>
        <li
          class="staff--qualifications__qualification"
          v-for="qualification in props.fields.qualifications"
          :key="qualification"
        >
          <WParagraph size="large">{{ qualification }}</WParagraph>
          <WButton
            variant="secondary"
            icon-button
            icon="icTrash"
            size="small"
            @click="removeQualification(qualification)"
          ></WButton>
        </li>
      </ul>
    </div>
  </WGridItem>
</template>

<style scoped lang="scss">
.staff--qualifications,
.staff--educations {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-02);
  overflow-y: auto;
  max-height: 200px;

  &__qualification,
  &__education {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--gl-spacing-02);
    padding: var(--gl-spacing-02);
  }
}
</style>
