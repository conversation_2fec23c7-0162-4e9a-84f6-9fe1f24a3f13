<script setup lang="ts">
import { computed, ref } from 'vue'
import { StaffType } from '@staff/models'
import { useQuery } from '@tanstack/vue-query'
import { getRoles } from '@staff/api/queries'
import { WModal, WAutocomplete } from '@alice-health/wonderland-vue'

const props = defineProps<{
  opened: boolean
  filters: {
    active?: string | boolean
    types?: string | null
    roles?: string | null
  }
}>()
const emit = defineEmits(['closed'])

const types = ref(Object.entries(StaffType).map(([key, value]) => ({
  name: value,
  value: key
})))
const status = ref([
  {
    name: 'Ativo',
    value: 'true'
  },
  {
    name: 'Inativo',
    value: 'false'
  }
])
const staffModalFilters = ref({
  active: props.filters.active ? props.filters.active : status.value[0],
  types: props.filters.types ?? '',
  roles: props.filters.roles ?? null
})

const staffType = computed(() => staffModalFilters.value.types.value ?? '')

const {
  data: roles,
  isLoading: isLoadingRoles,
  refetch: refetchRoles
} = useQuery({
  queryKey: ['roles', staffType],
  queryFn: () => getRoles(staffType.value),
  enabled: false
})

function onCloseFilters(event: CustomEvent) {
  if (event.detail === 'cancel') {
    onClearFilters()
  }

  emit('closed', {
    active: staffModalFilters.value.active.value,
    types: staffModalFilters.value.types ? staffModalFilters.value.types?.value : null,
    roles: staffModalFilters.value.roles ? staffModalFilters.value.roles?.id : null
  })
}

function onChangeType(event: CustomEvent) {
  staffModalFilters.value.types = event.detail
  staffModalFilters.value.roles = null

  if (staffModalFilters.value.types) {
    refetchRoles()
  }
}

function onClearFilters() {
  staffModalFilters.value = {
    active: status.value[0],
    types: '',
    roles: null
  }
}
</script>

<template>
  <WModal
    id="staff-fiters"
    title="Filtros"
    confirm-label="Aplicar"
    cancel-label="Limpar"
    :opened="props.opened"
    @WClosed="onCloseFilters"
  >
    <div class="staff--filters">
      <WAutocomplete
        label="Status da conta"
        placeholder="Selecione o status"
        v-model="staffModalFilters.active"
        search-by-key="name"
        :items="status"
      />

      <WAutocomplete
        label="Tipo de staff"
        placeholder="Selecione o tipo"
        v-model="staffModalFilters.types"
        search-by-key="name"
        :items="types"
        @WSelect="onChangeType"
      />

      <WAutocomplete
        label="Role"
        placeholder="Selecione a role"
        v-model="staffModalFilters.roles"
        :items="roles"
        search-by-key="name"
        :disabled="!staffModalFilters.types || isLoadingRoles"
      />
    </div>
  </WModal>
</template>

<style scoped lang="scss">
.staff--filters {
  display: flex;
  flex-direction: column;
  justify-content: left;
  gap: 16px;
  min-width: 300px;
}
</style>
