import { within } from '@testing-library/vue'
import { customRender, screen } from '@staff/tests'
import { getRoles } from '@staff/tests/mocks'
import ListFiltersModal from '../ListFiltersModal.vue'

import { server } from '@commons/services/mockServer'

describe('ListFiltersModal', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getRoles))

  const baseFilters = {
    active: 'true',
    types: 'PITAYA',
    roles: null
  }

  it('renders modal with correct title and fields', async () => {
    customRender(ListFiltersModal, {
      props: {
        opened: true,
        filters: baseFilters
      }
    })
    expect(await screen.findByText('Filtros')).toBeInTheDocument()
    expect(screen.getByText('Status da conta')).toBeInTheDocument()
    expect(screen.getByText('Tipo de staff')).toBeInTheDocument()
    expect(screen.getByText('Role')).toBeInTheDocument()
  })

  it('should render modal buttons', async () => {
    customRender(ListFiltersModal, {
      props: {
        opened: true,
        filters: baseFilters
      }
    })

    const modal = await screen.findByRole('dialog')

    expect(modal).toBeInTheDocument()

    expect(within(modal).getByText('Limpar')).toBeInTheDocument()
    expect(within(modal).getByText('Aplicar')).toBeInTheDocument()
  })
})
