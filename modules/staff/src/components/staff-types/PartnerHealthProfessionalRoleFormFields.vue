<script lang="ts" setup>
import StaffAvatarAndStatus from '../form-field-groups/StaffAvatarAndStatus.vue'
import CommonPersonalFields from '../form-field-groups/CommonPersonalFields.vue'
import StaffBioAndEducationFields from '../form-field-groups/StaffBioAndEducationFields.vue'
import CommonProfessionalFields from '../form-field-groups/CommonProfessionalFields.vue'
import StaffAttendanceLocationsFields from '../form-field-groups/StaffAttendanceLocationsFields.vue'
import StaffCategoryFields from '../form-field-groups/StaffCategoryFields.vue'
import {
  StaffType,
  type StaffForm,
  type StaffFormCouncilType,
  type StaffFormSpecialty,
  type StaffFormProviderUnit,
  type StaffFormStaffTier,
  type StaffFormSubSpecialty
} from '@staff/models'

const props = defineProps<{
  staffType: StaffType
  fields: StaffForm
  councilTypes: StaffFormCouncilType[]
  specialties: StaffFormSpecialty[]
  subSpecialties: StaffFormSpecialty[]
  internalSubSpecialties: StaffFormSubSpecialty[]
  hospitals: StaffFormProviderUnit[]
}>()

const emit = defineEmits(['avatar-change', 'change-specialty', 'change-internal-specialty'])

const onChangeAvatar = async (event: CustomEvent) => emit('avatar-change', event)

const onChangeSpecialty = (specialtyId: string) => {
  emit('change-specialty', specialtyId)
}

const onChangeInternalSpecialty = (specialtyId: string) => {
  emit('change-internal-specialty', specialtyId)
}
</script>

<template>
  <StaffAvatarAndStatus
    :showAppSwitch="true"
    :fields="props.fields"
    @avatar-change="onChangeAvatar"
  />
  <CommonPersonalFields :fields="props.fields" />
  <StaffBioAndEducationFields :fields="props.fields" />
  <CommonProfessionalFields
    :fields="props.fields"
    :showOrganType="true"
    :showInternalSpecialty="true"
    :councilTypes="props.councilTypes"
    :specialties="props.specialties"
    :subSpecialties="props.subSpecialties"
    :internalSubSpecialties="props.internalSubSpecialties"
    :hospitals="props.hospitals"
    @change-specialty="onChangeSpecialty"
    @change-internal-specialty="onChangeInternalSpecialty"
  />
  <StaffAttendanceLocationsFields :fields="props.fields" />
  <StaffCategoryFields
    :showTierTheoretical="false"
    :showTierAttendance="true"
    :staffTiers="props.staffTiers"
    :fields="props.fields"
  />
</template>

<style scoped lang="scss">
.staff--form__field {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-08);
}
</style>
