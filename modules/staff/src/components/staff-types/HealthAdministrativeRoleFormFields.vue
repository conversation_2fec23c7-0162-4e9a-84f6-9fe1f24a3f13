<script lang="ts" setup>
import StaffAvatarAndStatus from '../form-field-groups/StaffAvatarAndStatus.vue'
import CommonPersonalFields from '../form-field-groups/CommonPersonalFields.vue'
import { StaffType, type StaffForm } from '@staff/models'

const props = defineProps<{
  staffType: StaffType
  fields: StaffForm
}>()

const emit = defineEmits(['avatar-change'])

const onChangeAvatar = async (event: CustomEvent) => emit('avatar-change', event)
</script>

<template>
  <StaffAvatarAndStatus :fields="props.fields" @avatar-change="onChangeAvatar" />
  <CommonPersonalFields :fields="props.fields" />
</template>

<style scoped lang="scss">
.staff--form__field {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-08);
}
</style>
