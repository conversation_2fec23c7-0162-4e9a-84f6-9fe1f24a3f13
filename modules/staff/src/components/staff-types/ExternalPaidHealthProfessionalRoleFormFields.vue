<script lang="ts" setup>
import StaffAvatarAndStatus from '../form-field-groups/StaffAvatarAndStatus.vue'
import CommonPersonalFields from '../form-field-groups/CommonPersonalFields.vue'
import { WGridItem, WTitle, WTextfield, WSelect } from '@alice-health/wonderland-vue'
import { StaffType, type StaffForm, type StaffFormCouncilType } from '@staff/models'

const props = defineProps<{
  staffType: StaffType
  fields: StaffForm
  councilTypes: StaffFormCouncilType[]
}>()

const emit = defineEmits(['avatar-change'])

const onChangeAvatar = async (event: CustomEvent) => {
  emit('avatar-change', event)
}
</script>

<template>
  <StaffAvatarAndStatus :fields="props.fields" @avatar-change="onChangeAvatar" />
  <CommonPersonalFields :fields="props.fields" />

  <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Dados profissionais</WTitle>
  </WGridItem>
  <WGridItem
    class="staff-professional-anesthetist"
    :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }"
  >
    <WTextfield
      class="flex-1"
      label="Número do conselho"
      placeholder="Digite o número"
      v-model="props.fields.council.number"
    />
    <WSelect class="flex-1" label="Estado do conselho" v-model="props.fields.council.state">
      <option value="" disabled selected>Selecione o estado</option>
      <option value="AC">Acre</option>
      <option value="AL">Alagoas</option>
      <option value="AP">Amapá</option>
      <option value="AM">Amazonas</option>
      <option value="BA">Bahia</option>
      <option value="CE">Ceará</option>
      <option value="DF">Distrito Federal</option>
      <option value="ES">Espírito Santo</option>
      <option value="GO">Goiás</option>
      <option value="MA">Maranhão</option>
      <option value="MT">Mato Grosso</option>
      <option value="MS">Mato Grosso do Sul</option>
      <option value="MG">Minas Gerais</option>
      <option value="PA">Pará</option>
      <option value="PB">Paraíba</option>
      <option value="PR">Paraná</option>
      <option value="PE">Pernambuco</option>
      <option value="PI">Piauí</option>
      <option value="RJ">Rio de Janeiro</option>
      <option value="RN">Rio Grande do Norte</option>
      <option value="RS">Rio Grande do Sul</option>
      <option value="RO">Rondônia</option>
      <option value="RR">Roraima</option>
      <option value="SC">Santa Catarina</option>
      <option value="SP">São Paulo</option>
      <option value="SE">Sergipe</option>
      <option value="TO">Tocantins</option>
    </WSelect>
    <WSelect class="flex-1" label="Orgão (tipo)" v-model="props.fields.council.type">
      <option value="" disabled selected>Selecione o orgão</option>
      <option
        v-for="councilType in props.councilTypes"
        :key="councilType.id"
        :value="councilType.id"
      >
        {{ councilType.name }}
      </option>
    </WSelect>
  </WGridItem>

  <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTitle variant="heavy" size="small">Categoria de atuação</WTitle>
  </WGridItem>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WTextfield
      label="Data de descrendenciamento"
      placeholder="Insira a data"
      mask-type="date"
      leading-icon="icCalendar"
      v-model="props.fields.deAccreditationDate"
    />
  </WGridItem>
</template>

<style scoped lang="scss">
.staff--form__field {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-08);
}

.staff-professional-anesthetist {
  display: flex;
  flex-direction: row;
  gap: var(--gl-spacing-08);
}
</style>
