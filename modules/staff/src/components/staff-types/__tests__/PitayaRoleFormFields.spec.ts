import { customRender, screen } from '@staff/tests'
import PitayaRoleFormFields from '../PitayaRoleFormFields.vue'

describe('PitayaRoleFormFields', () => {
  const minimalFields = {
    type: 'PITAYA',
    role: 'admin',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    gender: 'MALE',
    active: true
  }

  it('renders StaffAvatarAndStatus and CommonPersonalFields', async () => {
    customRender(PitayaRoleFormFields, {
      props: { staffType: 'PITAYA', fields: minimalFields }
    })
    expect(await screen.findByText('Primeiro nome')).toBeInTheDocument()
    expect(await screen.findByText('Sobrenome')).toBeInTheDocument()
  })

  it('passes fields prop to children', async () => {
    customRender(PitayaRoleFormFields, {
      props: { staffType: 'PITAYA', fields: minimalFields }
    })
    expect(await screen.findByRole('textbox', { name: 'Primeiro nome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Sobrenome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'CPF' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Data de nascimento' })).toBeInTheDocument()
    expect(screen.getByRole('combobox', { name: 'Gênero' })).toBeInTheDocument()
    expect(screen.getByRole('switch', { name: 'Ativo' })).toBeInTheDocument()
  })
}) 