import { customRender, screen } from '@staff/tests'
import AnesthetistRoleFormFields from '../ExternalPaidHealthProfessionalRoleFormFields.vue'
import { staffFormFields } from '../../../tests/mocks/buildStaffForm.mock'

const minimalFields = {
  type: 'EXTERNAL_PAID_HEALTH_PROFESSIONAL',
  role: 'anesthetist',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  gender: 'MALE',
  active: true,
  profileBio: '',
  council: { number: '', state: '', type: '' },
  deAccreditationDate: '',
}

describe('AnesthetistRoleFormFields', () => {
  it('renders all required fields', async () => {
    customRender(AnesthetistRoleFormFields, {
      props: {
        staffType: 'EXTERNAL_PAID_HEALTH_PROFESSIONAL',
        fields: minimalFields,
        councilTypes: staffFormFields.councilTypes
      }
    })
    // Personal fields
    expect(await screen.findByRole('textbox', { name: 'Primeiro nome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Sobrenome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'CPF' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Data de nascimento' })).toBeInTheDocument()
    expect(screen.getByRole('combobox', { name: 'Gênero' })).toBeInTheDocument()
    expect(await screen.findByRole('switch', { name: 'Ativo' })).toBeInTheDocument()
    // Professional fields
    expect(screen.getByRole('textbox', { name: 'Número do conselho' })).toBeInTheDocument()
    expect(screen.getByRole('combobox', { name: 'Estado do conselho' })).toBeInTheDocument()
    expect(await screen.findByRole('combobox', { name: 'Orgão (tipo)' })).toBeInTheDocument()
    // Category fields
    expect(screen.getByText('Categoria de atuação')).toBeInTheDocument()
    expect(await screen.findByRole('textbox', { name: 'Data de descrendenciamento' })).toBeInTheDocument()
    // Section titles
    expect(screen.getByText('Dados profissionais')).toBeInTheDocument()
  })
}) 