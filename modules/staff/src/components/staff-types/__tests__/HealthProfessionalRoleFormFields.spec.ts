import { customRender, screen } from '@staff/tests'
import HealthProfessionalRoleFormFields from '../HealthProfessionalRoleFormFields.vue'
import { staffFormFields } from '../../../tests/mocks/buildStaffForm.mock'

const minimalFields = {
  type: 'HEALTH_PROFESSIONAL',
  role: 'doctor',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  gender: 'MALE',
  active: true,
  profileBio: '',
  council: { number: '', state: '', type: '' },
  specialty: staffFormFields.specialties[0],
  subSpecialties: [staffFormFields.specialties[0]],
  internalSpecialty: staffFormFields.specialties[0],
  internalSubSpecialties: [staffFormFields.specialties[0]],
  providerUnits: [staffFormFields.providerUnits?.[0] || {}],
  qualifications: [],
  tier: '',
  theoristTier: '',
  curiosity: '',
  showOnApp: false,
  education: [],
  healthSpecialistScore: '',
  deAccreditationDate: '',
  contacts: [],
  paymentFrequency: 0,
  attendsToOnCall: false,
  onCallPaymentMethod: '',
  onVacationStart: '',
  onVacationUntil: '',
  memedStatus: null,
  urlSlug: ''
}

describe('HealthProfessionalRoleFormFields', () => {
  it('renders all grouped fields', async () => {
    customRender(HealthProfessionalRoleFormFields, {
      props: {
        staffType: 'HEALTH_PROFESSIONAL',
        fields: minimalFields,
        councilTypes: staffFormFields.councilTypes,
        specialties: staffFormFields.specialties,
        subSpecialties: staffFormFields.specialties,
        internalSubSpecialties: staffFormFields.specialties,
        hospitals: staffFormFields.providerUnits || [],
        staffTiers: staffFormFields.staffTiers || []
      }
    })
    expect(await screen.findByText('Especialidade')).toBeInTheDocument()
    expect(await screen.findByText('Especialidade interna')).toBeInTheDocument()
    expect(await screen.findByText('Hospitais que atua (filiação)')).toBeInTheDocument()
    expect(await screen.findByText('Categoria de atuação')).toBeInTheDocument()
  })
}) 