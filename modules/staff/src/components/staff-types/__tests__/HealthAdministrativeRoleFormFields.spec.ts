import { customRender, screen } from '@staff/tests'
import HealthAdministrativeRoleFormFields from '../HealthAdministrativeRoleFormFields.vue'

describe('HealthAdministrativeRoleFormFields', () => {
  const minimalFields = {
    type: 'HEALTH_ADMINISTRATIVE',
    role: 'admin',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    gender: 'MALE',
    active: true
  }

  it('renders StaffAvatarAndStatus and CommonPersonalFields', async () => {
    customRender(HealthAdministrativeRoleFormFields, {
      props: { staffType: 'HEALTH_ADMINISTRATIVE', fields: minimalFields }
    })
    expect(await screen.findByText('Primeiro nome')).toBeInTheDocument()
    expect(screen.getByText('Sobrenome')).toBeInTheDocument()
  })

  it('passes fields prop to children', async () => {
    customRender(HealthAdministrativeRoleFormFields, {
      props: { staffType: 'HEALTH_ADMINISTRATIVE', fields: minimalFields }
    })
    expect(await screen.findByRole('textbox', { name: 'Primeiro nome' })).toBeInTheDocument()
    expect(await screen.findByRole('textbox', { name: 'Sobrenome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'CPF' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Data de nascimento' })).toBeInTheDocument()
    expect(screen.getByRole('combobox', { name: 'Gênero' })).toBeInTheDocument()
    expect(screen.getByRole('switch', { name: 'Ativo' })).toBeInTheDocument()
  })
}) 