import { customRender, screen } from '@staff/tests'
import PartnerHealthProfessionalRoleFormFields from '../PartnerHealthProfessionalRoleFormFields.vue'
import { staffFormFields } from '../../../tests/mocks/buildStaffForm.mock'

const minimalFields = {
  type: 'PARTNER_HEALTH_PROFESSIONAL',
  role: 'doctor',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  gender: 'MALE',
  active: true,
  profileBio: '',
  council: { number: '', state: '', type: '' },
  specialty: staffFormFields.specialties[0],
  subSpecialties: [staffFormFields.specialties[0]],
  internalSpecialty: staffFormFields.specialties[0],
  internalSubSpecialties: [staffFormFields.specialties[0]],
  providerUnits: [staffFormFields.providerUnits?.[0] || {}],
  qualifications: [],
  tier: '',
  theoristTier: '',
  curiosity: '',
  showOnApp: false,
  education: [],
  healthSpecialistScore: '',
  deAccreditationDate: '',
  contacts: [],
  paymentFrequency: 0,
  attendsToOnCall: false,
  onCallPaymentMethod: '',
  onVacationStart: '',
  onVacationUntil: '',
  memedStatus: null,
  urlSlug: ''
}

describe('PartnerHealthProfessionalRoleFormFields', () => {
  it('renders all grouped fields', async () => {
    customRender(PartnerHealthProfessionalRoleFormFields, {
      props: {
        staffType: 'PARTNER_HEALTH_PROFESSIONAL',
        fields: minimalFields,
        councilTypes: staffFormFields.councilTypes,
        specialties: staffFormFields.specialties,
        subSpecialties: staffFormFields.specialties,
        internalSubSpecialties: staffFormFields.specialties,
        hospitals: staffFormFields.providerUnits || [],
        staffTiers: staffFormFields.staffTiers || []
      }
    })

    expect(await screen.findByText(/dados pessoais/i)).toBeInTheDocument()
    expect(screen.getByText(/educação/i)).toBeInTheDocument()
    expect(screen.getByText(/dados profissionais/i)).toBeInTheDocument()
    expect(screen.getByText(/locais de atendimento/i)).toBeInTheDocument()
    expect(screen.getByText(/categoria de atuação/i)).toBeInTheDocument()
  })
}) 