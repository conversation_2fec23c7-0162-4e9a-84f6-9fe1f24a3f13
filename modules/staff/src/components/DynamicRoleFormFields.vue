<script setup lang="ts">
import { StaffType, type StaffForm } from '@staff/models'
import { computed } from 'vue'

import PitayaRoleFormFields from './staff-types/PitayaRoleFormFields.vue'
import type {
  StaffFormCouncilType,
  StaffFormSpecialty,
  StaffFormProviderUnit,
  StaffFormSubSpecialty,
  StaffFormStaffTier
} from '@staff/models'
import CommunitySpecialistRoleFormFields from './staff-types/CommunitySpecialistRoleFormFields.vue'
import HealthAdministrativeRoleFormFields from './staff-types/HealthAdministrativeRoleFormFields.vue'
import HealthProfessionalRoleFormFields from './staff-types/HealthProfessionalRoleFormFields.vue'
import PartnerHealthProfessionalRoleFormFields from './staff-types/PartnerHealthProfessionalRoleFormFields.vue'
import ExternalPaidHealthProfessionalRoleFormFields from './staff-types/ExternalPaidHealthProfessionalRoleFormFields.vue'

const props = defineProps<{
  staffType: StaffType
  fields: StaffForm
  subSpecialties: StaffFormSubSpecialty[]
  staffTiers?: StaffFormStaffTier[]
  councilTypes: StaffFormCouncilType[]
  specialties: StaffFormSpecialty[]
  internalSubSpecialties: StaffFormSubSpecialty[]
  hospitals: StaffFormProviderUnit[]
}>()
const emit = defineEmits(['avatar-change', 'change-specialty', 'change-internal-specialty'])

const componentMap = {
  PITAYA: PitayaRoleFormFields,
  COMMUNITY_SPECIALIST: CommunitySpecialistRoleFormFields,
  HEALTH_ADMINISTRATIVE: HealthAdministrativeRoleFormFields,
  HEALTH_PROFESSIONAL: HealthProfessionalRoleFormFields,
  PARTNER_HEALTH_PROFESSIONAL: PartnerHealthProfessionalRoleFormFields,
  EXTERNAL_PAID_HEALTH_PROFESSIONAL: ExternalPaidHealthProfessionalRoleFormFields
}

const CurrentComponent = computed(() => {
  return componentMap[props.staffType as unknown as keyof typeof componentMap] || null
})

const onChangeSpecialty = (specialtyId: string) => {
  emit('change-specialty', specialtyId)
}

const onChangeInternalSpecialty = (specialtyId: string) => {
  emit('change-internal-specialty', specialtyId)
}

const onChangeAvatar = (file: File) => emit('avatar-change', file)
</script>

<template>
  <component
    v-if="CurrentComponent"
    :is="CurrentComponent"
    :staffType="props.staffType"
    :fields="props.fields"
    :councilTypes="props.councilTypes"
    :specialties="props.specialties"
    :hospitals="props.hospitals"
    :staffTiers="props.staffTiers"
    :subSpecialties="props.subSpecialties"
    :internalSubSpecialties="props.internalSubSpecialties"
    @avatar-change="onChangeAvatar"
    @change-specialty="onChangeSpecialty"
    @change-internal-specialty="onChangeInternalSpecialty"
  />
</template>
