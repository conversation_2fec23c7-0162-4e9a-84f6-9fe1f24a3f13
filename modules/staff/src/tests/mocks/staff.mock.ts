import { type StaffRequest } from '@staff/models'
import { HttpResponse, http } from 'msw'

const staff: StaffRequest = {
    id: "1689a567-be36-4e08-8547-1295e4993f00",
    firstName: "Doctor",
    lastName: "Test",
    email: "<EMAIL>",
    nationalId: "16087244840",
    gender: "NO_ANSWER",
    profileImageUrl: "https://placehold.co/150",
    role: "COMMUNITY",
    type: "COMMUNITY_SPECIALIST",
    active: true,
    urlSlug: "test-url-slug",
    council: {
        number: "86758",
        state: "SP",
        type: 6
    },
    specialty: "78d6214e-5a9b-44b4-8d84-5a3295add100",
    subSpecialties: [
        "97d5c499-d026-4fbe-800f-9f892f87ad00"
    ],
    internalSubSpecialties: [],
    providerUnits: [],
    qualifications: [
        "ISO_9001"
    ],
    tier: "SUPER_EXPERT",
    theoristTier: "ULTRA_EXPERT",
    curiosity: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    showOnApp: true,
    education: [
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
    ],
    healthSpecialistScore: "LIVING_THE_IMPOSSIBLE",
    contacts: [
        {
            "id": "d4451c53-7364-4b98-9a6d-5dc0c6add900",
            "phones": [
                {
                    "title": "Contato 1",
                    "phone": "(11)99999-9999",
                    "type": "WHATSAPP"
                }
            ],
            "modality": "REMOTE",
            "availableDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]
        },
        {
            "id": "4ed20087-6fed-40f0-8212-ab369ce15000",
            "address": {
                "id": "4ed20087-6fed-40f0-8212-ab369ce15000",
                "street": "R. Test",
                "number": "123",
                "complement": "",
                "neighborhood": "Test",
                "state": "SP",
                "city": "Test",
                "zipcode": "01308-000",
                "label": "Test",
                "active": true,
                "latitude": "-23.5566802",
                "longitude": "-46.6550322"
            },
            "phones": [
                {
                    "title": "Contato 1",
                    "phone": "(11)99999-9999",
                    "type": "WHATSAPP"
                }
            ],
            "modality": "PRESENTIAL",
            "availableDays": []
        }
    ],
    paymentFrequency: 0,
    memedStatus: "NOT_FOUND",
    attendsToOnCall: false,
    version: 148,
    fullName: "Doctor Test"
}

const getById = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/staff/${staff.id}`, () => {
  return HttpResponse.json(staff)
})

export default getById
