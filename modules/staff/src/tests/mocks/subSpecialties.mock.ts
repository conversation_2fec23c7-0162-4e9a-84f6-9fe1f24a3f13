import type { StaffFormSubSpecialty } from '@staff/models'
import { HttpResponse, http } from 'msw'

const subSpecialtiesResult: StaffFormSubSpecialty[] = [
  {
      "name": "Alergologia Pediátrica",
      "type": "SUBSPECIALTY",
      "parentSpecialtyId": "78d6214e-5a9b-44b4-8d84-5a3295add100",
      "requireSpecialist": false,
      "generateGeneralistSubSpecialty": false,
      "active": true,
      "internal": false,
      "urlSlug": "alergologia-ped",
      "attentionLevel": "SECONDARY",
      "isTherapy": false,
      "createdAt": "2022-11-04T14:21:45.059865",
      "version": 2,
      "id": "64d63d70-23ec-43b8-820d-6dfcfe6c7500",
      "isAdvancedAccess": false
  },
  {
      "name": "Generalista",
      "type": "SUBSPECIALTY",
      "parentSpecialtyId": "78d6214e-5a9b-44b4-8d84-5a3295add100",
      "requireSpecialist": false,
      "generateGeneralistSubSpecialty": false,
      "active": true,
      "internal": false,
      "urlSlug": "alergologia-generalista",
      "attentionLevel": "SECONDARY",
      "isTherapy": false,
      "createdAt": "2022-12-22T20:47:26.148111",
      "version": 2,
      "id": "97d5c499-d026-4fbe-800f-9f892f87ad00",
      "isAdvancedAccess": false
  }
]

const subSpecialties = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/staff/sub_specialties?parentSpecialtyId=0e973a00-9577-4a39-8c58-5e85b5ef3f00`, () => {
  return HttpResponse.json(subSpecialtiesResult)
})

export default subSpecialties
