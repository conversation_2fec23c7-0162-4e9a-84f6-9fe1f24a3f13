import list from './list.mock'
import getById from './staff.mock'
import buildStaffForm from './buildStaffForm.mock'
import subSpecialties from './subSpecialties.mock'
import getRoles from './getRoles.mock'
import staffSignupRequestsList from './signupRequestsList.mock'

const handlers = [list, getById, buildStaffForm, subSpecialties, getRoles, staffSignupRequestsList]

export {
  handlers,
  list,
  getById,
  buildStaffForm,
  subSpecialties,
  getRoles,
  staffSignupRequestsList
}
