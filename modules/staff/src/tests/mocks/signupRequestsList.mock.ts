import type { StaffSignupRequestListItem } from '@staff/models/staff-signup-request'
import { http, HttpResponse } from 'msw'

const staffSignupRequests: StaffSignupRequestListItem[] = [
  {
    id: '12345678-abcd-90ef-ghij-klmnopqrstuv',
    name: '<PERSON>',
    status: '<PERSON><PERSON><PERSON>'
  }
]

const staffSignupRequestsList = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/staff_signup_review`,
  () => {
    return HttpResponse.json({
      pagination: {
        totalPages: 1,
        pageSize: 1
      },
      results: staffSignupRequests
    })
  }
)

export default staffSignupRequestsList
