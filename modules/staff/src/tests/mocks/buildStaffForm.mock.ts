import { type StaffFormFields } from '@staff/models'
import { HttpResponse, http } from 'msw'

const staffFormFields: StaffFormFields = {
    specialties: [
        {
            "name": "Acupuntura",
            "type": "SPECIALTY",
            "requireSpecialist": false,
            "generateGeneralistSubSpecialty": true,
            "active": true,
            "cboCode": {
                "id": "fdf337cd-d058-4792-b593-db4ba7dae100",
                "code": "225105",
                "description": "Médico acupunturista"
            },
            "internal": false,
            "urlSlug": "acupuntura",
            "attentionLevel": "SECONDARY",
            "isTherapy": true,
            "createdAt": "2020-12-11T12:31:19.176",
            "version": 18,
            "id": "8605b051-ebdb-4b74-85c9-a030b5a98100",
            "isAdvancedAccess": false
        },
        {
            "name": "Agendamento",
            "type": "SPECIALTY",
            "requireSpecialist": true,
            "generateGeneralistSubSpecialty": false,
            "active": true,
            "internal": true,
            "urlSlug": "agendamento",
            "isTherapy": false,
            "createdAt": "2021-06-22T18:03:15.891",
            "version": 35,
            "id": "3bc096ea-fc85-496a-9abf-455bc835ef00",
            "isAdvancedAccess": false
        },
        {
            "name": "Alergologia",
            "type": "SPECIALTY",
            "requireSpecialist": false,
            "generateGeneralistSubSpecialty": true,
            "active": true,
            "cboCode": {
                "id": "74683c35-1f44-4ee5-971f-5295f90f0100",
                "code": "225110",
                "description": "Médico alergista e imunologista"
            },
            "internal": false,
            "urlSlug": "alergologista",
            "attentionLevel": "SECONDARY",
            "isTherapy": false,
            "createdAt": "2020-12-16T15:06:28.995",
            "version": 5,
            "id": "78d6214e-5a9b-44b4-8d84-5a3295add100",
            "isAdvancedAccess": false
        },
        {
            "name": "Anestesiologia",
            "type": "SPECIALTY",
            "requireSpecialist": false,
            "generateGeneralistSubSpecialty": true,
            "active": true,
            "cboCode": {
                "id": "6f8127d1-615c-4eda-8eac-27f3cdcd4200",
                "code": "225151",
                "description": "Médico anestesiologista"
            },
            "internal": false,
            "urlSlug": "anestesiologia",
            "attentionLevel": "SECONDARY",
            "isTherapy": false,
            "createdAt": "2021-05-28T18:35:35.919",
            "version": 4,
            "id": "03322cd5-d7e7-4226-9571-91bd3bf4d100",
            "isAdvancedAccess": false
        },
        {
            "name": "Cirurgia Buxo-maxilo",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": false,
            "internal": true,
            "url_slug": "cirurgia-buco-maxilo",
            "is_therapy": false,
            "created_at": "2024-05-15T14:06:12.918142",
            "version": 2,
            "id": "4a011611-498d-47f1-b246-f012bf341700",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Cabeça e Pescoço",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": true,
            "cbo_code": {
                "id": "9d9b0214-5432-49c5-87fc-12ec3fac5900",
                "code": "225215",
                "description": "Médico cirurgião de cabeça e pescoço"
            },
            "internal": false,
            "url_slug": "cirurgia-cabeca-e-pescoco",
            "attention_level": "SECONDARY",
            "is_therapy": false,
            "created_at": "2021-03-09T12:10:59.944",
            "version": 4,
            "id": "d8e3c4a7-7ba3-4a08-9dea-38eae13f5800",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Cardíaca Pediátrica",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": false,
            "active": false,
            "internal": false,
            "url_slug": "cirurgia-cardiovascular-pediatrica",
            "attention_level": "PRIMARY",
            "is_therapy": false,
            "created_at": "2023-11-30T20:24:00.407593",
            "version": 2,
            "id": "f8401a44-7d20-4275-965a-cc051f13e200",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Cardiovascular",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": true,
            "cbo_code": {
                "id": "52ba313b-43ee-4547-b659-64c3a11fe100",
                "code": "225210",
                "description": "Médico cirurgião cardiovascular"
            },
            "internal": false,
            "url_slug": "cirurgia-cardiovascular",
            "attention_level": "SECONDARY",
            "is_therapy": false,
            "created_at": "2021-06-07T12:53:42.381",
            "version": 5,
            "id": "6b7cbde7-37fb-4d8b-bc28-be5e261d8b00",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Geral",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": false,
            "active": true,
            "internal": true,
            "url_slug": "cirurgia-geral",
            "is_therapy": false,
            "created_at": "2024-04-30T14:54:00.33282",
            "version": 6,
            "id": "996ec7a4-21d4-4ffd-b6b6-a2fd6dda9100",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Geral (Aparelho Digestivo)",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": true,
            "cbo_code": {
                "id": "5f1d8262-7219-4911-ba18-19a3ed1b4900",
                "code": "225220",
                "description": "Médico cirurgião do aparelho digestivo"
            },
            "internal": false,
            "url_slug": "cirurgia-geral-aparelho-digestivo",
            "attention_level": "SECONDARY",
            "is_therapy": false,
            "created_at": "2020-10-16T21:17:15.048",
            "version": 3,
            "id": "44d302d7-90f8-40d2-8b91-97f190cdc800",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Pediátrica",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": true,
            "cbo_code": {
                "id": "309c3a09-a269-4cad-bcb3-7fd930406700",
                "code": "225230",
                "description": "Médico cirurgião pediátrico"
            },
            "internal": false,
            "url_slug": "cirurgia-ped",
            "attention_level": "SECONDARY",
            "is_therapy": false,
            "created_at": "2022-07-12T21:41:42.242",
            "version": 12,
            "id": "f7536dc2-39fd-494e-9b99-eeae85040000",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Pediátrica",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": false,
            "internal": true,
            "url_slug": "cirurgia-pedia",
            "is_therapy": false,
            "created_at": "2024-06-19T18:56:35.978416",
            "version": 5,
            "id": "0dfaa669-7b5f-41c5-9868-b2473975b900",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Plástica",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": true,
            "cbo_code": {
                "id": "f49d3609-aba1-4ddd-8758-500166a1d300",
                "code": "225235",
                "description": "Médico cirurgião plástico"
            },
            "internal": false,
            "url_slug": "cirurgia-plastica",
            "attention_level": "SECONDARY",
            "is_therapy": false,
            "created_at": "2020-11-09T13:42:50.583",
            "version": 6,
            "id": "54ea5289-583d-4e1e-b446-3386d0880b00",
            "is_advanced_access": false
        },
        {
            "name": "Cirurgia Torácica",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": true,
            "cbo_code": {
                "id": "dad544ed-6550-4a80-9db8-ff620a15ed00",
                "code": "225240",
                "description": "Médico cirurgião torácico"
            },
            "internal": false,
            "url_slug": "cirurgia-toracica",
            "attention_level": "SECONDARY",
            "is_therapy": false,
            "created_at": "2021-02-03T19:28:24.944",
            "version": 6,
            "id": "ea917faf-9fda-441c-a524-ba0564bf6200",
            "is_advanced_access": false
        },
        {
            "name": "Consulta  métodos anticonceptivos",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": true,
            "active": true,
            "internal": true,
            "url_slug": "consulta-metodos-anticonceptivos",
            "is_therapy": false,
            "created_at": "2021-01-13T13:16:00.263",
            "version": 4,
            "id": "1f99c103-1c14-444d-83de-b2c6ec698600",
            "is_advanced_access": false
        },
        {
            "name": "cuidados de suporte",
            "type": "SPECIALTY",
            "require_specialist": false,
            "generate_generalist_sub_specialty": false,
            "active": false,
            "internal": false,
            "url_slug": "cuidados-de-suporte-1",
            "is_therapy": false,
            "created_at": "2024-08-27T19:46:44.418211",
            "version": 2,
            "id": "210a8b7c-15d8-4b22-b7b1-a0615ec89900",
            "is_advanced_access": false
        }
    ],
    providerUnits: [
        {
            "id": "feebef5f-7c99-377d-ac0f-82394ada2200",
            "type": "HOSPITAL",
            "providerUnitGroupId": "58c09502-dbba-4904-be84-57a6fe91f700",
            "name": "Aconsel - Hospital Nossa Senhora de Lourdes",
            "contractOrigin": "CASSI",
            "cnpj": "07375113000110",
            "cnes": "5230241",
            "phones": [
                {
                    "title": "Contato 1",
                    "phone": "(54)3273-2500",
                    "type": "PHONE"
                },
                {
                    "title": "Contato 2",
                    "phone": "(54)3273-1123",
                    "type": "PHONE"
                }
            ],
            "workingPeriods": [],
            "qualifications": [],
            "products": [],
            "providerId": "da25b698-c151-415e-ac7a-d22368158300",
            "createdAt": "2023-11-28T17:49:22.557097",
            "updatedAt": "2024-08-02T02:01:02.066905",
            "administrativeStaff": [],
            "clinicalStaffIds": [],
            "brand": "ALICE",
            "medicalSpecialtyProfile": [],
            "urlSlug": "aconsel--hospital-nossa-senhora-de-lourdes",
            "status": "ACTIVE",
            "showOnScheduler": false,
            "attendanceTypes": [],
            "hasHospitalHealthTeam": false,
            "showOnApp": true
        },
        {
            "id": "83e54108-d63b-3903-ac3e-a71539cb8f00",
            "type": "HOSPITAL",
            "provider_unit_group_id": "9f3ae8aa-8591-435c-ba64-3a8a378fa500",
            "name": "Adas Médicas",
            "contract_origin": "CASSI",
            "cnpj": "07052462000100",
            "cnes": "6289754",
            "phones": [
                {
                    "title": "Telefone 1",
                    "phone": "(91)37547500",
                    "type": "PHONE"
                },
                {
                    "title": "Telefone 2",
                    "phone": "(91)37547533",
                    "type": "PHONE"
                }
            ],
            "working_periods": [],
            "qualifications": [],
            "products": [],
            "provider_id": "da25b698-c151-415e-ac7a-d22368158300",
            "created_at": "2023-11-28T17:41:05.213287",
            "updated_at": "2024-08-02T02:00:11.074011",
            "administrative_staff": [],
            "clinical_staff_ids": [],
            "brand": "ALICE",
            "medical_specialty_profile": [],
            "url_slug": "adas-medicas",
            "status": "ACTIVE",
            "show_on_scheduler": false,
            "attendance_types": [],
            "has_hospital_health_team": false,
            "show_on_app": true
        },
        {
            "id": "4056e4f7-856b-3d40-8aa8-ca6fd8b5e400",
            "type": "HOSPITAL",
            "provider_unit_group_id": "95a24d5d-b61a-4c08-bfe3-734faaccd700",
            "name": "AHBB - Rede Santa Casa",
            "contract_origin": "CASSI",
            "site": "https://www.ahbb.org.br/",
            "cnpj": "45349461000960",
            "cnes": "9680500",
            "phones": [
                {
                    "title": "Telefone 1",
                    "phone": "(14)34075066",
                    "type": "PHONE"
                }
            ],
            "working_periods": [],
            "qualifications": [],
            "image_url": "https://www.ahbb.org.br/img/logo_ahbb_azul.c87d01a6.svg",
            "products": [],
            "provider_id": "da25b698-c151-415e-ac7a-d22368158300",
            "created_at": "2023-11-28T17:56:28.333278",
            "updated_at": "2024-07-29T15:32:04.610697",
            "administrative_staff": [],
            "clinical_staff_ids": [],
            "brand": "ALICE",
            "medical_specialty_profile": [],
            "url_slug": "ahbb--rede-santa-casa",
            "status": "ACTIVE",
            "show_on_scheduler": false,
            "attendance_types": [],
            "has_hospital_health_team": false,
            "show_on_app": true
        },
        {
            "id": "7d89a23c-b8df-412c-b6b4-fbb4d35f7e00",
            "type": "HOSPITAL",
            "provider_unit_group_id": "a3436198-2e96-4823-8d33-f2690ab87b00",
            "name": "Albert Sabin Hospital e Maternidade",
            "contract_origin": "CASSI",
            "site": "https://www.sabinatibaia.com/",
            "cnpj": "04275687000129",
            "cnes": "2081865",
            "phones": [
                {
                    "title": "Contato 1",
                    "phone": "(11)4414-5000",
                    "type": "PHONE"
                }
            ],
            "working_periods": [
                {
                    "day_of_the_week": "MONDAY",
                    "opens": "00:00",
                    "closes": "23:59"
                },
                {
                    "day_of_the_week": "TUESDAY",
                    "opens": "00:00",
                    "closes": "23:59"
                },
                {
                    "day_of_the_week": "WEDNESDAY",
                    "opens": "00:00",
                    "closes": "23:59"
                },
                {
                    "day_of_the_week": "THURSDAY",
                    "opens": "00:00",
                    "closes": "23:59"
                },
                {
                    "day_of_the_week": "FRIDAY",
                    "opens": "00:00",
                    "closes": "23:59"
                },
                {
                    "day_of_the_week": "SATURDAY",
                    "opens": "00:00",
                    "closes": "23:59"
                },
                {
                    "day_of_the_week": "SUNDAY",
                    "opens": "00:00",
                    "closes": "23:59"
                }
            ],
            "qualifications": [],
            "image_url": "https://sabinatibaia.com.br/wp-content/webp-express/webp-images/uploads/2024/04/cropped-HAS.jpg.webp",
            "products": [],
            "provider_id": "da25b698-c151-415e-ac7a-d22368158300",
            "created_at": "2023-02-01T14:47:40.301356",
            "updated_at": "2025-03-12T14:56:49.447037",
            "administrative_staff": [],
            "clinical_staff_ids": [],
            "brand": "ALICE",
            "medical_specialty_profile": [],
            "url_slug": "albert-sabin-hospital-e-maternidade",
            "status": "ACTIVE",
            "show_on_scheduler": false,
            "attendance_types": [],
            "has_hospital_health_team": false,
            "show_on_app": true
        },
        {
            "id": "8c4add18-74bd-424f-868a-c3152cd89100",
            "type": "HOSPITAL",
            "provider_unit_group_id": "3ec962e0-8c88-4db1-abbe-7d78a0acca00",
            "name": "Alíria",
            "contract_origin": "ALICE",
            "cnpj": "47848127000129",
            "cnes": "999999",
            "phones": [
                {
                    "title": "Contato 1",
                    "phone": "(48)9121-0311",
                    "type": "PHONE"
                }
            ],
            "working_periods": [
                {
                    "day_of_the_week": "MONDAY",
                    "opens": "09:00",
                    "closes": "18:00"
                },
                {
                    "day_of_the_week": "TUESDAY",
                    "opens": "09:00",
                    "closes": "18:00"
                },
                {
                    "day_of_the_week": "WEDNESDAY",
                    "opens": "09:00",
                    "closes": "18:00"
                },
                {
                    "day_of_the_week": "THURSDAY",
                    "opens": "09:00",
                    "closes": "18:00"
                },
                {
                    "day_of_the_week": "FRIDAY",
                    "opens": "09:00",
                    "closes": "18:00"
                }
            ],
            "qualifications": [],
            "products": [],
            "provider_id": "169df525-0d35-47b0-a5bd-32420465ef00",
            "created_at": "2024-11-07T16:54:00.480847",
            "updated_at": "2025-01-30T19:12:17.328242",
            "administrative_staff": [],
            "clinical_staff_ids": [],
            "brand": "ALICE_DUQUESA",
            "medical_specialty_profile": [
                {
                    "specialty_id": "8b0f2e62-21e7-4b7a-98a1-25163f76fc00",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "bb2d8aa3-9276-438f-b0d5-4e1fb2d19700",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "4319ca83-e727-4656-a967-0aa894e5eb00",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "fbbcd0e8-b729-4277-8308-2c7b2da5e600",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "c640a2b7-1540-42d1-94d0-ae7647eb7e00",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "58636df5-e6a6-4198-b703-6fb2247f7100",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "b4b507df-4d25-4a45-9314-3f70a226d600",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "78d6214e-5a9b-44b4-8d84-5a3295add100",
                    "sub_specialty_ids": []
                },
                {
                    "specialty_id": "20e92589-ad8e-4c3e-8503-6b5e23487d00",
                    "sub_specialty_ids": []
                }
            ],
            "url_slug": "arilia",
            "status": "ACTIVE",
            "show_on_scheduler": false,
            "has_hospital_health_team": false,
            "show_on_app": false
        }
    ],
    staffRoles: [
        {
            id: "COMMUNITY",
            name: "Especialista",
            value: "COMMUNITY"
        },
        {
            id: "HEALTH_COMMUNITY",
            name: "Health Community",
            value: "HEALTH_COMMUNITY"
        }
    ],
    staffTiers: [
        {
            "id": "OPT_IN",
            "name": "Opt-in",
            "value": "OPT_IN"
        },
        {
            "id": "TALENTED",
            "name": "Talented",
            "value": "TALENTED"
        },
        {
            "id": "EXPERT",
            "name": "Expert",
            "value": "EXPERT"
        },
        {
            "id": "SUPER_EXPERT",
            "name": "Super Expert",
            "value": "SUPER_EXPERT"
        },
        {
            "id": "ULTRA_EXPERT",
            "name": "Ultra Expert",
            "value": "ULTRA_EXPERT"
        }
    ],
    staffScore: [
        {
            "id": "NEED_TO_RAISE_THE_BAR",
            "name": "Precisa subir a barra",
            "value": "NEED_TO_RAISE_THE_BAR"
        },
        {
            "id": "IS_RAISING_THE_BAR",
            "name": "Tá subindo a barra",
            "value": "IS_RAISING_THE_BAR"
        },
        {
            "id": "DOMINATING",
            "name": "Dominando a parada",
            "value": "DOMINATING"
        },
        {
            "id": "LIVING_THE_IMPOSSIBLE",
            "name": "Vivendo o impossível",
            "value": "LIVING_THE_IMPOSSIBLE"
        },
        {
            "id": "DOES_NOT_APPLY",
            "name": "Não se aplica",
            "value": "DOES_NOT_APPLY"
        }
    ],
    councilTypes: [
        {
            "id": 1,
            "name": "CRAS"
        },
        {
            "id": 2,
            "name": "COREN"
        },
        {
            "id": 3,
            "name": "CRF"
        },
        {
            "id": 4,
            "name": "CRFA"
        },
        {
            "id": 5,
            "name": "CREFITO"
        },
        {
            "id": 6,
            "name": "CRM"
        },
        {
            "id": 7,
            "name": "CRN"
        },
        {
            "id": 8,
            "name": "CRO"
        },
        {
            "id": 9,
            "name": "CRP"
        },
        {
            "id": 10,
            "name": "CREFONO"
        },
        {
            "id": 11,
            "name": "CRESS"
        },
        {
            "id": 12,
            "name": "OUT"
        }
    ]
}

const buildStaffForm = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/staff/build_staff`, () => {
  return HttpResponse.json(staffFormFields)
})

export default buildStaffForm

export { staffFormFields }
