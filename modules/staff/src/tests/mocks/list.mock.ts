import type { StaffListItem } from '@staff/models'
import { HttpResponse, http } from 'msw'

const staffs: StaffListItem[] = [
  {
    id: '1689a567-be36-4e08-8547-1295e4993f00',
    firstName: 'staff name',
    lastName: 'staff last name',
    active: true,
    email: '<EMAIL>',
    role: 'ADMIN'
  }
]

const list = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/staff`, () => {
  return HttpResponse.json({
    pagination: {
      totalPages: 1,
      pageSize: 1
    },
    results: staffs
  })
})

export default list
