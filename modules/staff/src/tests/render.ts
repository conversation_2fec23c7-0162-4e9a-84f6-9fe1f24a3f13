import { type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { createRouter, createWebHistory } from 'vue-router'

export * from '@alice-health/unit-presets/src/testing-library'

const routerInstance = createRouter({
  history: createWebHistory('/'),
  routes: [
    { path: '/', name: 'home', component: async () => ({}) },
    { path: '/staff/list', name: 'staff/list', component: async () => ({}) }
  ]
})

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        ...provide
      },
      ...other
    })
  }
}
