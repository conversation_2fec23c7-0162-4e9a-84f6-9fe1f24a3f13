<template>
  <ListLayout>
    <template #actions>
      <WTooltip label="BETA" position="right">
        <WButton icon="icAdd" variant="cta" size="large" @click="onRegister">Novo cadastro</WButton>
      </WTooltip>
    </template>

    <template #list>
      <WListControllers
        margin="none"
        has-items-per-page-select
        has-pagination
        input-placeholder="Buscar por nome ou e-mail"
        :hide-input="false"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        :has-filters="true"
        @WOpenFilters="onOpenFilters"
        @WPaginationNext="setNext"
        @WInputChange="searchChange"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
          </WSelect>
        </div>
      </WListControllers>

      <ListFiltersModal :opened="isFiltersOpen" :filters="currentFilters" @closed="onCloseFilters" />

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            v-for="header in headers"
            :key="header.id"
            :width="header?.width"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell>
              {{ item.firstName }}
            </WTableBodyCell>
            <WTableBodyCell>
              {{ item.lastName }}
            </WTableBodyCell>
            <WTableBodyCell>
              {{ item.email }}
            </WTableBodyCell>
            <WTableBodyCell>
              {{ item.role }}
            </WTableBodyCell>
            <WTableBodyCell width="70px" align="end">
              <div class="staff--list__actions">
                <WButton icon="icEdit" variant="secondary" icon-button @click="onEdit(item.id)" />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import { ListLayout, type PaginatedResponse } from '@commons/index'
import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WTooltip,
  WSelect
} from '@alice-health/wonderland-vue'
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { getStaffs } from '../api/queries'
import { type StaffListItem } from '@staff/models'
import ListFiltersModal from '../components/ListFiltersModal.vue'
import { removeEmptyFields } from '@commons/helpers'

const router = useRouter()
const route = useRoute()

const limits = [5, 10, 15]
const headers = [
  { id: 1, title: 'Nome' },
  { id: 2, title: 'Sobrenome' },
  { id: 3, title: 'E-mail' },
  { id: 4, title: 'Role' },
  { id: 5, title: '', width: '70px' }
]
const searchTerm = ref('')
const searchTermDebounced = useDebounce(searchTerm, 800)
const isFiltersOpen = ref(false)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton,
} = usePagination({ router, route })

const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const currentFilters = ref({
  active: 'true',
  types: null,
  roles: null
})

const filter = computed(() => {
  return objectToJsonString(removeEmptyFields({ ...currentFilters.value, q: term.value }))
})

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

const { data } = useQuery({
  queryKey: ['staffs', getParams],
  queryFn: () => getStaffs(getParams.value),
  select
})

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function select({ results, pagination }: PaginatedResponse<StaffListItem[]>) {
  totalPages.value = pagination.totalPages

  return results
}

const onRegister = () => {
  router.push('/staff/register')
}

const onEdit = (id: string) => {
  router.push(`/staff/${id}`)
}

const onOpenFilters = () => {
  isFiltersOpen.value = true
}

function onCloseFilters(event: any) {
  isFiltersOpen.value = false

  currentFilters.value = {
    ...currentFilters.value,
    ...event
  }

  router.push({ query: { page: '1' } })
}

watch(searchTermDebounced, handleSearch)
</script>
