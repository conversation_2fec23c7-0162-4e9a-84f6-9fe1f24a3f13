<template>
  <ListLayout>
    <template #list>
      <div class="filters">
        <WSelect placeholder="Status" v-model="statusFilter">
          <option value="" selected>Status</option>
          <option value="PENDING">Pendente</option>
          <option value="APPROVED">Aprovado</option>
          <option value="REJECTED">Rejeitado</option>
        </WSelect>
        <WListControllers
          margin="none"
          has-items-per-page-select
          has-pagination
          input-placeholder="Buscar por nome ou e-mail"
          :value="term"
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WInputChange="searchChange"
          @WPaginationPrevious="setPrev"
        >
          <div slot="select-items-per-page">
            <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
              <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
            </WSelect>
          </div>
        </WListControllers>
      </div>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            v-for="header in headers"
            :key="header.id"
            :width="header?.width"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell>
              {{ item.name }}
            </WTableBodyCell>
            <WTableBodyCell>
              <div class="px-2 py-1 rounded bg-green-100">
                {{ item.status }}
              </div>
            </WTableBodyCell>
            <WTableBodyCell width="70px" align="end">
              <div class="staff--list__actions">
                <WButton
                  icon="icChevronArrowRight"
                  variant="secondary"
                  icon-button
                  @click="onEdit(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import { ListLayout, type PaginatedResponse } from '@commons/index'
import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect
} from '@alice-health/wonderland-vue'
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { getStaffSignupRequests } from '@staff/api/queries/getStaffSignupRequests'
import type { StaffSignupRequestListItem } from '@staff/models/staff-signup-request'

const router = useRouter()
const route = useRoute()

const limits = [5, 10, 15]
const headers = [
  { id: 1, title: 'Nome' },
  { id: 2, title: 'Status' },
  { id: 5, title: '', width: '70px' }
]
const searchTerm = ref('')
const searchTermDebounced = useDebounce(searchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const status = computed(() => route.query?.status || null)
const hasFilter = computed(() => term.value || status.value !== null)
const filter = computed(() =>
  hasFilter.value ? objectToJsonString({ q: term.value, status: status.value }) : ''
)

const statusFilter = ref(status.value)

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

const { data } = useQuery({
  queryKey: ['staffs-signup-requests', getParams],
  queryFn: () => getStaffSignupRequests(getParams.value),
  select
})

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  const status = statusFilter.value
  router.push({ query: { ...route.query, page: initials.page, term, status } })
}

function select({ results, pagination }: PaginatedResponse<StaffSignupRequestListItem[]>) {
  totalPages.value = pagination.totalPages

  return results
}

const onEdit = (id: string) => {
  router.push(`/staff-signup-requests/${id}`)
}

watch(searchTermDebounced, handleSearch)
watch(statusFilter, handleSearch)
</script>

<style scoped lang="scss"></style>

<style lang="scss" scoped>
.filters {
  display: grid;
  grid-template-columns: 1fr 5fr;
  gap: var(--gl-spacing-04);
}
</style>
