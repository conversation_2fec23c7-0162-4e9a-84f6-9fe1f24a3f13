<template>
  <FormLayout>
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'staff-signup-requests/list' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <div v-if="isLoadingStaffData" class="staff--form__loading">
        <WSpinner size="xlarge" />
      </div>
      <WGrid
        v-else
        :columns="{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }"
        column-gap="24px"
        row-gap="24px"
      >
        <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
          <WTitle variant="heavy" size="medium">Revisão de cadastro de Especialista</WTitle>
        </WGridItem>

        <StaffPersonalDataReadyOnly :fields="form.healthProfessional" />

        <StaffCouncilDataReadyOnly :fields="form.healthProfessional" />

        <WGridItem class="attendance-cards" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
          <div
            v-for="contact in form.healthProfessional.contacts"
            :key="contact.id"
            class="attendance-card"
          >
            <WIcon
              :icon="contact.modality === 'REMOTE' ? 'icVideoOn' : 'icLocalPin'"
              size="large"
            />

            <div v-if="contact.modality === 'REMOTE'" class="attendance-card__content">
              <WParagraph size="large">Remoto</WParagraph>
            </div>
            <div v-else class="attendance-card__content">
              <WParagraph variant="heavy"
                >Presencial {{ contact.address?.neighborhood }}</WParagraph
              >
              <WParagraph class="attendance-card__content-address" size="large" ellipsis>
                {{ contact.address?.street }}, {{ contact.address?.number }}
                {{ contact.address?.complement }} - {{ contact.address?.neighborhood }} -
                {{ contact.address?.city }} - {{ contact.address?.state }} -
                {{ contact.address?.zipcode }}
              </WParagraph>
            </div>
          </div>
        </WGridItem>

        <ProviderFieldsReadOnly :provider="form.provider" />

        <WGridItem class="staff--form__title" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
          <WTitle variant="heavy" size="small">Informações a serem preenchidas</WTitle>
        </WGridItem>
        <StaffAvatarAndStatus
          :disableShowAppSwitch="true"
          :disableAvatar="false"
          :disableActiveSwitch="false"
          :showAppSwitch="true"
          :fields="form.healthProfessional"
          @avatar-change="onChangeAvatar"
        />
        <WGridItem
          class="staff-category-fields"
          :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }"
        >
          <WSelect
            v-if="buildStaffData?.staffTiers"
            class="flex-1"
            label="Tier teórico"
            v-model="form.healthProfessional.theoristTier"
          >
            <option value="" disabled selected>Selecione o tier teórico</option>
            <option
              v-for="tier in buildStaffData?.staffTiers"
              :key="tier.value"
              :value="tier.value"
            >
              {{ tier.name }}
            </option>
          </WSelect>
          <WSelect
            v-if="buildStaffData?.staffTiers"
            class="flex-1"
            label="Tier de atendimento"
            v-model="form.healthProfessional.tier"
          >
            <option value="" disabled selected>Selecione o tier de atendimento</option>
            <option
              v-for="tier in buildStaffData?.staffTiers"
              :key="tier.value"
              :value="tier.value"
            >
              {{ tier.name }}
            </option>
          </WSelect>
        </WGridItem>
        <StaffSpecialtiesSection
          :show-internal-specialty="false"
          :fields="adaptHealthProfessionalForStaffSpecialties(form.healthProfessional)"
          :specialties="buildStaffData?.specialties || []"
          :sub-specialties="subSpecialtiesList"
          :internal-sub-specialties="[]"
          :hospitals="buildStaffData?.providerUnits || []"
          @change-specialty="onSelectSpecialty"
          @update:subSpecialties="onUpdateSubSpecialties"
        />
      </WGrid>
    </template>
    <template #actions>
      <div class="staff--form__actions">
        <WButton variant="cta" size="large" @click="onSubmit">Aprovar cadastro</WButton>
        <WButton variant="tertiary" size="large" @click="onCancel">Rejeitar cadastro</WButton>
      </div>
    </template>
  </FormLayout>
  <WModal
    id="reject-modal"
    title="Rejeitar Cadastro"
    icon="icAlertCircleFilled"
    :opened="showRejectModal"
    confirmLabel="Rejeitar"
    cancelLabel="Cancelar"
    @WClosed="onHandleRejectModal"
  >
    <div class="reject-modal-content">
      <WParagraph size="large" class="reject-modal-description">
        Essa ação não poderá ser desfeita.
      </WParagraph>
      <WTextfield
        class="reject-modal-input"
        placeholder="Digite o motivo da rejeição..."
        v-model="rejectReason"
      />
    </div>
  </WModal>
</template>

<script setup lang="ts">
import {
  WButton,
  WGrid,
  WGridItem,
  WLink,
  WSelect,
  WTitle,
  WSpinner,
  WParagraph,
  WIcon,
  WModal,
  WTextfield
} from '@alice-health/wonderland-vue'
import { FormLayout } from '@commons/index'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { buildFormStaff } from '../api/queries/getStaffs'
import { computed, ref, watch, type Ref } from 'vue'
import { useQueryString } from '@alice-health/vue-hooks'
import { StaffType, type StaffFormSubSpecialty } from '@staff/models'
import { useRoute, useRouter } from 'vue-router'
import { uploadProfileImage } from '../api/mutations'
import { inject } from 'vue'
import type { SnackbarComponentProps } from '@commons/index'
import { convertDateToLocaleDate } from '@commons/helpers'
import getSubspecialties from '@staff/api/queries/getSubspecialties'
import { getStaffSignupForm } from '@staff/api/queries/getStaffSignupRequests'
import type { StaffSignupForm } from '@staff/models/staff-signup-form'
import StaffAvatarAndStatus from '@staff/components/form-field-groups/StaffAvatarAndStatus.vue'
import type { AdditionalInformation } from '@staff/models/staff-signup-request'
import { approveSignup, rejectSignup } from '@staff/api/mutations/staffSignup'
import ProviderFieldsReadOnly from '@staff/components/form-field-groups/ProviderFieldsReadOnly.vue'
import StaffPersonalDataReadyOnly from '@staff/components/form-field-groups/StaffPersonalDataReadyOnly.vue'
import StaffCouncilDataReadyOnly from '@staff/components/form-field-groups/attendance-fields-group/StaffCouncilDataReadyOnly.vue'
import StaffSpecialtiesSection from '@staff/components/form-field-groups/StaffSpecialtiesSection.vue'

// Helper para converter tipos
const adaptHealthProfessionalForStaffSpecialties = (healthProfessional: any) => ({
  specialty: healthProfessional.selectedSpecialty || null,
  internalSpecialty: null,
  subSpecialties: healthProfessional.selectedSubSpecialties || [],
  internalSubSpecialties: [],
  providerUnits: healthProfessional.providerUnits || []
})

const router = useRouter()
const route = useRoute()
const { objectToJsonString } = useQueryString()
const requestId = computed(() => route.params.id as string)
const snackbar = inject<SnackbarComponentProps>('snackbar')
const staffType = ref(StaffType.COMMUNITY_SPECIALIST)
const subSpecialtiesList = ref<StaffFormSubSpecialty[]>([])
const subSpecialtyId = ref<string | null>(null)

// Modal de rejeição
const showRejectModal = ref(false)
const rejectReason = ref('')

const form: Ref<StaffSignupForm> = ref({
  healthProfessional: {
    active: true,
    showOnApp: false,
    firstName: '',
    lastName: '',
    email: '',
    gender: '',
    councilType: '',
    councilNumber: '',
    councilState: '',
    contacts: [],
    profileBio: '',
    education: '',
    nationalId: '',
    birthdate: '',
    tier: '',
    theoristTier: '',
    specialty: undefined,
    subSpecialties: undefined,
    providerUnits: undefined,
    selectedSpecialty: undefined,
    selectedSubSpecialties: []
  },
  provider: {
    id: '',
    name: '',
    address: {
      number: '',
      street: '',
      neighborhood: '',
      state: '',
      city: '',
      zipcode: '',
      complement: '',
      country: ''
    },
    phones: [],
    cnpj: '',
    accountNumber: ''
  }
})

const getBuildStaffParams = computed(() => ({
  filter: objectToJsonString({ type: 'COMMUNITY_SPECIALIST' })
}))
const getSubSpecialtiesParams = computed(() => ({
  filter: objectToJsonString({ parent_specialty_id: subSpecialtyId.value })
}))

const { isLoading: isLoadingStaffData } = useQuery({
  queryKey: ['staffSignup', requestId.value],
  queryFn: async () => await getStaffSignupForm(requestId.value),
  enabled: !!requestId.value,
  select: async (data) => {
    form.value = {
      ...data,
      healthProfessional: {
        ...data.healthProfessional,
        active: data.healthProfessional?.active ?? true,
        showOnApp: data.healthProfessional?.showOnApp ?? false,
        birthdate: data?.healthProfessional.birthdate
          ? convertDateToLocaleDate(data?.healthProfessional.birthdate)
          : '',
        specialty: data.healthProfessional?.specialty,
        subSpecialties: data.healthProfessional?.subSpecialties,
        councilType: data.healthProfessional?.councilType || '',
        councilNumber: data.healthProfessional?.councilNumber || '',
        councilState: data.healthProfessional?.councilState || ''
      }
    }

    await refetchBuildStaffData()
  }
})

const {
  data: buildStaffData,
  refetch: refetchBuildStaffData,
  isLoading: isLoadingBuildStaffData
} = useQuery({
  queryKey: ['buildStaffForm'],
  queryFn: () => buildFormStaff(getBuildStaffParams.value),
  enabled: false,
  select: (data) => {
    console.log(data)
    return {
      ...data,
      staffRoles: data.staffRoles
        ? data.staffRoles.map((role) => {
            return {
              name: role.name,
              value: role.value
            }
          })
        : [],
      specialties: data.specialties
        ? data.specialties.map((specialty) => {
            return {
              id: specialty.id,
              name: specialty.name
            }
          })
        : [],
      providerUnits: data.providerUnits
        ? data.providerUnits.map((hospital) => {
            return {
              id: hospital.id,
              name: hospital.name
            }
          })
        : []
    }
  }
})

const { refetch: refetchSubSpecialties } = useQuery({
  queryKey: ['subSpecialties'],
  queryFn: () => getSubspecialties(getSubSpecialtiesParams.value),
  enabled: false,
  select: (data) => {
    subSpecialtiesList.value = data

    form.value.healthProfessional.selectedSubSpecialties = []
  }
})

const uploadProfileImageMutation = useMutation({
  mutationKey: ['uploadProfileImage'],
  mutationFn: async (file: File) => {
    return await uploadProfileImage(file)
  }
})

const rejectHealthSpecialistSignup = useMutation({
  mutationKey: ['rejectHealthSpecialistSignup'],
  mutationFn: async (reason: string) => {
    return await rejectSignup(requestId.value, reason)
  }
})

const approveHealthSpecialistSignup = useMutation({
  mutationKey: ['acceptHealthSpecialistSignup'],
  mutationFn: async () => {
    // Validações obrigatórias
    const tier = form.value.healthProfessional.tier?.trim()
    const theoristTier = form.value.healthProfessional.theoristTier?.trim()
    const specialtyId = form.value.healthProfessional.selectedSpecialty?.id
    const subSpecialtyIds =
      form.value.healthProfessional.selectedSubSpecialties?.map(
        (subSpecialty) => subSpecialty.id
      ) || []

    // Verificar campos obrigatórios
    if (!tier) {
      snackbar?.value?.$el.add({
        message: 'Tier é obrigatório',
        icon: 'icAlertCircleOutlined'
      })
      return
    }

    if (!theoristTier) {
      snackbar?.value?.$el.add({
        message: 'Tier teórico é obrigatório',
        icon: 'icAlertCircleOutlined'
      })
      return
    }

    if (!specialtyId) {
      snackbar?.value?.$el.add({
        message: 'Especialidade é obrigatória',
        icon: 'icAlertCircleOutlined'
      })
      return
    }

    if (subSpecialtyIds.length === 0) {
      snackbar?.value?.$el.add({
        message: 'Pelo menos uma subespecialidade é obrigatória',
        icon: 'icAlertCircleOutlined'
      })
      return
    }

    const additionalInfo: AdditionalInformation = {
      tier,
      theoristTier,
      showOnApp: form.value.healthProfessional.showOnApp || false,
      specialtyId,
      subSpecialtyIds
    }

    return await approveSignup(requestId.value, additionalInfo)
  }
})

// Funções do modal de rejeição
const onOpenRejectModal = () => {
  showRejectModal.value = true
  rejectReason.value = ''
}

const onCloseRejectModal = () => {
  showRejectModal.value = false
  rejectReason.value = ''
}

const onHandleRejectModal = (event: CustomEvent) => {
  if (event.detail === 'confirm') {
    if (!rejectReason.value.trim()) {
      snackbar?.value?.$el.add({
        message: 'Motivo da rejeição é obrigatório',
        icon: 'icAlertCircleOutlined'
      })
      return
    }

    rejectHealthSpecialistSignup.mutate(rejectReason.value.trim(), {
      onSuccess: () => {
        onCloseRejectModal()
        snackbar?.value?.$el.add({
          message: 'Cadastro rejeitado com sucesso!',
          icon: 'icCheckOutlined'
        })
        router.push({ name: 'staff-signup-requests/list', query: route.query })
      },
      onError: () => {
        snackbar?.value?.$el.add({
          message: 'Erro ao rejeitar cadastro.',
          icon: 'icError'
        })
      }
    })
  } else {
    // Cancelar - apenas fechar o modal
    onCloseRejectModal()
  }
}

const onCancel = () => {
  onOpenRejectModal()
}

const onSubmit = async () => {
  approveHealthSpecialistSignup.mutate(undefined, {
    onSuccess: () => {
      snackbar?.value?.$el.add({
        message: 'Cadastro aprovado com sucesso!',
        icon: 'icCheckOutlined'
      })
      router.push({ name: 'staff-signup-requests/list', query: route.query })
    },
    onError: () => {
      snackbar?.value?.$el.add({
        message: 'Erro ao aprovar cadastro.',
        icon: 'icError'
      })
    }
  })
}

const onChangeAvatar = async (event: File) => {
  const file = event
  uploadProfileImageMutation.mutate(file, {
    onSuccess: (data: any) => {
      form.value.healthProfessional.profileImageUrl = data
      snackbar?.value?.$el.add({
        message: 'Foto de perfil atualizada com sucesso!',
        icon: 'icCheckCircle'
      })
    },
    onError: () => {
      snackbar?.value?.$el.add({
        message: 'Erro ao atualizar foto de perfil.',
        icon: 'icError'
      })
    }
  })
}

const onChangeSpecialty = (specialtyId: string) => {
  const selectedSpecialty = buildStaffData.value?.specialties?.find((s) => s.id === specialtyId)
  if (selectedSpecialty) {
    form.value.healthProfessional.selectedSpecialty = {
      id: selectedSpecialty.id,
      name: selectedSpecialty.name
    }
  }
  subSpecialtyId.value = specialtyId
  refetchSubSpecialties()
}

const onSelectSpecialty = (specialtyId: string) => {
  onChangeSpecialty(specialtyId)
}

const onUpdateSubSpecialties = (subSpecialties: StaffFormSubSpecialty[]) => {
  form.value.healthProfessional.selectedSubSpecialties = subSpecialties.map((sub) => ({
    id: sub.id,
    name: sub.name
  }))
}

watch(staffType, () => refetchBuildStaffData())
</script>

<style scoped lang="scss">
.staff--form__actions {
  display: flex;
  gap: var(--gl-spacing-04);
}

.staff--form {
  &__title {
    margin-top: var(--gl-spacing-12);
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
}

.attendance-card {
  align-items: center;
  background-color: var(--sys-color-surface-container-active);
  border: var(--gl-border-width-xs) solid var(--sys-color-stroke-active);
  border-radius: var(--gl-border-radius-sm);
  display: inline-flex;
  gap: var(--gl-spacing-03);
  padding: var(--gl-spacing-05) var(--gl-spacing-06);

  &__content {
    display: flex;
    flex-flow: column;
    gap: var(--gl-spacing-01);
    flex: 1;

    &-address {
      max-width: 300px;
    }
  }
}

.attendance-cards {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-02);
}

.staff-category-fields {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--gl-spacing-08);
}

.reject-modal-content {
  width: 100%;
}

.reject-modal-description {
  width: 100%;
  font-weight: 500;
  margin-bottom: 10px;
}

.reject-modal-input {
  width: 100%;
}
</style>
