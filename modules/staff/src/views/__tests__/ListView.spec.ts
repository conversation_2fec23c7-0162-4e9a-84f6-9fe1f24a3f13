import { screen, fireEvent, within } from '@testing-library/vue'
import { server } from '@commons/services/mockServer'
import { customRender, waitFor } from '@staff/tests'
import ListView from '@staff/views/ListView.vue'
import { list } from '@staff/tests/mocks'

describe('StaffRequest - ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(list))

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: 'Novo cadastro'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()

    expect(screen.getByText('staff name')).toBeInTheDocument()
    expect(screen.getByText('staff last name')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('renders filters button', async () => {
    customRender(ListView)

    const filtersButton = await screen.findByRole('button', { name: 'Filtros' })

    expect(filtersButton).toBeInTheDocument()

    await fireEvent.click(filtersButton)

    const modal = await screen.findByRole('dialog')

    expect(modal).toBeInTheDocument()

    expect(within(modal).getByText('Status da conta')).toBeInTheDocument()
    expect(within(modal).getByText('Tipo de staff')).toBeInTheDocument()
    expect(within(modal).getByText('Role')).toBeInTheDocument()
  })
})
