import { screen, fireEvent, within } from '@testing-library/vue'
import { customRender, waitFor } from '@staff/tests'
import { server } from '@commons/services/mockServer'
import { staffSignupRequestsList } from '@staff/tests/mocks'
import SignupRequestListView from '@staff/views/SignupRequestsListView.vue'

describe('StaffSignupRequest - ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(staffSignupRequestsList))

  it('renders list table with signup requests', async () => {
    customRender(SignupRequestListView)

    const table = await screen.findByRole('table')
    const withinTable = within(table)

    expect(withinTable.getByText('Nome')).toBeInTheDocument()
    expect(withinTable.getByText('Status')).toBeInTheDocument()
    expect(withinTable.getByText('John Doe')).toBeInTheDocument()
    expect(withinTable.getByText('Pendente')).toBeInTheDocument()

    expect(await screen.findByPlaceholderText('Status')).toBeInTheDocument()
  })

  it('renders filter on the screen', async () => {
    customRender(SignupRequestListView)

    expect(await screen.findByPlaceholderText('Status')).toBeInTheDocument()
  })
})
