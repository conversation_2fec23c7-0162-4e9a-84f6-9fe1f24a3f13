import { screen } from '@testing-library/vue'
import { server } from '@commons/services/mockServer'
import { customRender } from '@staff/tests'
import CreateView from '@staff/views/CreateView.vue'
import { buildStaffForm, getById } from '@staff/tests/mocks'

// Use the MSW handlers for mocking API responses

describe('StaffRequest - CreateView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(buildStaffForm, getById))

  it('renders the create staff form', async () => {
    customRender(CreateView)

    expect(
      await screen.findByText('Criar um usuário')
    ).toBeInTheDocument()

    expect(await screen.findByRole('button', { name: '<PERSON><PERSON>' })).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: '<PERSON><PERSON><PERSON>' })).toBeInTheDocument()
  })
})
