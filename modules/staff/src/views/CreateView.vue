<template>
  <FormLayout>
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'staff/list' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <div v-if="isLoadingStaffData" class="staff--form__loading">
        <WSpinner size="xlarge" />
      </div>
      <WGrid
        v-else
        :columns="{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }"
        column-gap="24px"
        row-gap="24px"
      >
        <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
          <WTitle variant="heavy" size="large">{{
            staffId ? 'Editar um usuário' : 'Criar um usuário'
          }}</WTitle>
        </WGridItem>
        <WGridItem>
          <WSelect label="Tipo de staff" v-model="form.type" @w-change="onChangeStaffType">
            <option value="" disabled selected>Selecione o tipo</option>
            <option v-for="type in types" :key="type" :value="type">
              {{ getNameByStaffType(type) }}
            </option>
          </WSelect>
        </WGridItem>

        <WGridItem>
          <WAutocomplete
            v-model="staffRole"
            label="Role"
            placeholder="Selecione a role"
            :items="buildStaffData?.staffRoles"
            search-by-key="name"
            @w-change="onChangeStaffRole"
          />
        </WGridItem>

        <DynamicRoleFormFields
          v-if="buildStaffData"
          :staffType="(form.type as StaffType)"
          :fields="form"
          :council-types="buildStaffData?.councilTypes"
          :specialties="buildStaffData?.specialties"
          :sub-specialties="subSpecialtiesList"
          :internal-sub-specialties="internalSubSpecialtiesList"
          :hospitals="buildStaffData?.providerUnits"
          :staff-tiers="buildStaffData?.staffTiers"
          @avatar-change="onChangeAvatar"
          @change-specialty="onChangeSpecialty"
          @change-internal-specialty="onChangeInternalSpecialty"
        />
      </WGrid>
    </template>
    <template #actions>
      <div class="staff--form__actions">
        <WButton variant="cta" size="large" @click="onSubmit">Salvar</WButton>
        <WButton variant="tertiary" size="large" @click="onCancel">Cancelar</WButton>
      </div>
    </template>
  </FormLayout>
</template>

<script setup lang="ts">
import {
  WAutocomplete,
  WButton,
  WGrid,
  WGridItem,
  WLink,
  WSelect,
  WTitle,
  WSpinner
} from '@alice-health/wonderland-vue'
import { FormLayout } from '@commons/index'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { buildFormStaff, getStaff } from '../api/queries/getStaffs'
import { computed, ref, watch, type Ref } from 'vue'
import { useQueryString } from '@alice-health/vue-hooks'
import {
  Gender,
  StaffType,
  type StaffForm,
  type StaffFormSubSpecialty,
  type StaffRequest
} from '@staff/models'
import DynamicRoleFormFields from '../components/DynamicRoleFormFields.vue'
import { useRoute, useRouter } from 'vue-router'
import { createStaff, editStaff, uploadProfileImage } from '../api/mutations'
import { inject } from 'vue'
import type { SnackbarComponentProps } from '@commons/index'
import { convertDateToISO, convertDateToLocaleDate } from '@commons/helpers'
import getSubspecialties from '@staff/api/queries/getSubspecialties'

const router = useRouter()
const route = useRoute()
const { objectToJsonString } = useQueryString()
const staffId = computed(() => route.params.id as string)
const snackbar = inject<SnackbarComponentProps>('snackbar')
const types = Object.keys(StaffType)
const staffType = ref('')
const staffRole = ref<{ name: string; value: string } | null | string | undefined>(null)
const subSpecialtiesList = ref<StaffFormSubSpecialty[]>([])
const internalSubSpecialtiesList = ref<StaffFormSubSpecialty[]>([])
const subSpecialtyId = ref<string | null>(null)
const internalSubSpecialtyId = ref<string | null>(null)

const form: Ref<StaffForm> = ref({
  type: '',
  role: '',
  email: '',
  gender: '',
  firstName: '',
  lastName: '',
  active: true,
  urlSlug: '',
  quote: '',
  profileBio: '',
  council: {
    number: '',
    state: '',
    type: ''
  },
  specialty: '',
  subSpecialties: [],
  internalSpecialty: '',
  internalSubSpecialties: [],
  providerUnits: [],
  qualifications: [],
  tier: '',
  theoristTier: '',
  curiosity: '',
  showOnApp: false,
  education: [],
  healthSpecialistScore: '',
  deAccreditationDate: '',
  contacts: [],
  paymentFrequency: 0,
  attendsToOnCall: false,
  onCallPaymentMethod: '',
  onVacationStart: '',
  onVacationUntil: '',
  memedStatus: null
})

const getBuildStaffParams = computed(() => ({
  filter: objectToJsonString({ type: form.value.type })
}))
const getSubSpecialtiesParams = computed(() => ({
  filter: objectToJsonString({ parent_specialty_id: subSpecialtyId.value })
}))
const getInternalSubSpecialtiesParams = computed(() => ({
  filter: objectToJsonString({ parent_specialty_id: internalSubSpecialtyId.value })
}))

const getNameByStaffType = (type: string) => {
  return StaffType[type as keyof typeof StaffType]
}

const { isLoading: isLoadingStaffData } = useQuery({
  queryKey: ['staff', staffId.value],
  queryFn: () => getStaff(staffId.value),
  enabled: staffId.value ? true : false,
  select: async (data) => {
    form.value = {
      ...data
    }

    await refetchBuildStaffData()

    const currentRole = buildStaffData.value?.staffRoles.find(
      (role: any) => role.value === data.role
    )

    staffRole.value = currentRole

    form.value = {
      ...data,
      birthdate: data?.birthdate ? convertDateToLocaleDate(data?.birthdate) : '',
      council: {
        ...data?.council,
        type: data?.council?.type ? String(data?.council?.type) : ''
      }
    }

    if (data.providerUnits) {
      form.value.providerUnits = data.providerUnits.map(() => {
        return buildStaffData.value?.providerUnits.find((providerUnit) => {
          return {
            id: providerUnit?.id,
            name: providerUnit?.name
          }
        })
      })
    }

    if (data.specialty) {
      const currentSpecialty = buildStaffData.value?.specialties.find(
        (specialty) => specialty.id === data?.specialty
      )

      form.value.specialty = currentSpecialty
    }

    if (data.internalSpecialty) {
      const currentInternalSpecialty = buildStaffData.value?.specialties.find(
        (specialty) => specialty.id === data?.internalSpecialty
      )
      form.value.internalSpecialty = currentInternalSpecialty
    }

    if (data?.subSpecialties) {
      subSpecialtyId.value = data?.specialty

      await refetchSubSpecialties()

      const subSpecialtiesAdpated = data.subSpecialties.map((subSpecialty: any) => {
        const currentSubSpecialty = subSpecialtiesList.value.find(
          (specialty) => specialty.id === subSpecialty
        )
        return {
          id: subSpecialty,
          name: currentSubSpecialty?.name || 'Sem nome definido'
        }
      })

      form.value.subSpecialties = subSpecialtiesAdpated || []
    }

    if (data?.internalSubSpecialties) {
      internalSubSpecialtyId.value = data?.internalSpecialty

      await refetchInternalSubSpecialties()

      const internalSubSpecialtiesAdpated = data?.internalSubSpecialties?.map(
        (subSpecialty: any) => {
          const currentSubSpecialty = internalSubSpecialtiesList.value.find(
            (specialty) => specialty.id === subSpecialty
          )
          return {
            id: subSpecialty,
            name: currentSubSpecialty?.name || 'Sem nome definido'
          }
        }
      )

      form.value.internalSubSpecialties = internalSubSpecialtiesAdpated || []
    }
  }
})

const {
  data: buildStaffData,
  refetch: refetchBuildStaffData,
  isLoading: isLoadingBuildStaffData
} = useQuery({
  queryKey: ['buildStaffForm'],
  queryFn: () => buildFormStaff(getBuildStaffParams.value),
  enabled: false,
  select: (data) => {
    return {
      ...data,
      staffRoles: data.staffRoles
        ? data.staffRoles.map((role) => {
            return {
              name: role.name,
              value: role.value
            }
          })
        : [],
      specialties: data.specialties
        ? data.specialties.map((specialty) => {
            return {
              id: specialty.id,
              name: specialty.name
            }
          })
        : [],
      providerUnits: data.providerUnits
        ? data.providerUnits.map((hospital) => {
            return {
              id: hospital.id,
              name: hospital.name
            }
          })
        : []
    }
  }
})

const { refetch: refetchSubSpecialties } = useQuery({
  queryKey: ['subSpecialties'],
  queryFn: () => getSubspecialties(getSubSpecialtiesParams.value),
  enabled: false,
  select: (data) => {
    subSpecialtiesList.value = data

    form.value.subSpecialties = []
  }
})

const { refetch: refetchInternalSubSpecialties } = useQuery({
  queryKey: ['internalSubSpecialties'],
  queryFn: () => getSubspecialties(getInternalSubSpecialtiesParams.value),
  enabled: false,
  select: (data) => {
    internalSubSpecialtiesList.value = data

    form.value.internalSubSpecialties = []
  }
})

const uploadProfileImageMutation = useMutation({
  mutationKey: ['uploadProfileImage'],
  mutationFn: async (file: File) => {
    return await uploadProfileImage(file)
  }
})

const createStaffMutation = useMutation({
  mutationKey: ['createStaff'],
  mutationFn: async (staff: StaffRequest) => {
    return await createStaff(staff)
  },
  onError: (error: any) => {
    let message = 'Erro ao criar usuário.'
    if (error?.response?.data) {
      const errData = error.response.data
      if (Array.isArray(errData.data) && errData.data.length > 0 && errData.data[0].message) {
        message = errData.data[0].message
      } else if (errData.message) {
        message = errData.message
      }
    }
    snackbar?.value?.$el.add({
      message,
      icon: 'icAlertCircleOutlined'
    })
  }
})

const editStaffMutation = useMutation({
  mutationKey: ['editStaff'],
  mutationFn: async (staff: StaffRequest) => {
    return await editStaff(staffId.value, staff)
  },
  onError: (error: any) => {
    let message = 'Erro ao atualizar usuário.'
    if (error?.response?.data) {
      const errData = error.response.data
      if (Array.isArray(errData.data) && errData.data.length > 0 && errData.data[0].message) {
        message = errData.data[0].message
      } else if (errData.message) {
        message = errData.message
      }
    }
    snackbar?.value?.$el.add({
      message,
      icon: 'icAlertCircleOutlined'
    })
  }
})

const onChangeStaffType = async (event: CustomEvent) => {
  const value = event.detail
  staffType.value = value
  form.value.type = value

  staffRole.value = null
}

const onChangeAvatar = async (event: File) => {
  const file = event
  uploadProfileImageMutation.mutate(file, {
    onSuccess: (data: any) => {
      form.value.profileImageUrl = data
      snackbar?.value?.$el.add({
        message: 'Foto de perfil atualizada com sucesso!',
        icon: 'icCheckCircle'
      })
    },
    onError: () => {
      snackbar?.value?.$el.add({
        message: 'Erro ao atualizar foto de perfil.',
        icon: 'icError'
      })
    }
  })
}

const onChangeStaffRole = async (event: CustomEvent) => {
  const value = event.detail
  staffRole.value = buildStaffData.value?.staffRoles.find((role) => role.name === value) ?? null
  form.value.role = buildStaffData.value?.staffRoles.find((role) => role.name === value) ?? null
}

const onChangeSpecialty = (specialtyId: string) => {
  form.value.specialty = specialtyId
  subSpecialtyId.value = specialtyId

  refetchSubSpecialties()
}

const onChangeInternalSpecialty = (specialtyId: string) => {
  form.value.internalSpecialty = specialtyId
  internalSubSpecialtyId.value = specialtyId
  refetchInternalSubSpecialties()
}

const onCancel = () => {
  router.push({ name: 'staff/list', query: route.query })
}

const adaptFormToStaffRequest = (form: StaffForm): StaffRequest => {
  let payload: Partial<StaffRequest> = {}

  if (form.type === 'PITAYA' || form.type === 'HEALTH_ADMINISTRATIVE') {
    payload = {
      type: form.type,
      role: staffRole.value?.value || null,
      profileImageUrl: form.profileImageUrl || null,
      active: form.active,
      firstName: form.firstName,
      lastName: form.lastName,
      email: form.email,
      gender: (form.gender as keyof typeof Gender) || null
    }
  }

  if (form.type === 'HEALTH_PROFESSIONAL' || form.type === 'PARTNER_HEALTH_PROFESSIONAL') {
    payload = {
      type: form.type,
      role: staffRole.value?.value || null,
      profileImageUrl: form.profileImageUrl || null,
      active: form.active,
      showOnApp: form.showOnApp,
      firstName: form.firstName,
      lastName: form.lastName,
      email: form.email,
      gender: (form.gender as keyof typeof Gender) || null,
      profileBio: form.profileBio || null,
      education: form.education || null,
      qualifications: form.qualifications || null,
      council: form.council,
      specialty: form.specialty?.id || null,
      subSpecialties: form.subSpecialties?.map((subSpecialty) => subSpecialty.id) || null,
      internalSpecialty: form.internalSpecialty?.id || null,
      internalSubSpecialties:
        form.internalSubSpecialties?.map((subSpecialty) => subSpecialty.id) || null,
      providerUnits: form.providerUnits?.map((unit) => unit.id) || null,
      memedStatus: form.memedStatus || null,
      contacts: form.contacts || null,
      attendsToOnCall: form.attendsToOnCall || null,
      onCallPaymentMethod: form.onCallPaymentMethod || null,
      tier: form.tier || null,
      deAccreditationDate: form.deAccreditationDate
        ? convertDateToISO(form.deAccreditationDate)
        : null
    }
  }

  if (form.type === 'COMMUNITY_SPECIALIST') {
    payload = {
      type: form.type,
      role: staffRole.value?.value || null,
      profileImageUrl: form.profileImageUrl || null,
      active: form.active,
      showOnApp: form.showOnApp,
      firstName: form.firstName,
      lastName: form.lastName,
      email: form.email,
      gender: (form.gender as keyof typeof Gender) || null,
      profileBio: form.profileBio || null,
      education: form.education || null,
      qualifications: form.qualifications || null,
      council: form.council,
      specialty: form.specialty?.id || null,
      subSpecialties: form.subSpecialties?.map((subSpecialty) => subSpecialty.id) || null,
      providerUnits: form.providerUnits?.map((unit) => unit.id) || null,
      memedStatus: form.memedStatus || null,
      contacts: form.contacts || null,
      attendsToOnCall: form.attendsToOnCall || null,
      onCallPaymentMethod: form.onCallPaymentMethod || null,
      tier: form.tier || null,
      theoristTier: form.theoristTier || null,
      deAccreditationDate: form.deAccreditationDate
        ? convertDateToISO(form.deAccreditationDate)
        : null,
      healthSpecialistScore: form.healthSpecialistScore || null
    }
  }

  if (form.type === 'EXTERNAL_PAID_HEALTH_PROFESSIONAL') {
    payload = {
      type: form.type,
      role: staffRole.value?.value || null,
      profileImageUrl: form.profileImageUrl || null,
      active: form.active,
      firstName: form.firstName,
      lastName: form.lastName,
      email: form.email,
      gender: (form.gender as keyof typeof Gender) || null,
      council: form.council,
      deAccreditationDate: form.deAccreditationDate
        ? convertDateToISO(form.deAccreditationDate)
        : null
    }
  }

  if (staffId.value) {
    payload = {
      ...payload,
      id: staffId.value
    }
  }

  return payload
}

const onSubmit = async () => {
  const staffPayload = adaptFormToStaffRequest(form.value)

  if (staffId.value) {
    editStaffMutation.mutate(staffPayload, {
      onSuccess: () => {
        snackbar?.value?.$el.add({
          message: 'Usuário atualizado com sucesso!',
          icon: 'icCheckOutlined'
        })
      }
    })
  } else {
    createStaffMutation.mutate(staffPayload, {
      onSuccess: () => {
        snackbar?.value?.$el.add({
          message: 'Usuário criado com sucesso!',
          icon: 'icCheckOutlined'
        })
      }
    })
  }
}

watch(staffType, () => refetchBuildStaffData())
</script>

<style scoped lang="scss">
.staff--form__actions {
  display: flex;
  gap: var(--gl-spacing-04);
}

.staff--form {
  &__title {
    margin-top: var(--gl-spacing-12);
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
}
</style>
