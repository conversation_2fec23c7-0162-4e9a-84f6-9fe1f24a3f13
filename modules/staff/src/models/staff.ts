export interface StaffListItem {
    id: string
    firstName: string
    lastName: string
    email: string
    role: string
    active: boolean
}

export enum StaffType {
    PITAYA = "Pitaya",
    COMMUNITY_SPECIALIST = "Especialista",
    HEALTH_PROFESSIONAL = "Profissional de Saúde Pitaya",
    PARTNER_HEALTH_PROFESSIONAL = "Profissional de Saúde Parceiro",
    HEALTH_ADMINISTRATIVE = "Administrador de Unidade de Saúde",
    EXTERNAL_PAID_HEALTH_PROFESSIONAL = "Profissional de saúde externo remunerado"
}

export enum SpecialistTier {
    OPT_IN = "Opt-in",
    TALENTED = "Talented",
    EXPERT = "Expert",
    SUPER_EXPERT = "Super Expert",
    ULTRA_EXPERT = "Ultra Expert"
}

export enum Qualification {
    ISO_9001 = "ISO_9001",
    JOINT_COMMISSION_INTERNATIONAL = "JOINT_COMMISSION_INTERNATIONAL",
}

export enum Gender {
    MALE = "Masculino",
    FEMALE = "Feminino",
    NON_BINARY = "Não Binário",
    NO_ANSWER = "Sem Resposta"
}

export enum SpecialistScore {
    NEED_TO_RAISE_THE_BAR = "Precisa subir a barra",
    IS_RAISING_THE_BAR = "Tá subindo a barra",
    DOMINATING = "Dominando a parada",
    LIVING_THE_IMPOSSIBLE = "Vivendo o impossível",
    DOES_NOT_APPLY = "Não se aplica"
}

export interface Contact {
    id: string
    address: Address,
    phones: PhoneNumber[],
    scheduleAvailabilityDays: number,
    modality: keyof typeof ModalityType,
    availableDays: AvailableDay[],
    website: string
}

export interface Address {
    id: string,
    street: string,
    number: string,
    complement: string,
    neighborhood: string,
    state?: string,
    city?: string,
    zipcode: string,
    label?: string,
    active?: boolean,
    latitude?: string,
    longitude?: string
}

export interface PhoneNumber {
    title: string,
    phone: string,
    type: keyof typeof PhoneType
}

export enum PhoneType {
    PHONE = "Fixo",
    MOBILE = "Móvel",
    WHATSAPP = "WhatsApp"
}

export enum OnCallPaymentMethod {
    HEALTH_INSTITUTION = "Instituição de Saúde",
    ALICE = "Alice"
}

export enum ModalityType {
    PRESENTIAL = "Presencial",
    REMOTE = "Online"
}

export interface AvailableDay {
    weekDay: Weekday,
}

export enum Weekday {
    MONDAY = "Segunda-feira",
    TUESDAY = "Terça-feira",
    WEDNESDAY = "Quarta-feira",
    THURSDAY = "Quinta-feira",
    FRIDAY = "Sexta-feira"
}

export interface Council {
    number?: string
    state?: string
    type?: number
    name?: string
}

