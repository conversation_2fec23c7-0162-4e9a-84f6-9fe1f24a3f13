import type {
    Contact,
    Qualification,
    SpecialistScore,
    SpecialistTier,
    Council,
    Gender,
    OnCallPaymentMethod,
    StaffType
} from "./staff"

export interface StaffRequest {
    id: string
    type?: keyof typeof StaffType | null
    role?: string | null
    firstName: string
    lastName: string
    gender: keyof typeof Gender | null
    fullName?: string | null
    email?: string | null
    nationalId?: string | null
    birthdate?: string | null
    profileImageUrl?: string | null
    active?: boolean | null
    version?: number | null
    urlSlug?: string | null
    quote?: string | null
    profileBio?: string | null
    council?: Council | null
    specialty?: string | null
    subSpecialties?: string[] | null
    internalSpecialty?: string | null
    internalSubSpecialties?: string[] | null
    providerUnits?: string[] | null
    qualifications?: Qualification[] | null
    tier?: SpecialistTier | null
    theoristTier?: SpecialistTier | null
    curiosity?: string | null
    showOnApp?: boolean | null
    education?: string[] | null
    healthSpecialistScore?: keyof typeof SpecialistScore | null
    deAccreditationDate?: string | null
    contacts?: Contact[] | null
    paymentFrequency?: number | null
    attendsToOnCall?: boolean | null
    onCallPaymentMethod?: OnCallPaymentMethod | null
    onVacationStart?: string | null
    onVacationUntil?: string | null
    memedStatus?: string | null
}