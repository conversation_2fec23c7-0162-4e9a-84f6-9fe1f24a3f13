import type { PhoneNumber } from "./staff"

export interface StaffFormSpecialty {
  id: string
  name: string
  type: string
  active?: boolean
  requireSpecialist?: boolean
  generateGeneralistSubSpecialty?: boolean
  cboCode?: {
    id: string
    code: string
    description: string
  }
  internal?: boolean
  urlSlug?: string
  attentionLevel?: string
  isTherapy?: boolean
  createdAt?: string
  version?: number
  isAdvancedAccess?: boolean
}

export interface StaffFormSubSpecialty {
  id: string
  name: string
  type: string
  active?: boolean
  parentSpecialtyId?: string
  requireSpecialist?: boolean
  generateGeneralistSubSpecialty?: boolean
  attentionLevel?: string
  internal?: boolean
  urlSlug?: string
  isTherapy?: boolean
  createdAt?: string
  version?: number
  isAdvancedAccess?: boolean
}

export interface StaffFormProviderUnit {
  id: string
  name: string
  status: string
  type: string
  administrativeStaff?: string[]
  attendanceTypes?: string[]
  brand?: string
  clinicalStaffIds?: string[]
  cnes?: string
  cnpj?: string
  contractOrigin?: string
  createdAt?: string
  hasHospitalHealthTeam?: boolean
  medicalSpecialtyIds?: string[]
  medicalSpecialtyProfile?: string[]
  medicalSubSpecialtyIds?: string[]
  phones?: PhoneNumber[]
  providerId?: string
  providerUnitGroupId?: string
  qualifications?: string[]
  searchTokens?: string
  showOnApp?: boolean
  showOnScheduler?: boolean
  updatedAt?: string
  updatedBy?: {
    environmentName?: string
    userId?: string
    userType?: string
  }
  urlSlug?: string
  version?: number
  workingPeriods?: string[]
}

export interface StaffFormStaffRole {
  id: string
  name: string
  value: string
}

export interface StaffFormStaffTier {
  id: string
  name: string
  value: string
}

export interface StaffFormStaffScore {
  id: string
  name: string
  value: string
}

export interface StaffFormCouncilType {
  id: number
  name: string
}

export interface StaffFormFields {
  specialties: StaffFormSpecialty[]
  providerUnits: StaffFormProviderUnit[]
  staffRoles: StaffFormStaffRole[]
  staffTiers: StaffFormStaffTier[]
  staffScore: StaffFormStaffScore[]
  councilTypes: StaffFormCouncilType[]
}
