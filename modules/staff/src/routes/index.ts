import type { RouteR<PERSON>ordRaw, NavigationGuardWithThis } from 'vue-router'

const staffRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/staff',
      name: 'staff',
      beforeEnter,
      children: [
        {
          path: 'list',
          name: 'staff/list',
          component: async () => await import('@staff/views/ListView.vue')
        },
        {
          path: 'register',
          name: 'staff/register',
          component: async () => await import('@staff/views/CreateView.vue')
        },
        {
          path: ':id',
          name: 'staff/edit',
          component: async () => await import('@staff/views/CreateView.vue')
        }
      ]
    },
    {
      path: '/staff-signup-requests',
      name: 'staff-signup-requests',
      beforeEnter,
      children: [
        {
          path: 'list',
          name: 'staff-signup-requests/list',
          component: async () => await import('@staff/views/SignupRequestsListView.vue')
        },
        {
          path: ':id',
          name: 'staff-signup-requests/detail',
          component: async () => await import('@staff/views/SignupRequestDetailView.vue')
        }
      ]
    }
  ]
}

export default staffRoutes
