import { string, object, boolean, number, array } from 'zod'

export const staffForm = object({
    type: string().min(1, 'Tipo de profissional é obrigatório'),
    role: string().min(1, 'Cargo é obrigatório'),
    firstName: string().min(1, 'Nome é obrigatório'),
    lastName: string().min(1, 'Sobrenome é obrigatório'),
    email: string().email().min(1, 'Email é obrigatório'),
    gender: string().min(1, 'Gênero é obrigatório'),
    active: boolean().default(true),

    nationalId: string().optional(),
    birthdate: string().optional(),
    profileImageUrl: string().optional(),
    urlSlug: string().optional(),
    quote: string().optional(),
    profileBio: string().optional(),
    council: object({
        number: string().optional(),
        state: string().optional(),
        type: string().optional()
    }).optional(),
    specialty: object({
        id: string(),
        name: string()
    }).optional(),
    subSpecialties: array(object({
        id: string(),
        name: string()
    })).optional(),
    internalSpecialty: object({
        id: string(),
        name: string()
    }).optional(),
    internalSubSpecialties: array(object({
        id: string(),
        name: string()
    })).optional(),
    providerUnits: array(object({
        id: string(),
        name: string()
    })).default([]),
    qualifications: array(string()).optional(),
    tier: string().optional(),
    theoristTier: string().optional(),
    curiosity: string().optional(),
    showOnApp: boolean().optional(),
    education: array(string()).optional(),
    healthSpecialistScore: string().optional(),
    deAccreditationDate: string().optional(),
    contacts: array(object({
        id: string(),
        address: object({
            id: string().optional(),
            street: string().optional(),
            number: string().optional(),
            complement: string().optional(),
            neighborhood: string().optional(),
            state: string().optional(),
            city: string().optional(),
            zipcode: string().optional(),
            label: string().optional(),
            active: boolean().optional(),
            latitude: string().optional(),
            longitude: string().optional()
        }).optional(),
        phones: array(object({
            title: string(),
            phone: string(),
            type: string()
        })).optional(),
        scheduleAvailabilityDays: number().optional(),
        modality: string().optional(),
        availableDays: array(object({
            weekDay: string()
        })).optional(),
        website: string().optional()
    })).optional(),
    paymentFrequency: number().optional(),
    attendsToOnCall: boolean().optional(),
    onCallPaymentMethod: string().optional(),
    onVacationStart: string().optional(),
    onVacationUntil: string().optional()
})
