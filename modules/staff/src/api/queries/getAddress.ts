import { useQueryString } from '@alice-health/vue-hooks'
import http from '@http/index'
import type { AddressDetailsResponse, AddressSearchResponse } from '@staff/models'

export const getAddress = async (params?: Record<string, string>) => {
const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''
  const { data } = await http.get<AddressSearchResponse[]>(`/staff/address/search${queryString}`)
  return data
}

export const getAddressDetails = async (placeId: string) => {
  const { data } = await http.get<AddressDetailsResponse>(`/staff/address/${placeId}`)
  return data
}
