import { useQueryString } from '@alice-health/vue-hooks'
import http from '@http/index'
import type { StaffFormSubSpecialty } from '@staff/models'

const getSubspecialties = async (params?: Record<string, string>) => {
const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''
  const { data } = await http.get<StaffFormSubSpecialty[]>(`/staff/sub_specialties${queryString}`)
  return data
}

export default getSubspecialties
