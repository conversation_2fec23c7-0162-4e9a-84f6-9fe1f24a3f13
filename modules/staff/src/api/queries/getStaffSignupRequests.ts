import type { PaginatedResponse } from '@commons/index'
import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'
import type { StaffSignupRequestListItem } from '@staff/models/staff-signup-request'
import type { StaffSignupForm } from '@staff/models/staff-signup-form'

export const getStaffSignupRequests = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<PaginatedResponse<StaffSignupRequestListItem[]>>(
    `/staff_signup_review${queryString}`
  )
  return data
}

export const getStaffSignupForm = async (id: string) => {
  const { data } = await http.get<StaffSignupForm>(`/staff_signup_review/${id}`)
  return data
}
