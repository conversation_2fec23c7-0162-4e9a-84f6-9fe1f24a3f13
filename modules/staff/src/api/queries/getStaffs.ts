import type { PaginatedResponse } from '@commons/index'
import http from '@http/index'
import type { StaffFormFields, StaffListItem, StaffForm } from '@staff/models'
import { useQueryString } from '@alice-health/vue-hooks'

export const getStaffs = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<PaginatedResponse<StaffListItem[]>>(`/staff${queryString}`)
  return data
}


export const buildFormStaff = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<StaffFormFields>(`/staff/build_staff${queryString}`)
  return data
}


export const searchProviderUnits = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get(`/staff/search_provider_units${queryString}`)
  return data
}

export const getStaff = async (id: string) => {
  const { data } = await http.get<StaffForm>(`/staff/${id}`)
  return data
}