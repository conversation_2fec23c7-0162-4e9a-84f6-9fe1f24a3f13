import http from '@http/index'
import type { AdditionalInformation } from '@staff/models/staff-signup-request'

export const rejectSignup = async (requestId: string, reason: string) => {
  await http.post(`/staff_signup_review/${requestId}/reject`, { rejectionReason: reason })
}

export const approveSignup = async (requestId: string, information: AdditionalInformation) => {
  await http.post(`/staff_signup_review/${requestId}/approve`, information)
}
