/// <reference types='vitest' />
import { fileURLToPath } from 'url'
import { defineConfig, searchForWorkspaceRoot } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import * as path from 'path'
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin'

export default defineConfig({
  root: __dirname,
  cacheDir: '../../../node_modules/.vite/modules/core/auth',

  plugins: [
    vue(),
    nxViteTsPaths(),
    dts({
      entryRoot: 'src',
      tsConfigFilePath: path.join(__dirname, 'tsconfig.lib.json'),
      skipDiagnostics: true
    })
  ],

  server: {
    fs: {
      allow: [searchForWorkspaceRoot(process.cwd()), '../../../vitest.setup.ts']
    }
  },

  resolve: {
    alias: {
      '@http': fileURLToPath(new URL('../http/src', import.meta.url))
    }
  },

  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [ nxViteTsPaths() ],
  // },

  // Configuration for building your library.
  // See: https://vitejs.dev/guide/build.html#library-mode
  build: {
    outDir: '../../../dist/modules/core/auth',
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true
    },
    lib: {
      // Could also be a dictionary or array of multiple entry points.
      entry: 'src/index.ts',
      name: 'auth',
      fileName: 'index',
      // Change this to the formats you want to support.
      // Don't forget to update your package.json as well.
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      // External packages that should not be bundled into your library.
      external: []
    }
  },

  test: {
    globals: true,
    cacheDir: '../../../node_modules/.vitest',
    environment: 'jsdom',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    setupFiles: ['../../../vitest.setup.ts'],
    reporter: ['default'],
    coverage: {
      reportsDirectory: '../../../coverage/modules/core/auth',
      provider: 'v8',
      reporter: ['lcov', 'html']
    }
  }
})
