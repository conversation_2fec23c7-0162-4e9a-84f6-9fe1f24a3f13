import { screen, render } from '@testing-library/vue'
import LoginView from './LoginView.vue'
import { createRouter, createWebHistory } from 'vue-router'

describe('LoginView', () => {
  vi.mock('modules/core/auth/src/firebase/auth.ts', async () => ({}))

  const router = createRouter({
    history: createWebHistory('/'),
    routes: [{ path: '/', name: 'home', component: async () => ({}) }]
  })

  it('renders properly', async () => {
    render(LoginView, { global: { plugins: [router] } })

    expect(screen.getByAltText(/Alice logo/i)).toBeInTheDocument()

    expect(
      await screen.findByRole('heading', {
        level: 2,
        name: 'Olá! Vamos tornar o mundo mais saudável'
      })
    ).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Entrar com Google/i })).toBeInTheDocument()
  })
})
