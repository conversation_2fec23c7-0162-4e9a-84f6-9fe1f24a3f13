<template>
  <div class="ab-login">
    <section>
      <div class="ab-login__content">
        <div class="ab-login__header">
          <img src="/modules/core/auth/src/assets/logo.png" alt="Alice logo" />
          <WParagraph>Backoffice</WParagraph>
        </div>

        <WTitle level="2" size="medium" variant="heavy">
          Olá! Vamos tornar o mundo mais saudável
        </WTitle>

        <WButton @click="onLogin" variant="cta" size="large" class="ab-login__button">
          Entrar com Google
        </WButton>
      </div>
    </section>
    <section class="ab-login__background">
      <picture>
        <img
          class="ab-login__image"
          src="/modules/core/auth/src/assets/bg.png"
          alt="Imagem de fundo da tela de login - na imagem um uma sala de espera com um sofá azul, uma poltrona branca, uma mesa de centro de madeira e paredes de vidro em volta"
          :width="740"
          :height="720"
          decoding="async"
          loading="lazy"
        />
      </picture>
    </section>
  </div>
</template>
<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { WButton, WTitle, WParagraph } from '@alice-health/wonderland-vue'
import { loginWithGoogle } from '../firebase/auth'
import { isAuthenticated } from '../services/auth.service'

const router = useRouter()

const onLogin = async () => {
  await loginWithGoogle().then(() => {
    router.push('/')
  })
}

onMounted(() => {
  if (isAuthenticated()) {
    router.push('/')
  }
})
</script>
<style scoped lang="scss">
.ab-login {
  display: grid;
  grid-template-columns: 1fr 2fr;
  height: 100vh;
  overflow: hidden;

  &__background {
    width: 100%;
    height: 100%;
    position: relative;
    pointer-events: none;

    @media screen and (max-width: 768px) {
      display: none;
    }
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  &__content {
    padding: var(--gl-spacing-12);
  }

  &__header {
    display: flex;
    margin-bottom: var(--gl-spacing-20);
    justify-content: space-between;
    align-items: center;
  }

  &__button {
    margin-top: var(--gl-spacing-06);
  }
}
</style>
