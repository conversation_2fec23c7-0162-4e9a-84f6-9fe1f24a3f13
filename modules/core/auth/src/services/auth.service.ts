import type { OAuthCredential, UserCredential } from 'firebase/auth'

const ACCESS_TOKEN_KEY = 'accessToken'
const USER_KEY = 'user'
const EXPIRATION_TIME = 'expirationTime'

export const isAuthenticated = () => {
  const token = getToken()
  return token !== null
}

export const setToken = (token: OAuthCredential['accessToken']) => {
  localStorage.setItem(ACCESS_TOKEN_KEY, JSON.stringify(token))
}

export const getToken = () => {
  const tokenValue = localStorage.getItem(ACCESS_TOKEN_KEY)

  if (!tokenValue) {
    return null
  }

  return JSON.parse(tokenValue)
}

export const removeToken = () => {
  localStorage.removeItem(ACCESS_TOKEN_KEY)
}

export const setUser = (user: UserCredential['user']) => {
  localStorage.setItem('user', JSON.stringify(user))
}

export const getUser = () => {
  const userPayload = localStorage.getItem(USER_KEY)

  if (!userPayload) {
    return null
  }

  return JSON.parse(userPayload)
}

export const removeUser = () => {
  localStorage.removeItem('user')
}

export const getRoles = () => {
  const user = getUser()
  return user?.roles || []
}

export const setExpirationTime = (expirationTime: string) => {
  localStorage.setItem(EXPIRATION_TIME, JSON.stringify(new Date(expirationTime).getTime()))
}

export const getExpirationTime = () => {
  const expirationTime = localStorage.getItem(EXPIRATION_TIME)

  if (!expirationTime) {
    return null
  }

  return JSON.parse(expirationTime)
}
