import type { User } from 'firebase/auth'
import { getToken, getUser, removeToken, removeUser, setToken, setUser } from './auth.service'

describe('Auth service', () => {
  it('should firebase set, get and remove token', async () => {
    expect(getToken()).toBe(null)

    const content = 'token_test'

    setToken(content)

    expect(getToken()).toMatch(content)

    removeToken()

    expect(getToken()).toBe(null)
  })

  it('should firebase set, get and remove user', async () => {
    expect(getUser()).toBe(null)

    const userPayload: Partial<User> = {
      displayName: 'Test User',
      email: '<EMAIL>',
      photoURL: 'https://via.placeholder.com/100'
    }

    setUser(userPayload)

    expect(getUser()).toMatchObject(userPayload)

    removeUser()

    expect(getUser()).toBe(null)
  })
})
