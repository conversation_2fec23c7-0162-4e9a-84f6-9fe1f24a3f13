import { initializeApp } from 'firebase/app'
import { GoogleAuthProvider, getAuth, signInWithPopup } from 'firebase/auth'
import type { AuthError } from 'firebase/auth'
import http from '@http/index'

import {
  getExpirationTime,
  removeToken,
  removeUser,
  setExpirationTime,
  setToken,
  setUser
} from '../services/auth.service'
import { FIREBASE_CONFIG } from './config/firebase.config'

export const firebaseAppInstance = initializeApp(FIREBASE_CONFIG)
export const auth = getAuth(firebaseAppInstance)

export const loginWithGoogle = async () => {
  const provider = new GoogleAuthProvider()
  try {
    const result = await signInWithPopup(auth, provider)

    const token = await result.user.getIdToken()
    await checkUser(token)
    const expirationTime = (await result.user.getIdTokenResult()).expirationTime
    const user = result.user

    setToken(token)
    setExpirationTime(expirationTime)
    setUser(user)
  } catch (error: unknown) {
    const errorObject = error as AuthError
    const errorCode = errorObject.code
    const errorMessage = errorObject.message

    console.error(errorCode, errorMessage)
  }
}

export const refreshToken = async () => {
  const expirationTimeDate = getExpirationTime()
  const now = new Date().getTime()

  if (expirationTimeDate < now) {
    const token = await auth.currentUser?.getIdToken(true)

    if (auth.currentUser === null) {
      console.error('No user found')
      return
    }

    const expirationTime = (await auth.currentUser.getIdTokenResult()).expirationTime

    setToken(token)
    setExpirationTime(expirationTime)
  }
}

export const listenTokenChanged = () => {
  auth.onIdTokenChanged(async (user) => {
    if (user) {
      const token = await user.getIdToken()
      setToken(token)
    }
  })
}

async function checkUser(idToken: string) {
  if (!idToken) return null

  try {
    await http.post('/signIn', { idToken })
  } catch (error) {
    console.error('An error happened on checkUser', error)
    logout()
  }
}

export const logout = async () => {
  await auth
    .signOut()
    .then(() => {
      removeToken()
      removeUser()
    })
    .catch((error) => {
      console.error('An error happened on logout', error)
    })
}
