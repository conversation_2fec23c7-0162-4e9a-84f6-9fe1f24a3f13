import type { VNode } from 'vue'

// TO DO: Estamos em definição da arquitetura de permissionamento, esse arquivo será implementado assim que tivermos a definição de roles

// import { getUser } from "../services/auth.service";

export const roleDirective = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  beforeMount(el: Element, binding: any, vnode: VNode, prevNode: VNode) {
    // const { value } = binding;
    // const roles = value.split(',');
    // const user = getUser()
    // const userRoles = user?.roles;
    // const hasRole = roles.some((role) => userRoles.includes(role));
    // if (!hasRole) {
    //     el.style.display = 'none';
    // }
  }
}
