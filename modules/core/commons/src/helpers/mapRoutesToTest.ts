import type { RouteRecordRaw } from 'vue-router'

export function mapRoutesToTests(routes: RouteRecordRaw[], isRoot = true): RouteRecordRaw[] {
  const mappedRoutes = routes.map((route) => {
    if (route.children) {
      return {
        ...route,
        children: mapRoutesToTests(route.children, false)
      }
    } else if (route.component) {
      return {
        ...route,
        component: () => ({ mock: true })
      }
    } else {
      return { ...route }
    }
  })

  if (isRoot) {
    return [...mappedRoutes, { path: '/', name: 'home', component: () => ({ mock: true }) }]
  }
  return mappedRoutes
}
