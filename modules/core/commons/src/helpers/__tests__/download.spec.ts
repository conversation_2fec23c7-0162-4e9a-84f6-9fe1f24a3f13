import { describe, it, expect, vi, beforeEach } from 'vitest'
import { downloadFile } from '../download'

describe('downloadFile', () => {
  const mockCreateElement = vi.fn()
  const mockAppendChild = vi.fn()
  const mockRemoveChild = vi.fn()
  const mockClick = vi.fn()

  beforeEach(() => {
    // Mock DOM elements and methods
    const mockLink = {
      href: '',
      download: '',
      click: mockClick
    }
    mockCreateElement.mockReturnValue(mockLink)
    document.createElement = mockCreateElement
    document.body.appendChild = mockAppendChild
    document.body.removeChild = mockRemoveChild
  })

  it('should create and trigger download link', () => {
    const testUrl = 'https://example.com/file.csv'
    downloadFile(testUrl)

    // Verify link creation and configuration
    expect(mockCreateElement).toHaveBeenCalledWith('a')
    expect(mockAppendChild).toHaveBeenCalled()
    expect(mockClick).toHaveBeenCalled()
    expect(mockRemoveChild).toHaveBeenCalled()
  })

  it('should set the correct href and download attributes', () => {
    const testUrl = 'https://example.com/file.csv'
    const mockLink = {
      href: '',
      download: '',
      click: mockClick
    }
    mockCreateElement.mockReturnValue(mockLink)

    downloadFile(testUrl)

    expect(mockLink.href).toBe(testUrl)
    expect(mockLink.download).toBe('')
  })
})
