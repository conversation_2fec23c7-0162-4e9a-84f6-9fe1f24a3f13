import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'
import { describe, it, expect, vi } from 'vitest'
import { mapRoutesToTests } from '../mapRoutesToTest'

type MockComp = () => Promise<{ string: boolean }>

const mockRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/mock-route',
      name: 'mock-route',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'mock-route-list',
          component: async () => ({ component: true })
        },
        {
          path: 'create',
          name: 'mock-route-create',
          component: async () => ({ component: true })
        },
        {
          path: ':id',
          name: 'mock-route-edit',
          component: async () => ({ component: true })
        }
      ]
    }
  ]
}

describe('mapRoutesToTests', () => {
  it('should map routes and replace components with async components, and add home route', async () => {
    const beforeEnter = vi.fn()
    const routes = mockRoutes(beforeEnter)
    const [module, home] = mapRoutesToTests(routes)

    expect(module).toMatchObject({
      path: '/mock-route',
      name: 'mock-route',
      beforeEnter
    })

    expect(home).toMatchObject({
      path: '/',
      name: 'home'
    })

    expect(module.children?.length).toBe(3)
    expect(module.children?.[0]?.path).toBe('')
    expect(module.children?.[0]?.name).toBe('mock-route-list')
    expect(await (module.children?.[0]?.component as MockComp)()).toMatchObject({
      mock: true
    })
    expect(module.children?.[1]?.path).toBe('create')
    expect(module.children?.[1]?.name).toBe('mock-route-create')
    expect(await (module.children?.[1]?.component as MockComp)()).toMatchObject({
      mock: true
    })
    expect(module.children?.[2]?.path).toBe(':id')
    expect(module.children?.[2]?.name).toBe('mock-route-edit')
    expect(await (module.children?.[2]?.component as MockComp)()).toMatchObject({
      mock: true
    })
  })
})
