export function convertDateToISO(date: string): string {
  const [day, month, year] = date.split('/')
  return `${year}-${month}-${day}`
}

export function convertDateToLocaleDate(date: string, locale = 'pt-BR'): string {
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    const [year, month, day] = date.split('-').map(Number)
    const localDate = new Date(year, month - 1, day)
    return localDate.toLocaleDateString(locale)
  }
  return new Date(date).toLocaleDateString(locale)
}