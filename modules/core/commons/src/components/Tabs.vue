<script setup lang="ts">
import type { Tab } from '../models/Tab.model'

import { ref, nextTick, onMounted, onBeforeUnmount, watch } from 'vue'

import { WIcon, WParagraph } from '@alice-health/wonderland-vue'
import { useRouter } from 'vue-router'

/*
 * Custom Types
 */

type Events = {
  (e: 'onChangeTab', tab: Tab): void
}

type Props = {
  /**
   * The tabs to be displayed, a list of objects with the following properties:
   * - `id`: The id of the tab
   * - `label`: The label of the tab
   * - `icon`: The icon of the tab (optional)
   * - `route`: The route of the tab (optional)
   */
  tabs: Tab[]
  /**
   * The initial tab to be selected (optional, defaults to the first list item)
   */
  initialActive?: Tab['id']
}

/*
 * Vue Definitions
 */

const emit = defineEmits<Events>()
const props = defineProps<Props>()

/*
 * Hooks
 */

const router = useRouter()

/*
 * Refs
 */

const getInitialActiveTab = () => {
  const currentRouteName = router.currentRoute.value.name
  const matchingTab = props.tabs.find(
    (tab) => !tab.disabled && tab.route?.name === currentRouteName
  )
  if (matchingTab) return matchingTab.id
  if (props.initialActive) {
    const initialTab = props.tabs.find((tab) => tab.id === props.initialActive && !tab.disabled)
    if (initialTab) return initialTab.id
  }
  // Retorna o primeiro tab não desabilitado
  const firstEnabled = props.tabs.find((tab) => !tab.disabled)
  return firstEnabled ? firstEnabled.id : null
}

const activeTab = ref<Tab['id'] | null>(getInitialActiveTab())

/*
 * Methods
 */

const updateTabIndicator = () => {
  const activeElement = document.querySelector('.list__item.active') as HTMLElement

  if (!activeElement) return

  const list = document.querySelector('.list') as HTMLElement

  if (list) {
    list.style.setProperty('--tab-width', `${activeElement.clientWidth}px`)
    list.style.setProperty('--tab-left', `${activeElement.offsetLeft}px`)
  }
}

const handleClick = (tab: Tab) => {
  if (tab.disabled) return
  emit('onChangeTab', tab)
  activeTab.value = tab.id
  nextTick(() => updateTabIndicator())
}

const handleKeyDown = (tab: Tab) => {
  if (tab.disabled) return
  emit('onChangeTab', tab)
  activeTab.value = tab.id
  nextTick(() => updateTabIndicator())
}

/*
 * Lifecycle Hooks
 */

onMounted(() => {
  updateTabIndicator()

  window.addEventListener('resize', updateTabIndicator)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateTabIndicator)
})

/*
 * Watchers
 */

watch(
  () => router.currentRoute.value.name,
  (newRouteName) => {
    const matchingTab = props.tabs.find((tab) => !tab.disabled && tab.route?.name === newRouteName)
    if (matchingTab) {
      activeTab.value = matchingTab.id
      nextTick(() => updateTabIndicator())
    }
  }
)
</script>

<template>
  <div class="list" key="tabs" role="tablist">
    <component
      v-for="(tab, index) in props.tabs"
      role="tab"
      :is="tab.route ? 'router-link' : 'div'"
      :to="tab.route"
      :key="tab.label"
      :class="{
        list__item: true,
        active: activeTab === tab.id,
        disabled: tab.disabled
      }"
      :tabindex="tab.disabled ? -1 : index + 1"
      :aria-disabled="tab.disabled ? 'true' : 'false'"
      @click="handleClick(tab)"
      @keydown.enter="handleKeyDown(tab)"
      @keydown.space="handleKeyDown(tab)"
    >
      <WIcon v-if="tab.icon" :icon="tab.icon" />

      <WParagraph variant="heavy" :mode="activeTab === tab.id ? 'active' : 'primary'">
        {{ tab.label }}
      </WParagraph>
    </component>
  </div>
</template>

<style lang="scss" scoped>
.list {
  display: flex;
  flex-direction: row;

  width: fit-content;
  gap: 0;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: var(--sys-color-content-active);
    transition: all 0.3s ease;
    width: var(--tab-width, 0);
    left: var(--tab-left, 0);
  }

  &__item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    align-content: center;
    gap: var(--gl-spacing-04);
    padding: var(--gl-spacing-04);

    cursor: pointer;

    &.active {
      color: var(--sys-color-content-active);
    }

    &.disabled {
      opacity: 0.5;
      pointer-events: none;
      cursor: not-allowed;
    }
  }
}
</style>
