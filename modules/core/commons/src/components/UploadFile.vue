<script lang="ts" setup>
import { ref, Transition } from 'vue'

import { WButton, WIcon, WParagraph } from '@alice-health/wonderland-vue'

type Props = {
  /**
   * If true, the component will show a list of files above the upload box
   */
  listFiles?: boolean
  /**
   * Whether the file input is multiple, default is `false`
   */
  multiple?: boolean
  /**
   * Accepts a list of file types separated by commas, e.g. `'image/*,application/pdf'`, or `'.csv, .pdf'`
   */
  accept?: string
  /**
   * Max size of the file in bytes
   */
  maxSize?: number
  /**
   * Disable the component
   */
  disabled?: boolean
  /**
   * Disable the delete button on file list
   */
  disableDeleteButton?: boolean
}

type Emits = {
  (e: 'onFileChange', files: File[] | null): void
}

const props = withDefaults(defineProps<Props>(), {
  accept: '',
  listFiles: false,
  multiple: false,
  /** 5MB default max size */
  maxSize: 1024 * 1024 * 5,
  disabled: false,
  disableDeleteButton: false
})

const files = ref<File[] | null>(null)
const dragOver = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)

const emit = defineEmits<Emits>()

const handleFileChange = (event: Event) => {
  const input = event?.target as HTMLInputElement

  let selectedFiles = input?.files ? Array.from(input?.files) : []

  if (props.accept) {
    const acceptList = props.accept.split(',').map((a) => a.trim())
    selectedFiles = selectedFiles.filter((file) =>
      acceptList.some((type) => file.type === type || file.name.endsWith(type.replace('.', '')))
    )
  }
  if (props.maxSize) {
    selectedFiles = selectedFiles.filter((file) => file.size <= props.maxSize!)
  }

  if (props.multiple) {
    const currentFiles = files.value ? [...files.value] : []

    selectedFiles.forEach((file) => {
      if (!currentFiles.some((f) => f.name === file.name && f.size === file.size)) {
        currentFiles.push(file)
      }
    })
    files.value = currentFiles
  } else {
    files.value = selectedFiles.slice(0, 1)
  }
  emit('onFileChange', files.value && files.value.length > 0 ? files.value : null)

  if (input) input.value = ''
}

const handleDragOver = (event: DragEvent) => {
  if (props.disabled) return

  event.preventDefault()
  dragOver.value = true
}

const handleDragLeave = () => {
  if (props.disabled) return

  dragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  if (props.disabled) return

  event?.preventDefault()
  dragOver.value = false

  const dt = event.dataTransfer
  let droppedFiles = dt?.files ? Array.from(dt.files) : []

  if (props.accept) {
    const acceptList = props.accept.split(',').map((a) => a.trim())
    droppedFiles = droppedFiles.filter((file) =>
      acceptList.some((type) => file.type === type || file.name.endsWith(type.replace('.', '')))
    )
  }

  if (props.maxSize) {
    droppedFiles = droppedFiles.filter((file) => file.size <= props.maxSize!)
  }

  if (props.multiple) {
    const currentFiles = files.value ? [...files.value] : []
    droppedFiles.forEach((file) => {
      if (!currentFiles.some((f) => f.name === file.name && f.size === file.size)) {
        currentFiles.push(file)
      }
    })
    files.value = currentFiles
  } else {
    files.value = droppedFiles.slice(0, 1)
  }

  emit('onFileChange', files.value && files.value.length > 0 ? files.value : null)
}

const handleClick = () => {
  if (props.disabled) return

  inputRef?.value?.click()
}

const handleRemoveFile = (file: File, event?: Event) => {
  if (props.disabled) return

  event?.stopPropagation()

  if (!files.value) return

  files.value = files.value.filter((f) => f !== file)

  emit('onFileChange', files.value && files.value.length > 0 ? files.value : null)
}
</script>

<template>
  <div
    :class="{
      'upload-file': true,
      'drag-over': dragOver,
      disabled: disabled
    }"
    @drop="handleDrop"
    @click="handleClick"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
    role="button"
    :aria-disabled="disabled"
  >
    <input
      ref="inputRef"
      type="file"
      class="upload-file__input"
      :multiple="multiple"
      :accept="accept"
      @change="handleFileChange"
      data-testid="file-input"
    />

    <div
      :class="{
        'upload-file__icon': true,
        disabled: disabled
      }"
    >
      <Transition name="fade-scale" mode="out-in">
        <WIcon
          size="xlarge"
          :key="dragOver ? 'icHandToolOpened' : 'icUpload'"
          :icon="dragOver ? 'icHandToolOpened' : 'icUpload'"
        />
      </Transition>
    </div>

    <WParagraph :mode="dragOver ? 'on-brand' : 'primary'" size="large">
      {{ dragOver ? 'Solte o arquivo aqui' : 'Arraste um arquivo CSV ou clique para selecionar' }}
    </WParagraph>
  </div>

  <div v-if="listFiles" class="list-files">
    <div v-for="file in files" :key="file.name" class="list-files__item">
      <WParagraph variant="plain" size="large">
        {{ file?.name }}
      </WParagraph>

      <WButton
        iconButton
        icon="icTrash"
        size="medium"
        variant="secondary"
        :disabled="disableDeleteButton"
        @click="handleRemoveFile(file, $event)"
        :aria-label="`Remove ${file.name}`"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.upload-file {
  display: flex;
  padding: var(--gl-spacing-12) var(--gl-spacing-06);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-self: stretch;

  gap: var(--gl-spacing-06);
  width: 100%;
  max-width: 680px;
  height: 100%;
  min-height: 220px;

  border: 2px dashed var(--sys-color-stroke-active);
  border-radius: var(--gl-border-radius-md);
  background: var(--gl-color-shades-white);

  cursor: pointer;

  transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;

    width: var(--gl-spacing-16);
    height: var(--gl-spacing-16);

    border-radius: var(--gl-border-radius-md);
    background: var(--gl-color-shades-blue-00);

    &.disabled {
      background: var(--sys-color-content-disabled);
    }
  }

  &__input {
    display: none;
  }

  &.drag-over {
    background-color: var(--sys-color-content-info);
  }

  &.disabled {
    cursor: not-allowed;
    border: 2px dashed var(--sys-color-stroke-active);
    background: var(--sys-color-surface-container-disabled-light);
  }
}

.list-files {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-06);

  &__item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    padding: var(--gl-spacing-04);

    border: 1px solid var(--sys-color-stroke-active);
    border-radius: var(--gl-border-radius-sm);
    background: var(--sys-color-surface-container-idle);

    width: 100%;
  }
}

.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: opacity 0.25s, transform 0.25s;
}
.fade-scale-enter-from,
.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.85);
}
.fade-scale-enter-to,
.fade-scale-leave-from {
  opacity: 1;
  transform: scale(1);
}
</style>
