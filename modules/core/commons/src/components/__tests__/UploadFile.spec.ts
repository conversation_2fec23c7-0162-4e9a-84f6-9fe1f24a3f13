import { describe, it, expect } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/vue'

import { WButton, WIcon, WParagraph } from '@alice-health/wonderland-vue'

import UploadFile from '../UploadFile.vue'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'

type EmittedEvents = {
  onFileChange: [File[] | null]
}

describe('UploadFile', () => {
  const renderComponent = (props = {}) => {
    return render(UploadFile, {
      props: {
        ...props
      },
      global: {
        components: {
          WButton,
          WIcon,
          WParagraph
        }
      }
    })
  }

  it('renders correctly with default props', async () => {
    renderComponent()

    await waitForWonderlandComponents(expect)

    expect(screen.getByRole('button')).toBeInTheDocument()
    expect(screen.getByText('Arraste um arquivo CSV ou clique para selecionar')).toBeInTheDocument()
  })

  it('shows correct text when not dragging', async () => {
    renderComponent()

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Arraste um arquivo CSV ou clique para selecionar')).toBeInTheDocument()
  })

  it('shows correct text when dragging', async () => {
    renderComponent()

    const dropZone = screen.getByRole('button')
    await fireEvent.dragOver(dropZone)

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Solte o arquivo aqui')).toBeInTheDocument()
  })

  it('handles file input change correctly', async () => {
    const { emitted } = renderComponent()

    const file = new File(['test'], 'test.csv', { type: 'text/csv' })

    await waitForWonderlandComponents(expect)

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file]
    })

    await fireEvent.change(input)
    const emittedEvents = emitted() as EmittedEvents

    expect(emittedEvents.onFileChange).toBeTruthy()

    const files = emittedEvents.onFileChange[0]?.[0]

    expect(files).toEqual([file])
  })

  it('filters files by accept type', async () => {
    const { emitted } = renderComponent({ accept: 'text/csv' })

    const file1 = new File(['test'], 'test.csv', { type: 'text/csv' })
    const file2 = new File(['test'], 'test.pdf', { type: 'application/pdf' })

    await waitForWonderlandComponents(expect)

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file1, file2]
    })

    await fireEvent.change(input)
    const emittedEvents = emitted() as EmittedEvents
    const files = emittedEvents.onFileChange[0]?.[0]

    expect(files).toEqual([file1])
  })

  it('filters files by max size', async () => {
    const { emitted } = renderComponent({ maxSize: 1024 }) // 1KB

    const file1 = new File(['test'], 'test.csv', { type: 'text/csv' })
    const file2 = new File(['a'.repeat(2048)], 'large.csv', { type: 'text/csv' })

    await waitForWonderlandComponents(expect)

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file1, file2]
    })

    await fireEvent.change(input)
    const emittedEvents = emitted() as EmittedEvents
    const files = emittedEvents.onFileChange[0]?.[0]

    expect(files).toEqual([file1])
  })

  it('handles multiple files correctly', async () => {
    const { emitted } = renderComponent({ multiple: true })

    const file1 = new File(['test1'], 'test1.csv', { type: 'text/csv' })
    const file2 = new File(['test2'], 'test2.csv', { type: 'text/csv' })

    await waitForWonderlandComponents(expect)

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file1, file2]
    })

    await fireEvent.change(input)
    const emittedEvents = emitted() as EmittedEvents
    const files = emittedEvents.onFileChange[0]?.[0]

    expect(files).toEqual([file1, file2])
  })

  it('prevents duplicate files in multiple mode', async () => {
    const { emitted } = renderComponent({ multiple: true })

    const file = new File(['test'], 'test.csv', { type: 'text/csv' })

    await waitForWonderlandComponents(expect)

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file, file]
    })

    await fireEvent.change(input)
    const emittedEvents = emitted() as EmittedEvents
    const files = emittedEvents.onFileChange[0]?.[0]
    expect(files).toEqual([file])
  })

  it('shows file list when listFiles prop is true', async () => {
    renderComponent({ listFiles: true })

    await waitForWonderlandComponents(expect)

    const file = new File(['test'], 'test.csv', { type: 'text/csv' })

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file]
    })

    await fireEvent.change(input)

    expect(screen.getByText('test.csv')).toBeInTheDocument()
  })

  it('handles file removal correctly', async () => {
    const { emitted } = renderComponent({ listFiles: true })

    const file = new File(['test'], 'test.csv', { type: 'text/csv' })

    await waitForWonderlandComponents(expect)

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file]
    })

    await fireEvent.change(input)
    const removeButton = screen.getByLabelText(/remove/i)

    await fireEvent.click(removeButton)

    const emittedEvents = emitted() as EmittedEvents
    const lastEvent =
      emittedEvents.onFileChange?.[emittedEvents.onFileChange.length - 1]?.[0] ?? null
    expect(lastEvent).toBeNull()
  })

  it('disables component when disabled prop is true', async () => {
    renderComponent({ disabled: true })

    await waitForWonderlandComponents(expect)

    const dropZone = screen.getByRole('button')

    expect(dropZone).toHaveAttribute('aria-disabled', 'true')
  })

  it('disables delete button when disableDeleteButton prop is true', async () => {
    renderComponent({ listFiles: true, disableDeleteButton: true })

    await waitForWonderlandComponents(expect)

    const file = new File(['test'], 'test.csv', { type: 'text/csv' })

    const input = screen.getByTestId('file-input') as HTMLInputElement

    Object.defineProperty(input, 'files', {
      value: [file]
    })

    await fireEvent.change(input)

    await waitFor(() => {
      const removeButton = screen.getByLabelText(/remove/i)
      const nativeButton = removeButton.querySelector('button')

      expect(nativeButton).toBeDisabled()
    })
  })
})
