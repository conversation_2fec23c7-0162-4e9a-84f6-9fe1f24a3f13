import { render, screen } from '@testing-library/vue'
import userEvent from '@testing-library/user-event'

import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'
import { createRouter, createWebHistory } from 'vue-router'
import { describe, it, expect, beforeEach } from 'vitest'

import Tabs from '../Tabs.vue'

const mockTabs = [
  { id: 'tab1', label: 'Tab 1', icon: 'icInfo' },
  { id: 'tab2', label: 'Tab 2', disabled: true },
  { id: 'tab3', label: 'Tab 3', route: { name: 'test-route' } }
]

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/test-route', name: 'test-route', component: { template: '<div>Test Route</div>' } }
  ]
})

describe('Tabs', () => {
  beforeEach(() => {
    router.push('/')
  })

  it('renders all tabs correctly', () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    mockTabs.forEach((tab) => {
      expect(screen.getByText(tab.label)).toBeTruthy()
    })
  })

  it('selects the first tab by default', () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const firstTab = screen.getByText(mockTabs[0].label).closest('[role="tab"]')
    expect(firstTab?.classList.contains('active')).toBe(true)
  })

  it('selects initial tab when initialTabId is provided', async () => {
    await router.push('/')
    render(Tabs, {
      props: { tabs: mockTabs, initialActive: 'tab3' },
      global: { plugins: [router] }
    })

    const thirdTab = screen.getByText(mockTabs[2].label).closest('[role="tab"]')

    expect(thirdTab?.classList.contains('active')).toBe(true)
  })

  it('emits onChangeTab event when clicking a tab', async () => {
    const user = userEvent.setup()
    const { emitted } = render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    await user.click(screen.getByText(mockTabs[2].label))

    expect(emitted().onChangeTab[0]).toEqual([mockTabs[2]])
  })

  it('changes active tab when clicking', async () => {
    const user = userEvent.setup()
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    await user.click(screen.getByText(mockTabs[2].label))

    const thirdTab = screen.getByText(mockTabs[2].label).closest('[role="tab"]')
    expect(thirdTab?.classList.contains('active')).toBe(true)
  })

  it('handles keyboard navigation with Enter key', async () => {
    const user = userEvent.setup()

    const { emitted } = render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const thirdTab = screen.getByText(mockTabs[2].label).closest('[role="tab"]') as HTMLElement
    if (thirdTab) {
      thirdTab.focus()
      await user.keyboard('[Enter]')
    }

    expect(emitted().onChangeTab[0]).toEqual([mockTabs[2]])
  })

  it('handles keyboard navigation with Space key', async () => {
    const user = userEvent.setup()
    const { emitted } = render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const thirdTab = screen.getByText(mockTabs[2].label).closest('[role="tab"]') as HTMLElement

    if (thirdTab) {
      thirdTab.focus()
      await user.keyboard('[Space]')
    }

    expect(emitted().onChangeTab[0]).toEqual([mockTabs[2]])
  })

  it('renders icons when provided', async () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    await waitForWonderlandComponents(expect)

    const icon = await screen.findByRole('img', { name: 'icInfo' })

    expect(icon).toBeInTheDocument()
  })

  it('uses router-link for tabs with routes', () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const tabWithRoute = screen.getByText(mockTabs[2].label).closest('a')
    expect(tabWithRoute).toBeTruthy()
  })

  it('maintains proper tab order for keyboard navigation', () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    mockTabs.forEach((tab, index) => {
      const tabElement = screen.getByText(tab.label).closest('[role="tab"]')

      if (tab.disabled) {
        expect(tabElement?.getAttribute('tabindex')).toBe('-1')
      } else {
        expect(tabElement?.getAttribute('tabindex')).toBe((index + 1).toString())
      }
    })
  })

  it('has proper ARIA attributes for accessibility', () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const tablist = screen.getByRole('tablist')
    expect(tablist).toBeTruthy()

    const tabs = screen.getAllByRole('tab')
    expect(tabs).toHaveLength(mockTabs.length)
  })

  it('updates active tab when route changes', async () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const firstTab = screen.getByText(mockTabs[0].label).closest('[role="tab"]')

    expect(firstTab?.classList.contains('active')).toBe(true)

    await router.push({ name: 'test-route' })

    const thirdTab = screen.getByText(mockTabs[2].label).closest('[role="tab"]')

    expect(thirdTab?.classList.contains('active')).toBe(true)
  })

  it('renders disabled tab with correct class and attributes', () => {
    render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const disabledTab = screen.getByText('Tab 2').closest('[role="tab"]')

    expect(disabledTab?.classList.contains('disabled')).toBe(true)
    expect(disabledTab?.getAttribute('aria-disabled')).toBe('true')
    expect(disabledTab?.getAttribute('tabindex')).toBe('-1')
  })

  it('does not emit or activate when clicking a disabled tab', async () => {
    const user = userEvent.setup()

    const { emitted } = render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    await user.click(screen.getByText('Tab 2'))
    expect(emitted().onChangeTab).toBeUndefined()

    const disabledTab = screen.getByText('Tab 2').closest('[role="tab"]')
    expect(disabledTab?.classList.contains('active')).toBe(false)
  })

  it('does not emit or activate when using keyboard on a disabled tab', async () => {
    const user = userEvent.setup()

    const { emitted } = render(Tabs, {
      props: { tabs: mockTabs },
      global: { plugins: [router] }
    })

    const disabledTab = screen.getByText('Tab 2').closest('[role="tab"]') as HTMLElement

    if (disabledTab) {
      disabledTab.focus()

      await user.keyboard('[Enter]')
      await user.keyboard('[Space]')
    }

    expect(emitted().onChangeTab).toBeUndefined()
    expect(disabledTab?.classList.contains('active')).toBe(false)
  })

  it('skips disabled tab for initialActive and default selection', () => {
    render(Tabs, {
      props: { tabs: mockTabs, initialActive: 'tab2' },
      global: { plugins: [router] }
    })

    const firstTab = screen.getByText('Tab 1').closest('[role="tab"]')
    expect(firstTab?.classList.contains('active')).toBe(true)

    const disabledTab = screen.getByText('Tab 2').closest('[role="tab"]')
    expect(disabledTab?.classList.contains('active')).toBe(false)
  })
})
