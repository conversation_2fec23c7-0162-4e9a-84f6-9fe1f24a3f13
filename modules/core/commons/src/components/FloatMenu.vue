<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

type Props = {
  position?: 'bottom' | 'top' | 'left' | 'right'
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const triggerRef = ref<HTMLElement | null>(null)
const menuRef = ref<HTMLElement | null>(null)
const menuStyles = ref<Record<string, string | number>>({
  top: '0px',
  left: '0px',
  position: 'fixed',
  zIndex: 9999
})

function closeMenu() {
  emit('update:modelValue', false)
}

function updateMenuPosition() {
  if (!triggerRef.value || !menuRef.value) return
  const triggerRect = triggerRef.value.getBoundingClientRect()
  const menuRect = menuRef.value.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let top = 0,
    left = 0
  let position = props.position || 'bottom'

  switch (position) {
    case 'top':
      top = triggerRect.top - menuRect.height
      left = triggerRect.left + (triggerRect.width - menuRect.width) / 2
      break
    case 'left':
      top = triggerRect.top + (triggerRect.height - menuRect.height) / 2
      left = triggerRect.left - menuRect.width
      break
    case 'right':
      top = triggerRect.top + (triggerRect.height - menuRect.height) / 2
      left = triggerRect.right
      break
    default:
      top = triggerRect.bottom
      left = triggerRect.left + (triggerRect.width - menuRect.width) / 2
  }

  left = Math.max(0, Math.min(left, viewportWidth - menuRect.width))
  top = Math.max(0, Math.min(top, viewportHeight - menuRect.height))

  menuStyles.value = {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    zIndex: 9999
  }
}

function onClickOutside(e: MouseEvent) {
  if (
    menuRef.value &&
    !menuRef.value.contains(e.target as Node) &&
    triggerRef.value &&
    !triggerRef.value.contains(e.target as Node)
  ) {
    closeMenu()
  }
}

onMounted(() => {
  document.addEventListener('mousedown', onClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('mousedown', onClickOutside)
})

watch(
  () => props.modelValue,
  async (val) => {
    if (val) {
      await nextTick()
      updateMenuPosition()
    }
  }
)
</script>

<template>
  <span ref="triggerRef" @click="emit('update:modelValue', !props.modelValue)">
    <slot name="trigger" />
  </span>

  <Teleport to="body">
    <div v-if="props.modelValue" ref="menuRef" :style="menuStyles" class="float-menu">
      <slot />
    </div>
  </Teleport>
</template>

<style lang="scss" scoped>
.float-menu {
  display: flex;
  flex-direction: column;

  gap: var(--gl-spacing-01);
  min-width: 120px;
  padding: var(--gl-spacing-02) 0;

  border: 1px solid var(--sys-color-stroke-active);
  border-radius: var(--gl-spacing-02);

  background: var(--sys-color-surface-container-active);
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.08);
}
</style>
