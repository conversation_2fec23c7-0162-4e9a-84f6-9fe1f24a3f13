import type { CSSProperties } from 'vue'
import type { WSnackbarComponentProps, WTagComponentProps } from '@alice-health/wonderland'

export { WSnackbarComponentProps }

interface Snackbar {
  add: (props: WSnackbarComponentProps) => void
}

export interface SnackbarComponentProps {
  value: {
    $el: Snackbar
  }
}

export interface Pagination {
  pageSize: number
  totalPages: number
}

export interface PaginatedResponse<T> {
  pagination: Pagination
  results: T
}

export interface ValueItem {
  id: string
  value: string
}

export type FriendlyEnum<T = string> = {
  value: T
  friendlyName: string
  color?: WTagComponentProps['color']
}

export type ViewLayoutStyles = {
  navigation?: CSSProperties
  content?: CSSProperties
  footer?: CSSProperties
}
