import { useSnackbar } from '../useSnackbar'
import { inject, ref } from 'vue'

vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')

  return {
    ...actual,
    inject: vi.fn()
  }
})

vi.mock('@alice-health/wonderland-vue', () => ({
  WSnackbar: vi.fn()
}))

describe('useSnackbar', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should call snackbar.add with provided props when notify is called', () => {
    const mockAdd = vi.fn()
    const snackbarRef = ref({
      $el: {
        add: mockAdd
      }
    })

    vi.mocked(inject).mockReturnValue(snackbarRef)

    const { notify } = useSnackbar()
    const props = { message: 'Test message', icon: 'testIcon' }

    notify(props)

    expect(mockAdd).toHaveBeenCalledWith(props)
  })

  it('should not throw when snackbar is not provided', () => {
    vi.mocked(inject).mockReturnValue(undefined)

    const { notify } = useSnackbar()
    const props = { message: 'Test message', icon: 'testIcon' }

    expect(() => notify(props)).not.toThrow()
  })

  it('should not throw when snackbar value is undefined', () => {
    const snackbarRef = ref(undefined)

    vi.mocked(inject).mockReturnValue(snackbarRef)

    const { notify } = useSnackbar()
    const props = { message: 'Test message', icon: 'testIcon' }

    expect(() => notify(props)).not.toThrow()
  })

  it('should not throw when snackbar $el is undefined', () => {
    const snackbarRef = ref({
      $el: undefined
    })

    vi.mocked(inject).mockReturnValue(snackbarRef)

    const { notify } = useSnackbar()
    const props = { message: 'Test message', icon: 'testIcon' }

    expect(() => notify(props)).not.toThrow()
  })

  it('should handle different types of snackbar messages', () => {
    const mockAdd = vi.fn()
    const snackbarRef = ref({
      $el: {
        add: mockAdd
      }
    })

    vi.mocked(inject).mockReturnValue(snackbarRef)

    const { notify } = useSnackbar()

    const testCases = [
      { message: 'Success', icon: 'successIcon' },
      { message: 'Error', icon: 'errorIcon' },
      { message: 'Warning', icon: 'warningIcon' },
      { message: 'Info', icon: 'infoIcon' }
    ]

    testCases.forEach((props) => {
      notify(props)
      expect(mockAdd).toHaveBeenCalledWith(props)
    })

    expect(mockAdd).toHaveBeenCalledTimes(testCases.length)
  })
})
