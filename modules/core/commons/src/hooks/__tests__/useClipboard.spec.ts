import { useClipboard } from '../useClipboard'

describe('useClipboard', () => {
  let clipboardText = ''

  Object.defineProperty(window, 'navigator', {
    value: {
      clipboard: {
        writeText: (text: string) => {
          clipboardText = text
          return Promise.resolve()
        },
        readText: () => Promise.resolve(clipboardText)
      }
    }
  })

  it('should be a function', () => {
    expect(typeof useClipboard).toBe('function')
  })

  it('should return a copy function', () => {
    const { copy } = useClipboard()

    expect(typeof copy).toBe('function')
  })

  it('copy function should copy text to clipboard', () => {
    const spy = vi.spyOn(window.navigator.clipboard, 'writeText')

    const { copy } = useClipboard()

    copy({ text: 'test' })

    expect(spy).toHaveBeenCalledWith('test')
  })

  it('read function should read text from clipboard', async () => {
    const spy = vi.spyOn(window.navigator.clipboard, 'readText')

    const { read, copy } = useClipboard()

    await copy({ text: 'test' })

    const text = await read()

    expect(spy).toHaveBeenCalled()
    expect(text).toBe('test')
  })
})
