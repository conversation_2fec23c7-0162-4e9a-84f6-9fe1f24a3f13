export function useBrowserDownload() {
  function downloadWithLink(data: string, filename: string) {
    const a = document.createElement('a')

    a.href = data
    a.download = filename

    a.click()

    URL.revokeObjectURL(a.href)
    document.body.removeChild(a)
  }

  function downloadWithBlob(data: string, filename: string, type: string) {
    const blob = new Blob([data], { type })

    const url = URL.createObjectURL(blob)

    downloadWithLink(url, filename)
  }

  return { downloadWithLink, downloadWithBlob }
}
