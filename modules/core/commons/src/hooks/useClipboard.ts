import { useSnackbar } from './useSnackbar'

export type ClipboardHookCopyProps = {
  text: string
  notifyOnError?: boolean
  callback?: () => void
}

export function useClipboard() {
  const { notify } = useSnackbar()

  async function copy({ text, notifyOnError = false, callback }: ClipboardHookCopyProps) {
    try {
      await navigator.clipboard.writeText(text)

      callback?.()
    } catch (error) {
      if (notifyOnError) {
        notify({ message: 'Erro ao copiar texto', icon: 'icAlertCircleOutlined' })
      }
    }
  }

  async function read(notifyOnError = false) {
    try {
      const text = await navigator.clipboard.readText()

      return text
    } catch (error) {
      if (notifyOnError) {
        notify({
          message: 'Erro ao ler conteúdo da área de colagem',
          icon: 'icAlertCircleOutlined'
        })
      }

      return null
    }
  }

  return { copy, read }
}
