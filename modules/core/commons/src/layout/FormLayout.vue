<template>
  <div class="form-layout">
    <div class="form-layout__back">
      <slot name="nav" />
    </div>
    <WHeading variant="heavy" size="small" v-if="props.title">{{ props.title }}</WHeading>
    <section class="form-layout__form">
      <slot name="form" />
    </section>

    <section class="form-layout__actions">
      <slot name="actions" />
    </section>
  </div>
</template>

<script setup lang="ts">
import { WHeading } from '@alice-health/wonderland-vue'

const props = defineProps({
  title: {
    type: String,
    required: false
  }
})
</script>

<style scoped lang="scss">
.form-layout {
  display: grid;
  padding: var(--gl-spacing-10);
  grid-gap: var(--gl-spacing-10);
  max-width: 600px;

  &__form {
    display: grid;
    grid-gap: var(--gl-spacing-06);
  }

  &__actions {
    display: flex;
    justify-content: space-between;
  }

  &__back {
    display: flex;
    justify-content: start;
  }
}
</style>
