<script lang="ts" setup>
import type { ViewLayoutStyles } from '../types'

import { useSlots, type VueElement } from 'vue'

import { WHeading, WLabel } from '@alice-health/wonderland-vue'

type Slots = {
  navigation: VueElement
  content: VueElement
  footer: VueElement
  headerAction: VueElement
}

type Props = {
  title?: string
  aboveTitle?: string
  belowTitle?: string
  styles?: ViewLayoutStyles
  maxWidth?: string
}

defineSlots<Slots>()

withDefaults(defineProps<Props>(), {
  maxWidth: '1000px'
})

const slots = useSlots()
</script>

<template>
  <div class="view-layout" :style="{ maxWidth }">
    <div class="view-layout__navigation" :style="styles?.navigation" v-if="slots.navigation">
      <slot name="navigation" />
    </div>

    <header v-if="title" class="view-layout__header">
      <WLabel v-if="aboveTitle" variant="plain">{{ aboveTitle }}</WLabel>

      <div class="view-layout__header-title">
        <WHeading variant="plain" size="small">{{ title }}</WHeading>

        <div class="view-layout__header-title__action">
          <slot name="headerAction" />
        </div>
      </div>

      <WLabel v-if="belowTitle" variant="plain">{{ belowTitle }}</WLabel>
    </header>

    <main class="view-layout__content" :style="styles?.content" v-if="slots.content">
      <slot name="content" />
    </main>

    <footer class="view-layout__footer" :style="styles?.footer" v-if="slots.footer">
      <slot name="footer" />
    </footer>
  </div>
</template>

<style lang="scss" scoped>
.view-layout {
  display: flex;
  flex-direction: column;

  gap: var(--gl-spacing-10);
  padding: var(--gl-spacing-10);

  width: 100%;
  height: 100%;
  min-height: 100dvh;

  &__navigation {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  &__header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;

    width: 100%;

    &-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      align-content: center;

      width: 100%;

      &__action {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        align-content: center;
      }
    }
  }

  &__content {
    display: flex;
    flex-direction: column;

    width: 100%;
    height: 100%;
    overflow-y: auto;
  }

  &__footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    width: 100%;
  }
}
</style>
