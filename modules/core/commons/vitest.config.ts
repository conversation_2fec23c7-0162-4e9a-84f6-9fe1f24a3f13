import vue from '@vitejs/plugin-vue'
import { fileURLToPath } from 'url'
import { searchForWorkspaceRoot } from 'vite'

export default {
  plugins: [vue()],
  server: {
    fs: {
      allow: [searchForWorkspaceRoot(process.cwd()), '../../../vitest.setup.ts']
    }
  },
  test: {
    globals: true,
    environment: 'jsdom',
    cacheDir: '../../../node_modules/.vitest',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    setupFiles: ['../../../vitest.setup.ts'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: '../../../coverage/modules/core/commons',
      provider: 'v8',
      reporter: ['lcov', 'html']
    }
  },
  resolve: {
    alias: {
      '@commons': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
}
