import axios from 'axios'
import { getToken, refreshToken } from '@alice-backoffice/auth'

const timeout = 10000
const headers = {
  Accept: 'application/json',
  'Content-Type': 'application/json'
}
const baseURL = import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL
const isNotTestingEnv = import.meta.env.NODE_ENV !== 'test'
const API = axios.create({ baseURL, headers, timeout })

if (isNotTestingEnv) {
  API.interceptors.request.use(
    async (config) => {
      await refreshToken()

      const normalizedToken = getToken()?.replace(/"/g, '') || ''

      config.headers.Authorization = `Bearer ${normalizedToken}`

      return config
    },
    (error) => Promise.reject(error)
  )
}

export default API
