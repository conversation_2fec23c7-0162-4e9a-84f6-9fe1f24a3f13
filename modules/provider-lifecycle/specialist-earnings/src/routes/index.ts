import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const specialistEarningsRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/specialist-earnings',
      name: 'specialist-earnings',
      beforeEnter,
      component: async () => await import('@specialist-earnings/views/HomeView.vue'),
      children: [
        {
          path: 'invoices',
          name: 'specialist-earnings-invoices',
          component: async () => await import('@specialist-earnings/views/ListView.vue')
        },
        {
          path: 'history',
          name: 'specialist-earnings-history',
          component: async () => await import('@specialist-earnings/views/HistoryView.vue')
        }
      ]
    }
  ]
}

export default specialistEarningsRoutes
