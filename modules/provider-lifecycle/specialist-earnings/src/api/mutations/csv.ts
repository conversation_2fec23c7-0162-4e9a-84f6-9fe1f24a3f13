import { axiosTrackProgress } from '@commons/helpers/axios'

import http from '@http/index'

type UploadSpecialistEarningsParams = {
  fileContent: File
  onProgress: (percent: number) => void
}

export async function downloadSpecialistEarningsCSV(ids?: string[]) {
  if (!ids) {
    throw new Error('Não foram fornecidos IDs para download')
  }

  const { data } = await http.post('/resourceBundleSpecialtyPricing/csv', {
    resourceBundleSpecialtyIds: ids
  })

  return data
}

export async function uploadSpecialistEarningsCSV({
  fileContent,
  onProgress
}: UploadSpecialistEarningsParams) {
  const formData = new FormData()

  formData.append('fileContent', fileContent)

  const { data } = await http.post('/previewEarningSummaries', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: axiosTrackProgress(onProgress)
  })

  return data
}
