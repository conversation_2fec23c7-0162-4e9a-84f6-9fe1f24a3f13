import type { InvoiceResponse } from '@specialist-earnings/models/Invoice.model'

import { useQueryString } from '@alice-health/vue-hooks'

import http from '@http/index'

export interface GetSpecialistEarningsParams {
  page: number
  pageSize: number
  filter?: string
}

export async function getSpecialistEarningsFiles(params: GetSpecialistEarningsParams) {
  const { data } = await http.get('/previewEarningSummaries', { params })

  return data
}

export async function getInvoices(params: Record<string, string> = {}) {
  const { createQueryString } = useQueryString(params)

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<InvoiceResponse>(`/invoices${qs}`)

  return data
}
