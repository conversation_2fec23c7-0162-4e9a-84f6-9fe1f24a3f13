<script lang="ts" setup>
import type { Tab } from '@commons/models'
import type { StatusCardData } from '@specialist-earnings/models/Status.model'
import type { WIconComponentProps } from '@alice-health/wonderland/dist/types/models'
import type { SnackbarComponentProps } from '@commons/types'

import { computed, inject, ref, watch } from 'vue'

import { useMutation, useQueryClient } from '@tanstack/vue-query'

import { uploadSpecialistEarningsCSV } from '@specialist-earnings/api/mutations/csv'

import { RouterView } from 'vue-router'
import { ViewLayout } from '@commons/layout'
import { WButton, WModal } from '@alice-health/wonderland-vue'

import Tabs from '@commons/components/Tabs.vue'
import UploadFile from '@commons/components/UploadFile.vue'

/*
 * Constants
 */

const tabs: Tab[] = [
  {
    id: 'specialist-earnings-invoices',
    label: 'Faturas',
    route: { name: 'specialist-earnings-invoices' }
  },
  {
    id: 'specialist-earnings-history',
    label: 'Histórico de CSV',
    route: { name: 'specialist-earnings-history' }
  }
]

/*
 * Injections
 */

const snackbar = inject<SnackbarComponentProps>('snackbar')

/*
 * Hooks
 */

const queryClient = useQueryClient()

/*
 * Requests & Mutations
 */

const { mutate: uploadSpecialistEarnings, status: uploadStatus } = useMutation({
  mutationFn: (data: { fileContent: File }) =>
    uploadSpecialistEarningsCSV({ ...data, onProgress: handleUploadProgress }),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['prices'] })

    notify(
      'Estamos processando sua solicitação. Você será notificado assim que o processo for concluído.',
      'icTaskCheck'
    )
  },
  onError: () => {
    notify(
      'Não conseguimos processar sua solicitação. Verifique o arquivo enviado e tente novamente.',
      'icClose'
    )
  }
})

/*
 * Refs & Reactives
 */

const uploadProgress = ref(0)
const showUploadModal = ref(false)

const fileToUpload = ref<File | null>(null)

/*
 * Computeds
 */

const isUploading = computed(() => uploadStatus.value === 'pending')
const invalidUploadSend = computed(() => !fileToUpload.value)

const uploadModalConfirmLabel = computed(() => {
  return isUploading.value ? `Enviando (${uploadProgress.value}%)` : 'Enviar resumo'
})

/*
 * Methods
 */

function handleUploadProgress(percent: number) {
  uploadProgress.value = percent
}

function notify(message: string, icon: WIconComponentProps['icon']) {
  snackbar?.value.$el.add({ message, icon })
}

function openUploadModal() {
  showUploadModal.value = !showUploadModal.value
}

function closeUploadModal() {
  showUploadModal.value = false
}

function handleUpload() {
  if (!fileToUpload.value) {
    notify('Nenhum arquivo selecionado', 'icClose')
    return
  }

  uploadSpecialistEarnings({
    fileContent: fileToUpload.value
  })
}

function handleFileChange(files: File[] | null) {
  fileToUpload.value = files?.[0] ?? null
}

/*
 * Watchers
 */

watch(uploadProgress, (value) => {
  if (value >= 100) {
    showUploadModal.value = false
  }
})
</script>

<template>
  <ViewLayout title="Resumos de ganhos" maxWidth="1280px">
    <template #headerAction>
      <WButton variant="cta" size="large" @click="openUploadModal">Enviar resumo</WButton>
    </template>

    <template #content>
      <div class="home">
        <Tabs :tabs="tabs" data-testid="tabs" initialActive="list-pricings" />

        <RouterView />
      </div>
    </template>
  </ViewLayout>

  <WModal
    id="upload-modal"
    title="Enviar CSV do resumo de ganhos"
    :opened="showUploadModal"
    @WClosed="closeUploadModal"
  >
    <div class="home__upload-modal">
      <UploadFile
        listFiles
        accept=".csv"
        :disabled="isUploading"
        @onFileChange="handleFileChange"
      />

      <div class="home__upload-modal__actions">
        <WButton :loading="isUploading" :disabled="invalidUploadSend" @click="handleUpload">
          {{ uploadModalConfirmLabel }}
        </WButton>
      </div>
    </div>
  </WModal>
</template>

<style lang="scss" scoped>
.home {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-10);
  height: 100%;
  width: 100%;

  &__upload-modal {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-06);
    width: 624px;

    &__form {
      display: flex;
      flex-direction: column;
      gap: var(--gl-spacing-06);
      max-width: 320px;
    }

    &__actions {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      align-content: center;
      gap: var(--gl-spacing-04);
    }
  }
}
</style>
