import { describe, expect, it, beforeAll, afterAll, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

import {
  customRender,
  screen,
  waitFor,
  waitForWonderlandComponents
} from '@specialist-earnings/test'
import ListView from '../ListView.vue'

const API_BASE = '/invoices?page=1&pageSize=5&filter=%7B%22q%22%3A%22%22%7D'

const server = setupServer()

server.events.on('request:start', (req) => {
  console.log('MSW request:', req.request.method, req.request.url)
})

beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

describe('ListView', () => {
  it('should render the title', async () => {
    server.use(
      http.get(/\/invoices$/, () =>
        HttpResponse.json({ results: [], pagination: { totalPages: 1 } })
      )
    )
    customRender(ListView)
    await waitForWonderlandComponents(expect)
    expect(screen.getByText('Consulte os resumos de ganhos dos especialistas')).toBeInTheDocument()
  })

  it('should show skeleton while loading', async () => {
    server.use(http.get(/\/invoices$/, () => new Promise(() => {})))
    customRender(ListView)
    expect(await screen.findByTestId('list-skeleton')).toBeInTheDocument()
    await waitForWonderlandComponents(expect)
  })

  it('should show empty message when there are no invoices', async () => {
    server.use(
      http.get(/\/invoices$/, (...args) => {
        // Para depuração, garantir que o mock está sendo chamado
        // eslint-disable-next-line no-console
        console.log('MSW /invoices handler called', args[0].request.url)
        return HttpResponse.json({ results: [], pagination: { totalPages: 1 } })
      })
    )
    customRender(ListView)
    await waitForWonderlandComponents(expect)
    expect(
      await screen.findByText((content) => content.includes('Não registros há para mostrar'))
    ).toBeInTheDocument()
  })

  it('should show invoice data in the table', async () => {
    const mockData = [
      {
        id: 'inv-1',
        staff: { name: 'Dr. Alice' },
        expenseType: { friendlyName: 'Consulta' },
        createdAt: '2024-06-01',
        code: '12345678',
        status: {
          friendlyName: 'Pendente',
          value: 'pending',
          color: 'yellow'
        },
        guiaQuantity: 3,
        earningsAmount: 1500.5
      },
      {
        id: 'inv-2',
        staff: { name: 'Dr. Bob' },
        expenseType: { friendlyName: 'Exame' },
        createdAt: '2024-06-02',
        code: '87654321',
        status: {
          friendlyName: 'Pago',
          value: 'paid',
          color: 'green'
        },
        guiaQuantity: 1,
        earningsAmount: 500
      }
    ]
    server.use(
      http.get(/\/invoices$/, (...args) => {
        // eslint-disable-next-line no-console
        console.log('MSW /invoices handler called', args[0].request.url)
        return HttpResponse.json({ results: mockData, pagination: { totalPages: 1 } })
      })
    )
    customRender(ListView)

    // Busca normal pelos dados da tabela usando Testing Library
    expect(await screen.findByText('Dr. Alice')).toBeInTheDocument()

    expect(screen.getByText('Consulta')).toBeInTheDocument()
    expect(screen.getByText('2024-06-01')).toBeInTheDocument()
    expect(screen.getByText('1234-5678')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
    expect(screen.getByText('R$ 1.500,50')).toBeInTheDocument()
    expect(screen.getByText('Dr. Bob')).toBeInTheDocument()
    expect(screen.getByText('Exame')).toBeInTheDocument()
    expect(screen.getByText('2024-06-02')).toBeInTheDocument()
    expect(screen.getByText('8765-4321')).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('R$ 500,00')).toBeInTheDocument()
  })
})
