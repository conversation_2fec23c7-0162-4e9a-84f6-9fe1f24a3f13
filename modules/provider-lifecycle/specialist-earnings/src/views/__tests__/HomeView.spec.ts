import { describe, expect, it } from 'vitest'

import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

import {
  customRender,
  screen,
  waitFor,
  waitForWonderlandComponents
} from '@specialist-earnings/test'

import HomeView from '../HomeView.vue'

const API_BASE = '/resource_specialty_pricing/pending'

const server = setupServer()

beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

describe('HomeView', () => {
  it('should render title and tabs', async () => {
    customRender(HomeView)
    await waitForWonderlandComponents(expect)
    expect(screen.getByText('Resumos de ganhos')).toBeInTheDocument()
    expect(screen.getByText('Faturas')).toBeInTheDocument()
    expect(screen.getByText('Histórico de CSV')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Enviar resumo/i })).toBeInTheDocument()
  })

  it('should open upload modal when clicking upload button', async () => {
    customRender(HomeView)
    await waitForWonderlandComponents(expect)
    await screen.findByRole('button', { name: /Enviar resumo/i })
    await screen.getByRole('button', { name: /Enviar resumo/i }).click()
    expect(screen.getByText('Enviar CSV do resumo de ganhos')).toBeInTheDocument()
  })
})

describe('HomeView Cards', () => {
  it.skip('should show pending status card when there are pending prices', async () => {
    server.use(
      http.get(API_BASE, () => {
        return HttpResponse.json(['code1', 'code2'])
      })
    )

    customRender(HomeView)

    await waitForWonderlandComponents(expect)

    expect(screen.getByText(/2 códigos possuem pendências de precificação/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Baixar CSV de preços/i })).toBeInTheDocument()
  })

  it.skip('should show done status card when there are no pending prices', async () => {
    server.use(
      http.get(API_BASE, () => {
        return HttpResponse.json([])
      })
    )

    customRender(HomeView)

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Tudo certo por aqui!')).toBeInTheDocument()
  })

  it.skip('should show uploading status card when upload is in progress', async () => {
    server.use(
      http.get(API_BASE, () => HttpResponse.json([])),
      http.post('/resourceBundleSpecialtyPricing/upload', () =>
        HttpResponse.json({ success: true })
      )
    )

    const { user } = customRender(HomeView)

    await waitForWonderlandComponents(expect)

    await user.click(await screen.findByRole('button', { name: /Enviar CSV de preços/i }))

    const file = new File(['csv'], 'prices.csv', { type: 'text/csv' })

    const input = screen.getByLabelText(/Upload/i) || screen.getByTestId('upload-input')

    await user.upload(input, file)
    await user.click(screen.getByRole('button', { name: /Enviar/i }))

    expect(await screen.findByText('Cadastro em andamento!')).toBeInTheDocument()
  })
})
