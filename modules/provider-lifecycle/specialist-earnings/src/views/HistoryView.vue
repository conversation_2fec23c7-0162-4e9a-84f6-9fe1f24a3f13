<script lang="ts" setup>
import type { WTagComponentProps } from '@alice-health/wonderland/dist/types/models'

import { computed, ref, watch } from 'vue'

import { useQuery } from '@tanstack/vue-query'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useRoute, useRouter } from 'vue-router'

import { tableSkeleton } from '@specialist-earnings/constants/skeleton'

import {
  WButton,
  WListControllers,
  WTitle,
  WSkeleton,
  WTable,
  WTableHeader,
  WTableHeaderCell,
  WTableBody,
  WTableRow,
  WTableBodyCell,
  WTag,
  WList,
  WListItem
} from '@alice-health/wonderland-vue'

import FloatMenu from '@commons/components/FloatMenu.vue'
import {
  getSpecialistEarningsFiles,
  type GetSpecialistEarningsParams
} from '@specialist-earnings/api/queries/invoices'

/*
 * Constants
 */

const headers = [
  { id: 'fileName', label: 'Arquivo enviado' },
  { id: 'date', label: 'Data' },
  { id: 'responsible', label: 'Responsável' },
  { id: 'status', label: 'Status' },
  { id: 'details', label: 'Detalhes' },
  { id: 'actions', width: '64px' }
]

const statusMap = {
  ERROR: {
    label: 'Erro',
    color: 'red'
  },
  SUCCESS: {
    label: 'Sucesso',
    color: 'green'
  },
  PROCESSING: {
    label: 'Processando',
    color: 'yellow'
  }
}

/*
 * Hooks
 */

const { objectToJsonString } = useQueryString()

const term = ref('')
const debouncedTerm = useDebounce(term, 800)

/*
 * Hooks
 */

const route = useRoute()
const router = useRouter()

const { setNext, setPrev, currentPage, totalPages, disableNextButton, disablePrevButton } =
  usePagination({ router, route })

/*
 * Computeds
 */

const params = computed(() => ({
  filter:
    debouncedTerm.value.length > 0 ? objectToJsonString({ q: debouncedTerm.value }) : undefined,
  page: currentPage.value.toString(),
  pageSize: '5'
}))

/*
 * Requests & Mutations
 */

const {
  data: earningsFiles,
  refetch: refetchEarningsFiles,
  isLoading: isLoadingEarningsFiles
} = useQuery({
  queryKey: ['earnings-files', params.value],
  queryFn: () => getSpecialistEarningsFiles(params.value as unknown as GetSpecialistEarningsParams),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages

    return results
  }
})

/*
 * Refs
 */

const openMenus = ref<{ [id: number | string]: boolean }>({})

/*
 * Methods
 */

function searchChange(event: CustomEvent) {
  term.value = event.detail
}

function formatDate(date: string) {
  return new Date(date)
    .toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
    .replace(/\//g, '-')
}

function formatTime(date: string) {
  return new Date(date).toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/*
 * Watchers
 */

watch(currentPage, () => refetchEarningsFiles())
watch(debouncedTerm, () => refetchEarningsFiles())
</script>

<template>
  <div class="history">
    <div class="history__header">
      <WTitle level="3" variant="heavy" size="small">Consulte os arquivos enviados</WTitle>
    </div>

    <div class="history__table">
      <WListControllers
        hide-input
        has-pagination
        margin="none"
        input-placeholder="Buscar"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WInputChange="searchChange"
        @WPaginationNext="setNext"
        @WPaginationPrevious="setPrev"
      />

      <WSkeleton
        v-if="isLoadingEarningsFiles"
        :layout="tableSkeleton.layout"
        :gap="tableSkeleton.gap"
      />

      <WTable v-else>
        <WTableHeader slot="header">
          <WTableHeaderCell width="50%">Nome do arquivo</WTableHeaderCell>
          <WTableHeaderCell>Data de Criação</WTableHeaderCell>
          <WTableHeaderCell>Status</WTableHeaderCell>
          <WTableHeaderCell width="80px"></WTableHeaderCell>
        </WTableHeader>

        <WTableBody slot="body">
          <WTableRow v-if="!earningsFiles?.length">
            <WTableBodyCell :colspan="headers.length">
              <div class="history__table-body-empty">
                <WParagraph variant="plain" size="large" mode="secondary">
                  Nenhum arquivo enviado encontrado
                </WParagraph>
              </div>
            </WTableBodyCell>
          </WTableRow>

          <template v-else>
            <WTableRow v-for="item in earningsFiles" :key="item.id">
              <WTableBodyCell width="50%">{{ item.file.fileName }}</WTableBodyCell>

              <WTableBodyCell>{{ formatDate(item.createdAt) }}</WTableBodyCell>

              <WTableBodyCell>
                <WTag
                  :label="statusMap[item.status as keyof typeof statusMap]?.label"
                  :color="(statusMap[item.status as keyof typeof statusMap]?.color as WTagComponentProps['color'])"
                />
              </WTableBodyCell>

              <WTableBodyCell width="80px">
                <FloatMenu v-model="openMenus[item.id]" position="right">
                  <template #trigger>
                    <WButton iconButton icon="icMenuKebab" variant="secondary" size="small" />
                  </template>

                  <WList>
                    <WListItem>
                      <WButton variant="tertiary" icon="icDownload" size="medium">
                        Baixar arquivo
                      </WButton>
                    </WListItem>
                  </WList>
                </FloatMenu>
              </WTableBodyCell>
            </WTableRow>
          </template>
        </WTableBody>
      </WTable>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.history {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-10);

  &__header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--gl-spacing-06);
  }

  &__table {
    display: flex;
    flex-direction: column;

    gap: var(--gl-spacing-08);
    width: 100%;

    &-body {
      &-empty {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 200px;
      }
    }

    &-filters {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: var(--gl-spacing-06);

      &__buttons {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: var(--gl-spacing-06);
      }
    }
  }
}
</style>
