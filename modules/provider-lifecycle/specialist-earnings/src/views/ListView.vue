<script lang="ts" setup>
import type { WTagComponentProps } from '@alice-health/wonderland/dist/types/models'
import type { Invoice, InvoiceStatus } from '@specialist-earnings/models/Invoice.model'

import { computed, ref, watch } from 'vue'

import { useQuery } from '@tanstack/vue-query'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'

import { getInvoices } from '@specialist-earnings/api/queries/invoices'
import { tableSkeleton } from '@specialist-earnings/constants/skeleton'

import {
  WListControllers,
  WModal,
  WParagraph,
  WTitle,
  WSkeleton,
  WTable,
  WTableHeader,
  WTableHeaderCell,
  WTableBody,
  WTableRow,
  WTableBodyCell,
  WButton,
  WTag
} from '@alice-health/wonderland-vue'

/*
 * Constants
 */

/*
 * Hooks
 */

const route = useRoute()
const router = useRouter()

const { objectToJsonString } = useQueryString()

const { setNext, setPrev, totalPages, currentPage, disableNextButton, disablePrevButton } =
  usePagination({ router, route })

const term = ref('')
const debouncedTerm = useDebounce(term, 800)

/*
 * Refs
 */

const showFilters = ref(false)

/*
 * Computeds
 */

const params = computed(() => ({
  page: currentPage?.value?.toString() ?? '1',
  pageSize: '5',
  filter: debouncedTerm.value.length > 0 ? objectToJsonString({ q: debouncedTerm.value }) : ''
}))

/*
 * Requests & Mutations
 */

const {
  data: invoices,
  refetch: refetchInvoices,
  isError: isErrorInvoices,
  isLoading: isLoadingInvoices
} = useQuery({
  queryKey: ['invoices', params.value],
  queryFn: () => getInvoices(params.value),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages

    return results
  }
})

/*
 * Computeds
 */

const showTableError = computed(() => {
  return !isLoadingInvoices.value && isErrorInvoices.value
})

const showTableEmpty = computed(() => {
  return !isLoadingInvoices.value && invoices?.value?.length === 0
})

const showTableData = computed(() => {
  return (
    !isLoadingInvoices.value &&
    !isErrorInvoices.value &&
    invoices.value &&
    invoices?.value?.length > 0
  )
})

/*
 * Methods
 */

function searchChange(event: CustomEvent) {
  term.value = event.detail
}

function openFilters() {
  showFilters.value = !showFilters.value
}

function closeFilters() {
  showFilters.value = false
}

const handleOnViewClick = ({ id: invoiceId }: Invoice) => {
  // TODO: redirect to EITA with filters
}

const getFormattedCode = (code: string) => {
  return code.substring(0, 4) + '-' + code.substring(4, 8)
}

const getStatusColor = (status: InvoiceStatus): WTagComponentProps['color'] => {
  return (status?.color?.toLowerCase() as WTagComponentProps['color']) || 'gray'
}

function formatMoney(value: number) {
  return Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

/*
 * Watchers
 */

watch(debouncedTerm, () => refetchInvoices())
watch(currentPage, () => refetchInvoices())
</script>

<template>
  <div class="specialist-earnings">
    <div class="specialist-earnings__panel">
      <div class="specialist-earnings__panel-header">
        <div class="specialist-earnings__panel-header-title">
          <WTitle level="3" variant="heavy" size="small">
            Consulte os resumos de ganhos dos especialistas
          </WTitle>
        </div>
      </div>

      <WListControllers
        has-pagination
        margin="none"
        input-placeholder="Buscar"
        :hide-input="false"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WOpenFilters="openFilters"
        @WInputChange="searchChange"
        @WPaginationNext="setNext"
        @WPaginationPrevious="setPrev"
      />
    </div>

    <div class="specialist-earnings__panel-table">
      <WSkeleton
        v-if="isLoadingInvoices"
        data-testid="list-skeleton"
        :gap="tableSkeleton.gap"
        :layout="tableSkeleton.layout"
      />

      <template v-else>
        <WTable class="invoices-list">
          <WTableHeader slot="header">
            <WTableHeaderCell> Especialista </WTableHeaderCell>

            <WTableHeaderCell> Tipo de conta </WTableHeaderCell>

            <WTableHeaderCell> Referente ao mês </WTableHeaderCell>

            <WTableHeaderCell> Código da conta </WTableHeaderCell>

            <WTableHeaderCell> Status da conta </WTableHeaderCell>

            <WTableHeaderCell> Registros clínicos </WTableHeaderCell>

            <WTableHeaderCell> Valor total </WTableHeaderCell>

            <WTableHeaderCell width="80px" />
          </WTableHeader>

          <WTableBody slot="body">
            <WTableRow v-if="isLoadingInvoices">
              <WTableBodyCell colspan="7" align="center">
                <div class="table-content">
                  <WParagraph size="large">Carregando...</WParagraph>
                </div>
              </WTableBodyCell>
            </WTableRow>

            <WTableRow v-if="showTableError">
              <WTableBodyCell colspan="7" align="center">
                <div class="table-content">
                  <WParagraph size="large"> Não foi possível carregar as informações. </WParagraph>

                  <WButton size="small" icon="icRepeat" variant="tertiary" @click="refetchInvoices">
                    Tentar novamente
                  </WButton>
                </div>
              </WTableBodyCell>
            </WTableRow>

            <WTableRow v-if="showTableEmpty">
              <WTableBodyCell colspan="7" align="center">
                <div class="table-content">
                  <WParagraph size="large"> Não registros há para mostrar. </WParagraph>
                </div>
              </WTableBodyCell>
            </WTableRow>

            <template v-if="showTableData">
              <WTableRow
                class="table-row"
                v-for="invoice in invoices"
                :key="invoice?.id"
                @click="handleOnViewClick(invoice)"
              >
                <WTableBodyCell>
                  <WParagraph variant="plain">
                    {{ invoice?.staff?.name }}
                  </WParagraph>
                </WTableBodyCell>

                <WTableBodyCell>
                  <WParagraph variant="plain">
                    {{ invoice?.expenseType?.friendlyName }}
                  </WParagraph>
                </WTableBodyCell>

                <WTableBodyCell>
                  <WParagraph variant="plain">
                    {{ invoice?.createdAt }}
                  </WParagraph>
                </WTableBodyCell>

                <WTableBodyCell>
                  <WParagraph variant="plain">
                    {{ getFormattedCode(invoice?.code) }}
                  </WParagraph>
                </WTableBodyCell>

                <WTableBodyCell>
                  <WTag
                    :label="invoice?.status?.friendlyName"
                    :color="getStatusColor(invoice?.status)"
                  />
                </WTableBodyCell>

                <WTableBodyCell>
                  <WParagraph variant="plain">
                    {{ invoice?.guiaQuantity }}
                  </WParagraph>
                </WTableBodyCell>

                <WTableBodyCell>
                  <WParagraph variant="plain">
                    {{ formatMoney(invoice?.earningsAmount) }}
                  </WParagraph>
                </WTableBodyCell>

                <WTableBodyCell width="80px">
                  <WButton icon-button icon="icArrowRight" variant="secondary" size="small" />
                </WTableBodyCell>
              </WTableRow>
            </template>
          </WTableBody>
        </WTable>
      </template>
    </div>

    <WModal
      id="filters-modal"
      title="Filtrar por"
      confirmLabel="Filtrar"
      cancelLabel="Fechar"
      :opened="showFilters"
      @WClosed="closeFilters"
    >
      <div class="specialist-earnings__filters"></div>
    </WModal>
  </div>
</template>

<style lang="scss" scoped>
.specialist-earnings {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: var(--gl-spacing-10);

  &__panel {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: var(--gl-spacing-06);

    width: 100%;

    &-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      align-content: center;

      width: 100%;

      &-title {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
      }
    }

    &-table {
      display: flex;
      flex-direction: column;

      gap: var(--gl-spacing-06);
      width: 100%;

      &__empty {
        display: flex;
        justify-content: center;
        align-items: center;

        width: 100%;
        height: 100%;
        min-height: 200px;
      }
    }
  }
}

.table-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-content: center;
  padding: 48px;
  gap: 24px;
}
</style>
