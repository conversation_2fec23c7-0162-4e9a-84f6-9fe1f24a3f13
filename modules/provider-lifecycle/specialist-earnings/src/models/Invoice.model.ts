import type { PaginatedResponse } from '@commons/types'

export interface InvoiceStatus {
  friendlyName: string
  value: string
  color?: string
}

export interface InvoiceExpenseType {
  friendlyName: string
  value: string
}

export interface InvoiceProviderUnit {
  id: string
  name: string
}

export interface InvoiceStaff {
  id: string
  name: string
}

export interface Invoice {
  id: string
  code: string
  status: InvoiceStatus
  expenseType: InvoiceExpenseType
  providerUnit: InvoiceProviderUnit
  staff: InvoiceStaff
  guiaQuantity: number
  earningsAmount: number
  createdAt: string
}

export type InvoiceResponse = PaginatedResponse<Invoice[]>
