<script lang="ts" setup>
import type { Pricing } from '@code-pricing/models/Pricing.model'
import type {
  WSkeletonComponentProps,
  WSkeletonLayout
} from '@alice-health/wonderland/dist/types/models'

import { computed, onMounted, ref, watch } from 'vue'

import { useBrowserDownload } from '@commons/hooks'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'

import { getPrices } from '@code-pricing/api/queries/prices'
import { downloadPricesCSV } from '@code-pricing/api/mutations/csv'
import {
  getAllMedicalSpecialties,
  type MedicalSpecialtyShortResponse
} from '@code-pricing/api/queries/getMedicalSpecialties'

import {
  WButton,
  WListControllers,
  WModal,
  WParagraph,
  WTitle,
  WSkeleton,
  WSegmentedButtonGroup,
  WSegmentedButton,
  WT<PERSON>tfield,
  WCheckbox
} from '@alice-health/wonderland-vue'

import PriceDetails from '@code-pricing/components/PriceDetails.vue'

/*
 * Constants
 */

const priceSkeletonItem: WSkeletonLayout = [
  {
    width: '100%',
    height: 120,
    borderRadius: 16
  }
]

const pricesSkeleton: WSkeletonComponentProps = {
  gap: { row: 24, column: 24 },
  layout: [priceSkeletonItem, priceSkeletonItem]
}

/*
 * Hooks
 */

const route = useRoute()
const router = useRouter()

const { downloadWithBlob } = useBrowserDownload()
const { objectToJsonString } = useQueryString()

const { setNext, setPrev, totalPages, currentPage, disableNextButton, disablePrevButton } =
  usePagination({ router, route })

const term = ref('')
const debouncedTerm = useDebounce(term, 800)

enum Status {
  NOT_PRICED = 'NOT_PRICED',
  PRICED = 'PRICED'
}

enum ServiceType {
  PROCEDURE = 'PROCEDURE',
  EXAM = 'EXAM',
  CONSULTATION = 'CONSULTATION'
}

const selectedStatusFilter = ref<Status | null>(null)
const selectedStatusFilterNotYetApplied = ref<Status | null>(null)
const selectedServiceTypesFilter = ref<ServiceType[]>([])
const selectedServiceTypesFilterNotYetApplied = ref<ServiceType[]>([])
/*
 * Refs
 */

const selectedPrices = ref<Record<string, string[]>>({})
const allPricesSelected = ref(false)

const showFilters = ref(false)
const medicalSpecialties = ref<MedicalSpecialtyShortResponse[]>([])
const filteredMedicalSpecialties = ref<MedicalSpecialtyShortResponse[]>([])
const selectedMedicalSpecialtiesNotYetApplied = ref<MedicalSpecialtyShortResponse[]>([])
const selectedMedicalSpecialties = ref<MedicalSpecialtyShortResponse[]>([])
const selectedFiltersCount = ref(0)

/*
 * Computeds
 */

const params = computed(() => ({
  filter: objectToJsonString({
    q: debouncedTerm.value,
    medicalSpecialtyIds: selectedMedicalSpecialties.value.length
      ? selectedMedicalSpecialties.value.map(
          (specialty: MedicalSpecialtyShortResponse) => specialty.id
        )
      : undefined,
    pricingStatus: selectedStatusFilter.value,
    serviceTypes: selectedServiceTypesFilter.value
  }),
  page: currentPage.value.toString(),
  pageSize: '5'
}))

const todayDateWithHour = computed(() => {
  const today = new Date().toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  return today.replace(/\//g, '-').replace(',', '--')
})

/*
 * Requests & Mutations
 */

const {
  data: prices,
  refetch: refetchPrices,
  isLoading: isLoadingPrices
} = useQuery({
  queryKey: ['prices', params.value],
  queryFn: () => getPrices(params.value),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages

    return results
  }
})

const { mutate: downloadSelectedPricesCSV } = useMutation({
  mutationFn: (ids: string[]) => downloadPricesCSV(ids),
  onSuccess: (data) =>
    downloadWithBlob(data, `prices-selected-${todayDateWithHour.value}`, 'text/csv'),
  onError: (error) => {
    console.error('Error downloading CSV', error)
  }
})

const { data: medicalSpecialtiesResponse } = useQuery({
  queryKey: ['medicalSpecialties'],
  queryFn: () => getAllMedicalSpecialties()
})

/*
 * Computeds
 */

const downloadCSVButtonLabel = computed(() => {
  const isAllPricesSelected = checkIfAllPricesAreSelected(selectedPrices.value)
  const noPricesSelected = !getSelectedPricesSize(selectedPrices.value)

  return isAllPricesSelected || noPricesSelected
    ? 'Baixar CSV (Todos os códigos)'
    : `Baixar CSV (${getSelectedPricesSize(selectedPrices.value)} selecionados)`
})

/*
 * Methods
 */

function searchChange(event: CustomEvent) {
  router.push({ query: { page: '1' } })
  term.value = event.detail
}

function openFilters() {
  selectedStatusFilterNotYetApplied.value = selectedStatusFilter.value
  selectedMedicalSpecialtiesNotYetApplied.value = selectedMedicalSpecialties.value
  selectedServiceTypesFilterNotYetApplied.value = selectedServiceTypesFilter.value
  showFilters.value = !showFilters.value
}

function closeFilters(e: CustomEvent) {
  if (e.detail === 'confirm') {
    applyFilters()
  } else if (e.detail === 'cancel') {
    clearFilters()
  }

  showFilters.value = false
}

function applyFilters() {
  selectedStatusFilter.value = selectedStatusFilterNotYetApplied.value
  selectedMedicalSpecialties.value = selectedMedicalSpecialtiesNotYetApplied.value
  selectedServiceTypesFilter.value = selectedServiceTypesFilterNotYetApplied.value
  selectedFiltersCount.value = 0
  if (selectedStatusFilter.value) {
    selectedFiltersCount.value = selectedFiltersCount.value + 1
  }
  selectedFiltersCount.value = selectedFiltersCount.value + selectedMedicalSpecialties.value.length
  selectedFiltersCount.value = selectedFiltersCount.value + selectedServiceTypesFilter.value.length
  router.push({ query: { page: '1' } })
  refetchPrices()
}

function clearFilters() {
  selectedStatusFilter.value = null
  selectedMedicalSpecialties.value = []
  selectedServiceTypesFilter.value = []
  selectedFiltersCount.value = 0
  router.push({ query: { page: '1' } })
  refetchPrices()
}

function filterMedicalSpecialties(event: CustomEvent) {
  const value = event.detail
  filteredMedicalSpecialties.value = medicalSpecialties.value.filter(
    (specialty: MedicalSpecialtyShortResponse) =>
      specialty.name.toLowerCase().includes(value.toLowerCase())
  )
}

function handleSelectionStatusFilter(e: CustomEvent) {
  selectedStatusFilterNotYetApplied.value = e.detail
}

function handleSelectionServiceTypesFilter(e: CustomEvent) {
  selectedServiceTypesFilterNotYetApplied.value = e.detail
}

function handleSelectMedicalSpecialty(specialty: MedicalSpecialtyShortResponse) {
  const index = selectedMedicalSpecialtiesNotYetApplied.value.findIndex(
    (s: MedicalSpecialtyShortResponse) => s.id === specialty.id
  )
  if (index === -1) {
    selectedMedicalSpecialtiesNotYetApplied.value.push(specialty)
  } else {
    selectedMedicalSpecialtiesNotYetApplied.value.splice(index, 1)
  }
}

function handleSelectPrices(selected: string[], groupId: string) {
  if (selected.length) {
    selectedPrices.value[groupId] = selected
  } else {
    delete selectedPrices.value[groupId]
  }

  allPricesSelected.value = checkIfAllPricesAreSelected(selectedPrices.value)
}

function handleDownloadSelectedPricesCSV() {
  const ids = Object.values(selectedPrices.value).flat()

  downloadSelectedPricesCSV(ids)
}

function getSelectedPricesSize(prices: Record<string, string[]>) {
  const items = Object.values(prices).flat()

  return items.length
}

function checkIfAllPricesAreSelected(selectedPrices: Record<string, string[]>) {
  const totalPrices = totalPages.value * 5

  return getSelectedPricesSize(selectedPrices) === totalPrices
}

/*
 * Watchers
 */

watch(debouncedTerm, () => refetchPrices())
watch(currentPage, () => refetchPrices())
watch(medicalSpecialtiesResponse, (data) => {
  if (data) {
    medicalSpecialties.value = data.medicalSpecialties
    filteredMedicalSpecialties.value = medicalSpecialties.value
  }
})

onMounted(() => {
  refetchPrices()
})
</script>

<template>
  <div class="list-pricings">
    <div class="list-pricings__panel">
      <div class="list-pricings__panel-header">
        <div class="list-pricings__panel-header-title">
          <WTitle level="3" variant="heavy" size="small">
            Consulte os códigos cadastrados e seus status
          </WTitle>

          <WParagraph variant="plain" size="large" mode="secondary">
            Para atualizar preços, selecione os itens, baixe o CSV, preencha os dados e envie o
            arquivo atualizado.
          </WParagraph>
        </div>

        <div class="list-pricings__panel-header-actions">
          <WButton
            variant="cta"
            size="large"
            :disabled="!prices?.length"
            @click="handleDownloadSelectedPricesCSV"
          >
            {{ downloadCSVButtonLabel }}
          </WButton>
        </div>
      </div>

      <WListControllers
        has-pagination
        margin="none"
        input-placeholder="Buscar pelo código ou descrição"
        :hide-input="false"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WOpenFilters="openFilters"
        @WInputChange="searchChange"
        @WPaginationNext="setNext"
        @WPaginationPrevious="setPrev"
        :has-filters="true"
        :selected-filters-count="selectedFiltersCount"
      />
    </div>

    <div class="list-pricings__panel-pricings">
      <WSkeleton
        v-if="isLoadingPrices"
        data-testid="list-skeleton"
        :gap="pricesSkeleton.gap"
        :layout="pricesSkeleton.layout"
      />

      <template v-if="!!prices?.length">
        <PriceDetails
          v-for="price in prices"
          :key="price.healthSpecialistResourceBundleId"
          :data="price"
          @onSelectPrices="(x) => handleSelectPrices(x, price.healthSpecialistResourceBundleId)"
        />
      </template>

      <div v-if="!isLoadingPrices && !prices?.length" class="list-pricings__panel-pricings__empty">
        <WParagraph size="large" mode="secondary">Nenhum código encontrado.</WParagraph>
      </div>
    </div>

    <WModal
      id="filters-modal"
      title="Filtrar"
      confirmLabel="Aplicar filtros"
      cancelLabel="Limpar seleção"
      :opened="showFilters"
      @WClosed="closeFilters"
    >
      <div class="list-pricings__filters">
        <div class="list-pricings__filters--section">
          <WParagraph variant="heavy" size="large">Status</WParagraph>
          <WSegmentedButtonGroup
            :value="selectedStatusFilterNotYetApplied"
            direction="horizontal"
            :onW-change="handleSelectionStatusFilter"
          >
            <WSegmentedButton
              :selected="selectedStatusFilterNotYetApplied === Status.NOT_PRICED"
              :flex-grow="true"
              :value="Status.NOT_PRICED"
            >
              Possui pendências
            </WSegmentedButton>
            <WSegmentedButton
              :selected="selectedStatusFilterNotYetApplied === Status.PRICED"
              :flex-grow="true"
              :value="Status.PRICED"
            >
              Sem pendências
            </WSegmentedButton>
          </WSegmentedButtonGroup>
        </div>
        <div class="list-pricings__filters--section">
          <WParagraph variant="heavy" size="large">Tipo de atendimento</WParagraph>
          <WSegmentedButtonGroup
            :value="selectedServiceTypesFilterNotYetApplied"
            direction="horizontal"
            :onW-change="handleSelectionServiceTypesFilter"
            :multiple="true"
          >
            <WSegmentedButton
              :selected="selectedServiceTypesFilterNotYetApplied.includes(ServiceType.CONSULTATION)"
              :flex-grow="true"
              :value="ServiceType.CONSULTATION"
            >
              Consulta
            </WSegmentedButton>
            <WSegmentedButton
              :selected="selectedServiceTypesFilterNotYetApplied.includes(ServiceType.EXAM)"
              :flex-grow="true"
              :value="ServiceType.EXAM"
            >
              Exame
            </WSegmentedButton>
            <WSegmentedButton
              :selected="selectedServiceTypesFilterNotYetApplied.includes(ServiceType.PROCEDURE)"
              :flex-grow="true"
              :value="ServiceType.PROCEDURE"
            >
              Procedimento
            </WSegmentedButton>
          </WSegmentedButtonGroup>
        </div>
        <div class="list-pricings__filters--section">
          <WParagraph variant="heavy" size="large">Especialidade</WParagraph>
          <WTextfield
            placeholder="Digite o nome da especialidade"
            @WChange="filterMedicalSpecialties"
            leading-icon="icSearch"
          >
          </WTextfield>
          <WCheckbox
            v-for="specialty in filteredMedicalSpecialties"
            :key="specialty.id"
            :label="specialty.name"
            :checked="selectedMedicalSpecialtiesNotYetApplied.includes(specialty)"
            @WChange="handleSelectMedicalSpecialty(specialty)"
          />
        </div>
      </div>
    </WModal>
  </div>
</template>

<style lang="scss" scoped>
.list-pricings {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: var(--gl-spacing-10);

  &__filters {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-10);
    width: 540px;

    &--section {
      display: flex;
      flex-direction: column;
      gap: var(--gl-spacing-04);
    }
  }

  &__panel {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: var(--gl-spacing-06);

    width: 100%;

    &-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      align-content: center;

      width: 100%;

      &-title {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
      }
    }

    &-pricings {
      display: flex;
      flex-direction: column;

      gap: var(--gl-spacing-06);
      width: 100%;

      &__empty {
        display: flex;
        justify-content: center;
        align-items: center;

        width: 100%;
        height: 100%;
        min-height: 200px;
      }
    }
  }
}
</style>
