import { describe, expect, it, beforeAll, afterAll, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

import { customRender, screen, waitFor, waitForWonderlandComponents } from '@code-pricing/test'
import ListView from '../ListView.vue'

const API_BASE = '/healthSpecialistResourceBundle/pricing'

const server = setupServer()

// Loga todas as requisições para depuração
server.events.on('request:start', (req) => {
  // eslint-disable-next-line no-console
  console.log('MSW request:', req.request.method, req.request.url)
})

beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

describe('ListView', () => {
  it('should render the title and instructions', async () => {
    server.use(
      http.get(API_BASE, () => HttpResponse.json({ results: [], pagination: { totalPages: 1 } }))
    )
    customRender(ListView)
    await waitForWonderlandComponents(expect)
    expect(screen.getByText('Consulte os códigos cadastrados e seus status')).toBeInTheDocument()
    expect(
      screen.getByText(
        /Para atualizar preços, selecione os itens, baixe o CSV, preencha os dados e envie o arquivo atualizado./
      )
    ).toBeInTheDocument()
  })

  it('should show skeleton while loading', async () => {
    server.use(http.get(API_BASE, () => new Promise(() => {})))

    customRender(ListView)

    expect(await screen.findByTestId('list-skeleton')).toBeInTheDocument()

    await waitForWonderlandComponents(expect)
  })

  it('should show empty message when there are no codes', async () => {
    server.use(
      http.get('*healthSpecialistResourceBundle/pricing*', (req) => {
        return HttpResponse.json({ results: [], pagination: { totalPages: 1 } })
      })
    )

    customRender(ListView)

    await waitForWonderlandComponents(expect)

    await waitFor(async () => {
      expect(await screen.findByText('Nenhum código encontrado.')).toBeInTheDocument()
    })
  })

  it('should show PriceDetails for each returned code', async () => {
    const mockData = [
      {
        aliceCode: 'A123',
        description: 'Consulta básica',
        pendingNumber: 0,
        serviceType: 'Consulta',
        primaryTuss: 'T123',
        healthSpecialistResourceBundleId: 'id-1',
        medicalSpecialties: [
          {
            prices: [],
            beginAt: '01/01/2024',
            description: 'Clínica Geral',
            changeBeginAt: '',
            pendingNumber: 0,
            hasScheduleChange: false,
            medicalSpecialtyId: 'spc-1',
            resourceBundleSpecialtyId: 'rb-1'
          }
        ]
      },
      {
        aliceCode: 'A456',
        description: 'Exame avançado',
        pendingNumber: 2,
        serviceType: 'Exame',
        primaryTuss: 'T456',
        healthSpecialistResourceBundleId: 'id-2',
        medicalSpecialties: [
          {
            prices: [],
            beginAt: '01/02/2024',
            description: 'Cardiologia',
            changeBeginAt: '',
            pendingNumber: 1,
            hasScheduleChange: false,
            medicalSpecialtyId: 'spc-2',
            resourceBundleSpecialtyId: 'rb-2'
          }
        ]
      }
    ]
    server.use(
      http.get('*healthSpecialistResourceBundle/pricing*', (req) => {
        return HttpResponse.json({ results: mockData, pagination: { totalPages: 1 } })
      })
    )

    customRender(ListView)

    await new Promise((r) => setTimeout(r, 900))

    await waitFor(async () => {
      const result = await screen.findAllByText('Consulta básica')
      expect(result.length).toBe(2)
    })

    expect(screen.getByText('Alice code A123 | Tuss T123')).toBeInTheDocument()

    expect(screen.getByText('Alice code A456 | Tuss T456')).toBeInTheDocument()
    const otherResult = await screen.findAllByText('Exame avançado')
    expect(otherResult.length).toBe(2)
  })
})
