import { describe, expect, it, beforeAll, afterAll, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

import { customRender, screen, waitFor, waitForWonderlandComponents } from '@code-pricing/test'
import { downloadFile } from '@commons/helpers/download'

import HistoryView from '../HistoryView.vue'

const API_BASE = '/resourceBundleSpecialtyPricingUpdate'

const server = setupServer()

server.events.on('request:start', (req) => {
  console.log('MSW request:', req.request.method, req.request.url)
})

vi.mock('@commons/helpers/download', () => ({
  downloadFile: vi.fn()
}))

beforeAll(() => {
  global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')
  server.listen()
})
afterEach(() => {
  server.resetHandlers()
  vi.clearAllMocks()
})
afterAll(() => server.close())

describe('HistoryView', () => {
  const mockHistoryData = {
    pagination: { totalPages: 1 },
    results: [
      {
        id: '1',
        filename: 'test.csv',
        createdAt: '2024-03-20T10:00:00',
        createdBy: 'John Doe',
        status: {
          value: 'PROCESSED',
          friendlyName: 'Processado',
          color: 'SUCCESS'
        },
        processingDetails: {
          friendlyDescription: 'test details',
          errorItems: 0,
          totalItems: 1
        },
        downloadFileUrl: 'https://example.com/download.csv',
        errorItems: false
      }
    ]
  }

  it('should download failed lines file when clicking download error button', async () => {
    const mockBlob = new Blob(['error,line'], { type: 'text/csv' })
    const mockResponse = { data: mockBlob }

    server.use(
      http.get(new RegExp(`${API_BASE}.*`), () => HttpResponse.json(mockHistoryData)),
      http.get(new RegExp(`${API_BASE}/[\\w-]+/failedLinesFile.*`), () =>
        HttpResponse.json(mockResponse)
      )
    )

    const { user } = customRender(HistoryView)

    await waitFor(() => {
      expect(screen.getByText('test.csv')).toBeInTheDocument()
    })

    // Click the menu button first
    const menuButton = screen.getByTestId('menu-button')
    await user.click(menuButton)

    // Now click the download error button
    const downloadErrorButton = await waitFor(() => screen.getByTestId('download-error-button'))
    await user.click(downloadErrorButton)

    expect(downloadFile).toHaveBeenCalled()
  })

  it('should download file when clicking download button', async () => {
    server.use(http.get(new RegExp(`${API_BASE}.*`), () => HttpResponse.json(mockHistoryData)))

    const { user } = customRender(HistoryView)

    await waitFor(() => {
      expect(screen.getByText('test.csv')).toBeInTheDocument()
    })

    // Click the menu button first
    const menuButton = screen.getByTestId('menu-button')
    await user.click(menuButton)

    // Now click the download button
    const downloadButton = await waitFor(() =>
      screen.getByRole('button', { name: /Baixar CSV enviado/i })
    )
    await user.click(downloadButton)

    expect(downloadFile).toHaveBeenCalled()
  })
})
