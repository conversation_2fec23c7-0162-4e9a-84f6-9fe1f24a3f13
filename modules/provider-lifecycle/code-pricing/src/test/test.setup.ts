import type { Snackbar } from '@commons/types'
import type { RouteRecordRaw } from 'vue-router'

import { vi } from 'vitest'
import { VueQueryPlugin } from '@tanstack/vue-query'

import { ref, type Component } from 'vue'
import { render, type RenderOptions } from '@testing-library/vue'
import { createRouter, createWebHistory } from 'vue-router'

import userEvent from '@testing-library/user-event'
import codePricingRoutes from '@code-pricing/routes'

type CustomOptions = {
  routes?: RouteRecordRaw[]
}

export function customRender(component: Component, options?: RenderOptions & CustomOptions) {
  const user = userEvent.setup()

  const snackbar = ref<Snackbar>()

  const router = createRouter({
    history: createWebHistory(),
    routes: options?.routes ?? codePricingRoutes(vi.fn())
  })

  return {
    ...render(component, {
      ...options,
      global: {
        plugins: [router, VueQueryPlugin],
        provide: { snackbar }
      }
    }),
    user,
    router
  }
}
