import type { FriendlyEnum, PaginatedResponse } from '@commons/types'

export type Price = {
  tier: string
  price: string
  productTier: string
}

export type MedicalSpecialty = {
  prices: Price[]
  beginAt: string
  description: string
  changeBeginAt: string
  pendingNumber: number
  hasScheduleChange: boolean
  medicalSpecialtyId: string
  resourceBundleSpecialtyId: string
}

export type Pricing = {
  aliceCode: string
  description: string
  pendingNumber: number
  serviceType: string
  primaryTuss: string
  medicalSpecialties: MedicalSpecialty[]
  healthSpecialistResourceBundleId: string
  status: FriendlyEnum
}

export type PriceResponse = PaginatedResponse<Pricing[]>
