<script lang="ts" setup>
import type { Pricing } from '@code-pricing/models/Pricing.model'

import { computed, ref, watch } from 'vue'

import { formatPrice } from '@commons/helpers'

import {
  WButton,
  WCheckbox,
  WIcon,
  WParagraph,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WTag,
  WTooltip
} from '@alice-health/wonderland-vue'

import PriceDetailsHeader from '@code-pricing/components/PriceDetailsHeader.vue'

/**
 * Custom Types
 */

type Emits = {
  (e: 'onSelectPrices', value: string[]): void
}

type Props = {
  data: Pricing
}

/**
 * Vue Definitions
 */

const emits = defineEmits<Emits>()
const props = defineProps<Props>()

/*
 * Constants
 */

const columns = [
  { id: 'checkbox', width: '500px', sticky: 'left' },
  { id: 'talented_tier_3', label: 'Talented tier 3', width: '140px' },
  { id: 'talented_tier_2', label: 'tier 2', width: '140px' },
  { id: 'talented_tier_1', label: 'tier 1', width: '140px' },
  { id: 'talented_tier_0', label: 'tier 0', width: '140px' },
  { id: 'expert_tier_2', label: 'Expert tier 2', width: '140px' },
  { id: 'expert_tier_1', label: 'tier 1', width: '140px' },
  { id: 'expert_tier_0', label: 'tier 0', width: '140px' },
  { id: 'super_expert_tier_1', label: 'Super Expert tier 1', width: '160px' },
  { id: 'super_expert_tier_0', label: 'tier 0', width: '140px' },
  { id: 'ultra_expert_tier_0', label: 'Ultra Expert tier 0', width: '160px' },
  { id: 'action', width: '80px', sticky: 'right' }
]

/*
 * Refs
 */

const contentRef = ref<HTMLElement | null>(null)
const selectedPrices = ref<string[]>([])
const accordionOpenState = ref(false)

const allSpecialtyIds = computed(() =>
  props.data.medicalSpecialties.map((price) => price.resourceBundleSpecialtyId)
)

/*
 * Computeds
 */

const shouldDisableCheckbox = computed(() => {
  return props.data.medicalSpecialties.length === 0 || !props.data.medicalSpecialties.length
})

const noneSelected = computed(() => selectedPrices?.value?.length === 0)
const allIsSelected = computed(
  () => selectedPrices?.value?.length === props?.data?.medicalSpecialties?.length
)

const checkboxState = computed(() => {
  if (noneSelected.value) return 'none'
  if (allIsSelected.value) return 'all'

  return 'some'
})

const allCheckboxChecked = computed(() => checkboxState.value === 'all')
const allCheckboxIndeterminate = computed(() => checkboxState.value === 'some')

/*
 * Methods
 */

function toggleAccordion() {
  accordionOpenState.value = !accordionOpenState.value
}

function handleSelection(id: string) {
  if (selectedPrices.value.includes(id)) {
    selectedPrices.value = selectedPrices.value.filter((price) => price !== id)
  } else {
    selectedPrices.value = [...selectedPrices.value, id]
  }
}

function handleSelectAll(event: MouseEvent) {
  event.stopPropagation()

  const allIds = allSpecialtyIds.value
  const hasAllSelected = selectedPrices.value.length === allIds.length

  if (!hasAllSelected) {
    selectedPrices.value = [...allIds]
  } else {
    selectedPrices.value = []
  }
}

function getPriceByTier(row: any, tier: string, productTier: string) {
  const priceObj = row.prices.find((p: any) => p.tier === tier && p.productTier === productTier)

  return formatPrice(priceObj?.price)
}

/*
 * Watchers
 */

watch(selectedPrices, (value) => {
  emits('onSelectPrices', value)
})

watch(accordionOpenState, (isOpen) => {
  if (contentRef.value) {
    if (isOpen) {
      const contentHeight = contentRef.value.scrollHeight
      contentRef.value.style.maxHeight = `${contentHeight}px`
    } else {
      contentRef.value.style.maxHeight = '0'
    }
  }
})

watch(
  () => props.data.medicalSpecialties,
  (newSpecialties) => {
    selectedPrices.value = selectedPrices.value.filter((id) =>
      newSpecialties.some((s) => s.medicalSpecialtyId === id)
    )
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div :class="{ 'price-details': true, open: accordionOpenState }">
    <div
      class="price-details__header"
      data-testid="price-details-header"
      @click="toggleAccordion()"
    >
      <PriceDetailsHeader :data="data">
        <template #leftAction>
          <div class="price-details__header-checkbox">
            <WCheckbox
              id="select-all-prices"
              :checked="allCheckboxChecked"
              :disabled="shouldDisableCheckbox"
              :indeterminate="allCheckboxIndeterminate"
              @click="handleSelectAll"
            />
          </div>
        </template>

        <template #rightAction>
          <WButton
            iconButton
            size="large"
            variant="tertiary"
            :icon="accordionOpenState ? 'icChevronArrowUp' : 'icChevronArrowDown'"
          />
        </template>
      </PriceDetailsHeader>
    </div>

    <div
      ref="contentRef"
      :class="{
        'price-details__content': true,
        open: accordionOpenState
      }"
    >
      <div class="price-details__content-table">
        <WTable style="max-height: 380px; overflow: auto">
          <WTableHeader slot="header" style="position: sticky; top: 0; z-index: 999">
            <WTableHeaderCell
              v-for="(column, index) in columns"
              :key="column?.id"
              :width="column?.width"
              :left-sticky="column?.sticky === 'left'"
              :right-sticky="column?.sticky === 'right'"
            >
              <div
                v-if="index === 0"
                class="price-details__content-table__first-content-header-cell"
              >
                <div class="price-details__content-table__first-content-header-cell-items">
                  <WParagraph size="large" mode="secondary">Especialidade</WParagraph>
                </div>

                <div class="price-details__content-table__first-content-header-cell-items">
                  <WParagraph size="large" mode="secondary">Vigência</WParagraph>
                </div>
              </div>

              <WParagraph v-else size="large" mode="secondary">
                {{ column?.label ?? '' }}
              </WParagraph>
            </WTableHeaderCell>
          </WTableHeader>

          <WTableBody slot="body">
            <WTableRow v-for="row in data.medicalSpecialties" :key="row?.resourceBundleSpecialtyId">
              <WTableBodyCell :width="columns[0]?.width" :left-sticky="true">
                <div class="price-details__content-table__first-content-body-cell">
                  <div class="price-details__content-table__first-content-body-cell-first-items">
                    <WCheckbox
                      :id="row?.resourceBundleSpecialtyId"
                      :checked="selectedPrices.includes(row?.resourceBundleSpecialtyId)"
                      @w-change="handleSelection(row?.resourceBundleSpecialtyId)"
                    />

                    <WTag
                      size="large"
                      mode="secondary"
                      :icon="row?.pendingNumber ? 'icAlertTriangle' : 'icSuccess'"
                      :color="row?.pendingNumber ? 'red' : 'green'"
                    />
                  </div>

                  <div class="price-details__content-table__first-content-body-cell-second-items">
                    {{ row?.description }}
                  </div>

                  <div class="price-details__content-table__first-content-body-cell-second-items">
                    <div
                      v-if="row?.changeBeginAt"
                      class="price-details__content-table__first-content-body-cell-second-items__update-icon"
                    >
                      <WTooltip :label="row?.changeBeginAt">
                        <WParagraph size="large" mode="secondary">
                          {{ row?.beginAt }}
                        </WParagraph>
                      </WTooltip>

                      <WIcon icon="icUpdate" />
                    </div>

                    <div v-else>
                      <WParagraph size="large" mode="secondary">
                        {{ row?.beginAt }}
                      </WParagraph>
                    </div>
                  </div>
                </div>
              </WTableBodyCell>

              <WTableBodyCell :width="columns[1]?.width">
                {{ getPriceByTier(row, 'TALENTED', 'TIER_3') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[2]?.width">
                {{ getPriceByTier(row, 'TALENTED', 'TIER_2') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[3]?.width">
                {{ getPriceByTier(row, 'TALENTED', 'TIER_1') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[4]?.width">
                {{ getPriceByTier(row, 'TALENTED', 'TIER_0') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[5]?.width">
                {{ getPriceByTier(row, 'EXPERT', 'TIER_2') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[6]?.width">
                {{ getPriceByTier(row, 'EXPERT', 'TIER_1') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[7]?.width">
                {{ getPriceByTier(row, 'EXPERT', 'TIER_0') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[8]?.width">
                {{ getPriceByTier(row, 'SUPER_EXPERT', 'TIER_1') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[9]?.width">
                {{ getPriceByTier(row, 'SUPER_EXPERT', 'TIER_0') }}
              </WTableBodyCell>

              <WTableBodyCell :width="columns[10]?.width">
                {{ getPriceByTier(row, 'ULTRA_EXPERT', 'TIER_0') }}
              </WTableBodyCell>

              <WTableBodyCell :right-sticky="true" :width="columns[11]?.width">
                <div class="price-details__content-table__action-cell">
                  <WButton iconButton icon="icHistoric" variant="tertiary" />
                </div>
              </WTableBodyCell>
            </WTableRow>
          </WTableBody>
        </WTable>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.price-details {
  display: flex;
  flex-direction: column;

  width: 100%;
  height: fit-content;
  min-height: fit-content;
  padding: var(--gl-spacing-06);

  border-radius: var(--gl-border-radius-sm, 8px);
  background: var(--sys-color-surface-container-idle);
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.open {
    transition: gap 0s;
  }

  &__header {
    &-checkbox {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }

  &__content {
    max-height: 0;
    overflow: hidden;
    transform-origin: top;
    transform: scaleY(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.open {
      max-height: 480px;
      transform: scaleY(1);
    }

    &-table {
      width: 100%;
      margin-top: var(--gl-spacing-06);

      &__first-content-header-cell {
        display: flex;
        flex-direction: row;
        align-items: center;
        align-content: center;
        justify-content: space-between;
        width: 100%;
        padding-left: var(--gl-spacing-40);

        &-items {
          width: 200px;
          min-width: 200px;
          padding: 0 var(--gl-spacing-04);
        }
      }

      &__first-content-body-cell {
        display: flex;
        flex-direction: row;
        align-items: center;
        align-content: center;
        justify-content: space-between;
        width: 100%;

        &-first-items {
          display: flex;
          flex-direction: row;
          align-items: center;
          align-content: center;
          justify-content: center;
          width: var(--gl-spacing-40);
          min-width: var(--gl-spacing-40);
          gap: var(--gl-spacing-12);
        }

        &-second-items {
          width: 200px;
          min-width: 200px;
          padding: 0 var(--gl-spacing-04);

          &__update-icon {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: var(--gl-spacing-02);
          }
        }
      }

      &__action-cell {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 100%;
      }
    }
  }
}
</style>
