<script lang="ts" setup>
import type { WIconComponentProps } from '@alice-health/wonderland/dist/types/models'
import type { Status, StatusCardData } from '@code-pricing/models/Status.model'

import { computed } from 'vue'

import { WIcon, WLabel, WParagraph } from '@alice-health/wonderland-vue'

import Card from '@code-pricing/components/Card.vue'

/*
 * Custom Types
 */

type Props = {
  data: StatusCardData
}

type StatusDetails = {
  icon: WIconComponentProps['icon']
  color: string
}

/*
 * Component Definitions
 */

const props = defineProps<Props>()

/*
 * Constants
 */

const statusMap: Record<Status, StatusDetails> = {
  upload: {
    icon: 'icUpload',
    color: 'var(--gl-color-shades-blue-00)'
  },
  uploading: {
    icon: 'icUpload',
    color: 'var(--sys-color-surface-container-disabled-light)'
  },
  error: {
    icon: 'icDownload',
    color: 'var(--gl-color-shades-red-00)'
  },
  success: {
    icon: 'icSuccess',
    color: 'var(--gl-color-shades-green-00)'
  }
}

const currentStatus = computed(() => statusMap[props?.data?.status])
</script>

<template>
  <Card hover>
    <div class="status-card">
      <div class="status-card__icon" :style="{ backgroundColor: currentStatus?.color }">
        <WIcon size="xlarge" :icon="currentStatus?.icon" />
      </div>

      <div class="status-card__content">
        <WLabel variant="heavy" size="large">{{ data?.title }}</WLabel>
        <WParagraph variant="plain">{{ data?.description }}</WParagraph>
      </div>

      <slot />
    </div>
  </Card>
</template>

<style lang="scss" scoped>
.status-card {
  display: flex;
  flex-direction: column;

  width: 538px;

  gap: var(--gl-spacing-06);

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;

    border-radius: var(--gl-border-radius-md);
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-02);
  }
}
</style>
