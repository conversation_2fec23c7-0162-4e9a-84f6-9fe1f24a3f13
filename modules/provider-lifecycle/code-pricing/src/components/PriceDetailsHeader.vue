<script lang="ts" setup>
import type { Pricing } from '@code-pricing/models/Pricing.model'
import type { WTagComponentProps } from '@alice-health/wonderland/dist/types/models'

import { computed, type VueElement } from 'vue'

import { WParagraph, WTag, WTooltip, WPopover } from '@alice-health/wonderland-vue'

/*
 * Custom Types
 */

type Props = {
  data: Pricing
}

type Slots = {
  leftAction?: () => VueElement
  rightAction?: () => VueElement
}

/*
 * Vue Definitions
 */

const props = defineProps<Props>()
const slots = defineSlots<Slots>()

/*
 * Computed
 */

const status = computed(() => {
  const hasPending = props.data.pendingNumber > 0

  const label = hasPending ? 'Possui pendências' : 'Sem pendências'
  const color = (hasPending ? 'red' : 'green') as WTagComponentProps['color']

  return { label, color }
})

const associatedSpecialtiesLabel = computed(() => {
  const spcCount = props?.data?.medicalSpecialties?.length ?? 0

  if (spcCount === 0) return 'Nenhuma associada'

  return `${spcCount} associado${spcCount > 1 ? 's' : ''}`
})
</script>

<template>
  <div class="price-details-header">
    <div class="price-details-header__left-action" v-if="slots.leftAction">
      <slot name="leftAction" />
    </div>

    <div class="price-details-header__block first-block">
      <WParagraph variant="plain" mode="secondary" size="medium">
        Alice code {{ data?.aliceCode }} | Tuss {{ data?.primaryTuss }}
      </WParagraph>

      <WPopover>
        <div slot="custom" class="price-details-header__description">
          <WParagraph size="medium">
            {{ data?.description }}
          </WParagraph>
        </div>
        <div slot="content">{{ data?.description }}</div>
      </WPopover>
    </div>

    <div class="price-details-header__divider" />

    <div class="price-details-header__block">
      <WParagraph mode="secondary">Tipo de código</WParagraph>

      <WParagraph variant="plain" mode="secondary" size="medium">
        {{ data?.serviceType }}
      </WParagraph>
    </div>

    <div class="price-details-header__block">
      <WParagraph mode="secondary">Especialidades</WParagraph>

      <WParagraph variant="plain" mode="secondary" size="medium">
        {{ associatedSpecialtiesLabel }}
      </WParagraph>
    </div>

    <div class="price-details-header__block">
      <WParagraph mode="secondary">Status</WParagraph>
      <WTag
        v-if="data?.status"
        :color="(data?.status?.color?.toLowerCase() as WTagComponentProps['color'])"
        :label="data?.status?.friendlyName"
      />
    </div>

    <div class="price-details-header__right-action" v-if="slots.rightAction">
      <slot name="rightAction" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.price-details-header {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;
  gap: var(--gl-spacing-10);
  width: 100%;
  cursor: pointer;

  &__left-action,
  &__right-action {
    flex: 0 0 auto;
  }

  &__block {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-06);
    flex: 0 1 auto;
    min-width: 120px;
    max-width: 200px;

    &.first-block {
      flex: 1 1 auto;
      min-width: 0;
      max-width: none;
    }
  }

  &__description {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-clamp: 2;
    text-overflow: ellipsis;
  }

  &__divider {
    width: 1px;
    height: 100%;
    min-height: 88px;
    border-right: 1px solid var(--sys-color-stroke-active);
    flex: 0 0 auto;
    align-self: stretch;
  }
}
</style>
