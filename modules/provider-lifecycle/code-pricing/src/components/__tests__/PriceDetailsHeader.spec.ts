import { customRender, screen, waitForWonderlandComponents } from '@code-pricing/test'

import PriceDetailsHeader from '@code-pricing/components/PriceDetailsHeader.vue'
import type { MedicalSpecialty } from '@code-pricing/models/Pricing.model'

type Props = {
  aliceCode: string
  primaryTuss: string
  description: string
  serviceType: string
  medicalSpecialties: MedicalSpecialty[]
  pendingNumber: number
  status: {
    friendlyName: string
    color: string
  }
}

describe('PriceDetailsHeader', () => {
  const data: Props = {
    aliceCode: '**********',
    primaryTuss: '**********',
    pendingNumber: 0,

    description:
      'Long description here with a lot of text to test the component with a lot of text',
    serviceType: 'Type',
    medicalSpecialties: [],
    status: {
      friendlyName: 'Sem pendências',
      color: 'GREEN'
    }
  }

  const dataWithPendingAndAssociatedSpecialties: Props = {
    ...data,
    pendingNumber: 1,
    status: {
      friendlyName: 'Possui pendências',
      color: 'RED'
    },
    medicalSpecialties: [
      {
        beginAt: '2021-01-01',
        prices: [],
        resourceBundleSpecialtyId: '1',
        changeBeginAt: '2021-01-01',
        description: 'Specialty 1 description',
        hasScheduleChange: false,
        medicalSpecialtyId: '1',
        pendingNumber: 0
      },
      {
        beginAt: '2021-01-01',
        prices: [],
        resourceBundleSpecialtyId: '2',
        changeBeginAt: '2021-01-01',
        description: 'Specialty 2 description',
        hasScheduleChange: false,
        medicalSpecialtyId: '2',
        pendingNumber: 0
      }
    ]
  }

  it('should render', async () => {
    customRender(PriceDetailsHeader, { props: { data } })

    await waitForWonderlandComponents(expect)

    expect(
      screen.getByText(`Alice code ${data.aliceCode} | Tuss ${data.primaryTuss}`)
    ).toBeInTheDocument()
    expect(screen.findAllByText(data.description)).toBeInTheDocument
    expect(screen.getByText(data.serviceType)).toBeInTheDocument()
    expect(screen.getByText('Sem pendências')).toBeInTheDocument()
    expect(screen.getByText('Nenhuma associada')).toBeInTheDocument()
  })

  it('should render with side actions', async () => {
    customRender(PriceDetailsHeader, {
      props: { data },
      slots: {
        leftAction: '<div>Side action left</div>',
        rightAction: '<div>Side action right</div>'
      }
    })

    await waitForWonderlandComponents(expect)

    expect(
      screen.getByText(`Alice code ${data.aliceCode} | Tuss ${data.primaryTuss}`)
    ).toBeInTheDocument()
    const descriptionElement = screen.findAllByText(data.description)
    expect((await descriptionElement).length).toBe(2)
    expect(screen.getByText(data.serviceType)).toBeInTheDocument()

    expect(screen.getByText('Sem pendências')).toBeInTheDocument()
    expect(screen.getByText('Nenhuma associada')).toBeInTheDocument()

    expect(screen.getByText('Side action left')).toBeInTheDocument()
    expect(screen.getByText('Side action right')).toBeInTheDocument()
  })

  it('should render with pending and associated specialties', async () => {
    customRender(PriceDetailsHeader, { props: { data: dataWithPendingAndAssociatedSpecialties } })

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Possui pendências')).toBeInTheDocument()
    expect(
      screen.getByText(
        `${dataWithPendingAndAssociatedSpecialties.medicalSpecialties.length} associados`
      )
    ).toBeInTheDocument()
  })
})
