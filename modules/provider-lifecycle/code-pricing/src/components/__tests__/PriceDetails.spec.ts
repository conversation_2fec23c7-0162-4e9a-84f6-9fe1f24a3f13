import { customRender, screen, waitForWonderlandComponents } from '@code-pricing/test'

import PriceDetails from '@code-pricing/components/PriceDetails.vue'
import type { MedicalSpecialty } from '@code-pricing/models/Pricing.model'

type Props = {
  aliceCode: string
  primaryTuss: string
  description: string
  serviceType: string
  medicalSpecialties: MedicalSpecialty[]
  pendingNumber: number
}

describe('PriceDetails', () => {
  const data: Props = {
    aliceCode: '**********',
    primaryTuss: '**********',
    description:
      'Long description here with a lot of text to test the component with a lot of text',
    serviceType: 'Type',
    medicalSpecialties: [],
    pendingNumber: 0
  }

  const dataWithMedicalSpecialties: Props = {
    ...data,
    medicalSpecialties: [
      {
        beginAt: '2021-01-01',
        prices: [
          {
            price: '250',
            tier: 'TALENTED',
            productTier: 'TIER_0'
          },
          {
            price: '210',
            tier: 'TALENTED',
            productTier: 'TIER_1'
          },
          {
            price: '180',
            tier: 'TALENTED',
            productTier: 'TIER_2'
          },
          {
            price: '100',
            tier: 'TALENTED',
            productTier: 'TIER_3'
          },
          {
            price: '280',
            tier: 'EXPERT',
            productTier: 'TIER_0'
          },
          {
            price: '290',
            tier: 'EXPERT',
            productTier: 'TIER_1'
          },
          {
            price: '300',
            tier: 'EXPERT',
            productTier: 'TIER_2'
          },
          {
            price: '400',
            tier: 'SUPER_EXPERT',
            productTier: 'TIER_0'
          },
          {
            price: '500',
            tier: 'SUPER_EXPERT',
            productTier: 'TIER_1'
          },
          {
            price: '1000',
            tier: 'ULTRA_EXPERT',
            productTier: 'TIER_0'
          }
        ],
        resourceBundleSpecialtyId: '1',
        changeBeginAt: '2021-01-01',
        description: 'Specialty 1 description',
        pendingNumber: 0,
        hasScheduleChange: false,
        medicalSpecialtyId: '1'
      },
      {
        beginAt: '2021-01-01',
        prices: [
          {
            price: '200',
            tier: 'TALENTED',
            productTier: 'TIER_0'
          },
          {
            price: '150',
            tier: 'TALENTED',
            productTier: 'TIER_2'
          },
          {
            price: '120',
            tier: 'TALENTED',
            productTier: 'TIER_3'
          }
        ],
        resourceBundleSpecialtyId: '2',
        changeBeginAt: '2021-01-01',
        description: 'Specialty 2 description',
        hasScheduleChange: false,
        medicalSpecialtyId: '2',
        pendingNumber: 0
      }
    ]
  }

  it('should render', async () => {
    customRender(PriceDetails, { props: { data } })

    await waitForWonderlandComponents(expect)

    expect(
      screen.getByText(`Alice code ${data.aliceCode} | Tuss ${data.primaryTuss}`)
    ).toBeInTheDocument()
  })

  it('should render with medical specialties', async () => {
    customRender(PriceDetails, { props: { data: dataWithMedicalSpecialties } })

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Specialty 1 description')).toBeInTheDocument()
    expect(screen.getByText('Specialty 2 description')).toBeInTheDocument()
  })

  it('should render with prices', async () => {
    customRender(PriceDetails, { props: { data: dataWithMedicalSpecialties } })

    await waitForWonderlandComponents(expect)

    expect(screen.getByText('R$ 250,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 210,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 180,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 100,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 280,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 290,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 300,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 400,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 500,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 1.000,00')).toBeInTheDocument()

    expect(screen.getByText('R$ 200,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 150,00')).toBeInTheDocument()
    expect(screen.getByText('R$ 120,00')).toBeInTheDocument()
  })
})
