import { customRender, screen } from '@code-pricing/test'

import Card from '@code-pricing/components/Card.vue'

describe('Card', () => {
  it('should render', () => {
    customRender(Card, { slots: { default: 'Card' } })

    expect(screen.getByText('Card')).toBeInTheDocument()
  })

  it('should render full width', () => {
    customRender(Card, { props: { fullWidth: true }, slots: { default: 'Card' } })

    expect(screen.getByText('Card')).toBeInTheDocument()
    expect(screen.getByText('Card')).toHaveClass('full-width')
  })

  it('should render hover', () => {
    customRender(Card, {
      props: { hover: true },
      slots: { default: 'Card' }
    })

    expect(screen.getByText('Card')).toBeInTheDocument()
    expect(screen.getByText('Card')).toHaveClass('hover')
  })
})
