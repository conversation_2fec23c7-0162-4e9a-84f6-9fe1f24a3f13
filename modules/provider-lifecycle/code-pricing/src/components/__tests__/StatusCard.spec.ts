import { customRender, screen } from '@code-pricing/test'

import StatusCard from '@code-pricing/components/StatusCard.vue'

describe('StatusCard', () => {
  it('should render', () => {
    customRender(StatusCard, {
      props: {
        data: {
          status: 'uploaded',
          title: 'Title',
          description: 'Description'
        }
      }
    })

    expect(screen.getByText('Title')).toBeInTheDocument()
    expect(screen.getByText('Description')).toBeInTheDocument()
  })

  it('should render actions', () => {
    customRender(StatusCard, {
      props: {
        data: {
          status: 'uploaded',
          title: 'Title',
          description: 'Description'
        }
      },
      slots: {
        default: `
          <button>
            Continuar
          </button>
        `
      }
    })

    expect(screen.getByText('Title')).toBeInTheDocument()
    expect(screen.getByText('Description')).toBeInTheDocument()
    expect(screen.getByText('Continuar')).toBeInTheDocument()
  })
})
