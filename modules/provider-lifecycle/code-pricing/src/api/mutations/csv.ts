import { axiosTrackProgress } from '@commons/helpers/axios'
import { convertDateToISO } from '@commons/helpers/date'

import http from '@http/index'

type UploadPricesParams = {
  fileContent: File
  effectiveDate: string
  onProgress: (percent: number) => void
}

export async function downloadPricesCSV(ids?: string[]) {
  if (!ids) {
    throw new Error('Não foram fornecidos IDs para download')
  }

  const { data } = await http.post('/resourceBundleSpecialtyPricing/csv', {
    resourceBundleSpecialtyIds: ids
  })

  return data
}

export async function uploadPricesCSV({
  effectiveDate,
  fileContent,
  onProgress
}: UploadPricesParams) {
  const formData = new FormData()

  formData.append('fileContent', fileContent)
  formData.append('effectiveDate', convertDateToISO(effectiveDate))

  const { data } = await http.post('/resourceBundleSpecialtyPricing/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: axiosTrackProgress(onProgress)
  })

  return data
}
