import type { PriceResponse } from '@code-pricing/models/Pricing.model'

import { useQueryString } from '@alice-health/vue-hooks'

import http from '@http/index'

type ProcessingStatusResponse = {
  isProcessing: boolean
}

export async function getPrices(params: Record<string, string> = {}) {
  const { createQueryString } = useQueryString(params)

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<PriceResponse>(`/healthSpecialistResourceBundle/pricing${qs}`)

  return data
}

export async function getPendingPrices() {
  const { data } = await http.get<string[]>(`/resourceBundleSpecialty/pending`)

  return data
}

export async function getProcessingStatus() {
  const { data } = await http.get<ProcessingStatusResponse>(
    `/resourceBundleSpecialtyPricing/processing`
  )

  return data
}
