import type { FriendlyEnum, PaginatedResponse } from '@commons/types'

import { useQueryString } from '@alice-health/vue-hooks'

import http from '@http/index'

export type PricingStatus = 'PRICED' | 'PENDING'

export type MedicalSpecialty = {
  id: string
  name: string
  isTherapy: boolean
  currentEndAt: string
  currentBeginAt: string
  pricingStatus: FriendlyEnum<PricingStatus>
  medicalSpecialtyId: string
  hasScheduledPriceChange: boolean
}

type MedicalSpecialtyType = 'SPECIALTY' | 'SUBSPECIALTY'

export type MedicalSpecialtyShortResponse = {
  id: string
  name: string
  type: MedicalSpecialtyType
  active: boolean
  isTherapy: boolean
  parentSpecialtyId: string | null
  isAdvancedAccess: boolean
}

type MedicalSpecialtyResourceBundle = {
  medicalSpecialties: MedicalSpecialtyShortResponse[]
  therapySpecialties: MedicalSpecialtyShortResponse[]
}

export async function getResourceBundleMedicalSpecialties(
  id: string,
  params?: Record<string, string>
) {
  const { createQueryString } = useQueryString()

  const queryString = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<PaginatedResponse<MedicalSpecialty[]>>(
    `/healthSpecialistResourceBundle/${id}/medicalSpecialties${queryString}`
  )

  return data
}

export async function getAllMedicalSpecialties() {
  const { data } = await http.get<MedicalSpecialtyResourceBundle>(
    `/medicalSpecialty/healthSpecialistResourceBundle`
  )

  return data
}
