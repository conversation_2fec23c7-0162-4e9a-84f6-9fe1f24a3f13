import type { PaginatedResponse } from '@commons/types'
import type { WTagComponentProps } from '@alice-health/wonderland/dist/types/models'

import http from '@http/index'

type History = {
  id: string
  filename: string
  createdAt: string
  createdBy: string
  totalItems: number
  errorItems: number
  processingDetails: {
    friendlyDescription: string
    errorItems: number
    totalItems: number
  }
  status: {
    color: WTagComponentProps['color']
    value: string
    friendlyName: string
  }
  downloadFileUrl: string
  downloadFailedLinesFileUrl: string
}

type HistoryResponse = PaginatedResponse<History[]>

export async function getHistory(params: Record<string, string | null> = {}) {
  const { data } = await http.get<HistoryResponse>(`/resourceBundleSpecialtyPricingUpdate`, {
    params
  })

  return data
}

export async function getFailedLinesFile(id: string) {
  const response = await http.get<string>(
    `/resourceBundleSpecialtyPricingUpdate/${id}/failedLinesFile`,
    {
      responseType: 'blob'
    }
  )

  return response
}
