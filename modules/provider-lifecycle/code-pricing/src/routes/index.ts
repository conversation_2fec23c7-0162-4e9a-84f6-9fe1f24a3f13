import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const codePricingRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/code-pricing',
      name: 'code-pricing',
      beforeEnter,
      component: async () => await import('@code-pricing/views/HomeView.vue'),
      children: [
        {
          path: 'list-pricings',
          name: 'list-pricings',
          component: async () => await import('@code-pricing/views/ListView.vue')
        },
        {
          path: 'history',
          name: 'history',
          component: async () => await import('@code-pricing/views/HistoryView.vue')
        }
      ]
    }
  ]
}

export default codePricingRoutes
