<template>
  <div v-if="!isLoading">
    <FormLayout title="Editar Time de Saúde">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'healthcare-team/list' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>
      <template #form>
        <WTextfield label="ID" :value="id" disabled />
        <WSwitch label="Ativo" v-model="form.active" @WChange="validate('active')" />
        <WSelect label="Segmento" v-model="form.segment" @WChange="validate('segment')">
          <option v-for="segment in Segments" :key="segment" :value="segment">
            {{ SegmentLabels[segment] }}
          </option>
        </WSelect>
        <WTextfield
          label="Limite de Membros Associados"
          input-mode="numeric"
          :value="memberAssociation"
          @WChange="handleMemberAssociationChange"
        />
        <WAutocomplete
          label="Médica(o)"
          aria-label="Médica(o)"
          :value="selectedPhysician"
          :items="physicianItems"
          search-by-key="name"
          @WChange="handlePhysicianChange"
          @WSelect="handlePhysicianSelect"
        />
        <AddressForm
          :address="
            form.address
              ? {
                  ...form.address,
                  complement: form.address.complement || undefined
                }
              : undefined
          "
          :referenced-model-id="id"
        />
      </template>
      <template #actions>
        <WButton
          variant="cta"
          size="large"
          @click="validateAndSave"
          :loading="saveIsPending"
          :disabled="invalid"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue'
import type { SnackbarComponentProps } from '@commons/index'
import type { Team } from '@healthcare-team-registration/models'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'
import type { Physician } from '@healthcare-team-registration/models'

import { inject, ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { useValidation } from '@alice-health/vue-hooks'
import {
  WAutocomplete,
  WButton,
  WLink,
  WSelect,
  WSwitch,
  WTextfield
} from '@alice-health/wonderland-vue'
import { FormLayout } from '@commons/index'
import AddressForm from '@healthcare-team-address/components/AddressForm.vue'
import { Segments, SegmentLabels } from '@healthcare-team-registration/constants/healthcareTeam'
import { HealthcareTeamForm } from '@healthcare-team-registration/schemas/healthcareTeamRegistrationForm'
import { getHealthcareTeam, getPhysicians } from '@healthcare-team-registration/api/queries'
import { updateHealthcareTeam } from '@healthcare-team-registration/api/mutations'

/**
 * Custom types
 */

type Form = Omit<Team, 'id' | 'createdAt' | 'updatedAt'>

/**
 * Refs
 */

const form: Ref<Form> = ref({
  active: true,
  segment: Segments.DEFAULT,
  maxMemberAssociation: 0,
  physician: null
})

/**
 * Computeds
 */

const memberAssociation = computed(() => String(form.value.maxMemberAssociation))
const id = computed(() => route.params.id as string)

const physicianItems = computed(() =>
  (physicians?.value || []).map((p: Physician) => ({
    id: p.id,
    name: p.name,
    type: p.type || ''
  }))
)

const selectedPhysician = computed(() =>
  form.value.physician
    ? {
        id: form.value.physician.id,
        name: form.value.physician.name,
        type: form.value.physician.type || ''
      }
    : undefined
)

/*
 * Injections
 */

const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const { validate, invalid, validateAll } = useValidation({
  formSchema: HealthcareTeamForm,
  form
})

const route = useRoute()
const router = useRouter()
const queryClient = useQueryClient()

const { data: physicians } = useQuery({
  queryKey: ['physicians'],
  queryFn: getPhysicians
})

const { isLoading } = useQuery({
  queryKey: ['healthcareTeam', id.value],
  queryFn: () => getHealthcareTeam(id.value),
  select: (data) => {
    form.value = data
    validateAll()
  }
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['updateHealthcareTeam'],
  mutationFn: () =>
    updateHealthcareTeam({
      id: id.value,
      active: form.value.active,
      segment: form.value.segment,
      maxMemberAssociation: form.value.maxMemberAssociation,
      physician: form.value.physician
    }),
  onSuccess,
  onError
})

/**
 * Functions
 */

function handleMemberAssociationChange(value: CustomEvent) {
  if (!value.detail) {
    form.value.maxMemberAssociation = 0
    validate('maxMemberAssociation')
    return
  }
  form.value.maxMemberAssociation = parseInt(value.detail)
  validate('maxMemberAssociation')
}

function handlePhysicianChange(event: WAutocompleteCustomEvent<string>) {
  if (!event.detail) {
    form.value.physician = null
    validate('physician')
  }
}

function handlePhysicianSelect(
  event: WAutocompleteCustomEvent<{ id: string; name: string; type: string }>
) {
  form.value.physician = event.detail
  validate('physician')
}

function validateAndSave() {
  const schema = validateAll()

  if (schema.success) save()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao atualizar o time de saúde',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['healthcareTeam'], exact: false })
  router.push({ name: 'healthcare-team/list' })
  snackbar?.value?.$el.add({
    message: 'Time de saúde atualizado com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
