import { customRender, screen } from '@healthcare-team-registration/tests'
import { server } from '@commons/services/mockServer'
import { list } from '@healthcare-team-registration/tests/mocks'

import ListView from '@healthcare-team-registration/views/ListView.vue'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(list))

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON>ria<PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()
    expect(await screen.findByLabelText('Status')).toBeInTheDocument()

    expect(await screen.findByText('Médica(o)')).toBeInTheDocument()
    expect(await screen.findByText('Segmento')).toBeInTheDocument()
    expect(await screen.findByText('Limite de Membros')).toBeInTheDocument()
    expect(screen.getAllByText('Ativo')[1]).toBeInTheDocument()

    expect(await screen.findByText('Dr. John Doe')).toBeInTheDocument()
    expect(await screen.findByText('Padrão')).toBeInTheDocument()
    expect(await screen.findByText('123')).toBeInTheDocument()
    // expect(await screen.findByText('icSuccess')).toBeInTheDocument()

    expect(await screen.findByText('Dr. Jane Doe')).toBeInTheDocument()
    expect(await screen.findByText('Pediatria')).toBeInTheDocument()
    expect(await screen.findByText('12')).toBeInTheDocument()
    // expect(await screen.findByText(/icClose/i)).toBeInTheDocument()

    expect(screen.getAllByLabelText('icEdit')[0]).toBeInTheDocument()
    expect(screen.getAllByLabelText('icEdit')[1]).toBeInTheDocument()
  })
})
