import { customRender, screen, waitFor, within } from '@healthcare-team-registration/tests'
import { server } from '@commons/services/mockServer'
import { getPhysicians, createHealthcareTeam } from '@healthcare-team-registration/tests/mocks'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'

import CreateView from '@healthcare-team-registration/views/CreateView.vue'

const mockPush = vi.fn()

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: vi.fn(() => ({
      push: mockPush
    }))
  }
})

describe('CreateView', () => {
  const mockSnackbarAdd = vi.fn()

  const renderWithMocks = () => {
    return customRender(CreateView, {
      provide: {
        snackbar: {
          value: {
            $el: {
              add: mockSnackbarAdd
            }
          }
        }
      }
    })
  }

  beforeAll(() => server.listen())
  afterEach(() => {
    server.resetHandlers()
    vi.clearAllMocks()
  })
  afterAll(() => server.close())
  beforeEach(() => {
    server.use(getPhysicians, createHealthcareTeam.successHandler)
  })

  it('renders properly', async () => {
    customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Novo Time de Saúde'
      })
    ).toBeInTheDocument()

    const saveButton = await screen.findByRole('button', {
      name: 'Salvar'
    })
    expect(saveButton).toBeDisabled()

    expect(await screen.findByRole('checkbox', { name: 'Ativo' })).toBeChecked()
    const segment = await screen.findByRole('combobox', { name: 'Segmento' })
    expect(segment).toHaveValue('DEFAULT')
    expect(segment).toHaveDisplayValue('Padrão')
    expect(
      await screen.findByRole('textbox', { name: 'Limite de Membros Associados' })
    ).toHaveValue('0')
    expect(await screen.findByRole('combobox', { name: 'Médica(o)' })).toHaveValue('')
  })

  it('validates form', async () => {
    const { user } = customRender(CreateView)

    const saveButton = await screen.findByRole('button', {
      name: 'Salvar'
    })
    expect(saveButton).toBeDisabled()

    await user.selectOptions(await screen.findByRole('combobox', { name: 'Segmento' }), [
      'PEDIATRIC'
    ])
    await user.type(
      await screen.findByRole('textbox', { name: 'Limite de Membros Associados' }),
      '123'
    )

    const autocomplete = await screen.findByRole('combobox', { name: 'Médica(o)' })
    const autocompleteInput = within(autocomplete).getByRole('textbox')
    expect(autocompleteInput).toBeInTheDocument()

    await user.type(autocompleteInput, 'teste')

    await waitFor(() => {
      expect(screen.getAllByRole('listitem')).toHaveLength(1)
    })

    await user.click(screen.getByText('Dr. John Doe'))

    await waitFor(() => {
      expect(saveButton).toBeEnabled()
    })
  })

  it.skip('handles member association validation', async () => {
    const { user } = customRender(CreateView)
    await waitForWonderlandComponents(expect)

    const memberAssociationInput = await screen.findByRole('textbox', {
      name: 'Limite de Membros Associados'
    })

    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    expect(saveButton).toBeDisabled()

    expect(memberAssociationInput).toHaveValue('0')
    expect(saveButton).toBeDisabled()

    await user.clear(memberAssociationInput)
    await user.type(memberAssociationInput, '-1')
    expect(memberAssociationInput).toHaveValue('-1')
    expect(saveButton).toBeDisabled()

    await user.clear(memberAssociationInput)
    await user.click(memberAssociationInput)
    await user.keyboard('{Backspace}')
    await user.type(memberAssociationInput, '123')
    expect(memberAssociationInput).toHaveValue('123')
    // Save button should still be disabled because other required fields are not filled
    expect(saveButton).toBeDisabled()
  })

  it('successfully creates healthcare team and redirects', async () => {
    const { user } = renderWithMocks()
    await waitForWonderlandComponents(expect)

    // Fill form
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Segmento' }), [
      'PEDIATRIC'
    ])
    await user.type(
      await screen.findByRole('textbox', { name: 'Limite de Membros Associados' }),
      '123'
    )

    const autocomplete = await screen.findByRole('combobox', { name: 'Médica(o)' })
    const autocompleteInput = within(autocomplete).getByRole('textbox')
    await user.type(autocompleteInput, 'teste')
    await waitFor(() => {
      expect(screen.getAllByRole('listitem')).toHaveLength(1)
    })
    await user.click(screen.getByText('Dr. John Doe'))

    // Submit form
    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    await user.click(saveButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith({ name: 'healthcare-team/list' })
    })

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Time de saúde cadastrado com sucesso',
        icon: 'icCheckOutlined'
      })
    })
  })

  it('handles save and create address button', async () => {
    const { user } = renderWithMocks()
    await waitForWonderlandComponents(expect)

    // Fill form
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Segmento' }), [
      'PEDIATRIC'
    ])
    await user.type(
      await screen.findByRole('textbox', { name: 'Limite de Membros Associados' }),
      '123'
    )

    const autocomplete = await screen.findByRole('combobox', { name: 'Médica(o)' })
    const autocompleteInput = within(autocomplete).getByRole('textbox')
    await user.type(autocompleteInput, 'teste')
    await waitFor(() => {
      expect(screen.getAllByRole('listitem')).toHaveLength(1)
    })
    await user.click(screen.getByText('Dr. John Doe'))

    // Submit form with save and create address
    const saveAndCreateAddressButton = await screen.findByRole('button', {
      name: 'Salvar e cadastrar endereço'
    })
    await user.click(saveAndCreateAddressButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/healthcare-team/update/mock-id-123')
    })

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Time de saúde cadastrado com sucesso',
        icon: 'icCheckOutlined'
      })
    })
  })

  it.skip('handles error on save', async () => {
    const { user } = renderWithMocks()
    await waitForWonderlandComponents(expect)

    // Fill form
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Segmento' }), [
      'PEDIATRIC'
    ])
    await user.type(
      await screen.findByRole('textbox', { name: 'Limite de Membros Associados' }),
      '123'
    )

    const autocomplete = await screen.findByRole('combobox', { name: 'Médica(o)' })
    const autocompleteInput = within(autocomplete).getByRole('textbox')
    await user.type(autocompleteInput, 'teste')
    await waitFor(() => {
      expect(screen.getAllByRole('listitem')).toHaveLength(1)
    })
    await user.click(screen.getByText('Dr. John Doe'))

    // Submit form with error
    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    server.use(createHealthcareTeam.errorHandler)
    await user.click(saveButton)

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Erro ao cadastrar o time de saúde',
        icon: 'icAlertCircleOutlined'
      })
    })
  })

  it('handles navigation back to list', async () => {
    customRender(CreateView)
    await waitForWonderlandComponents(expect)

    const backLink = await screen.findByText('Voltar')
    expect(backLink).toHaveAttribute('href', '/healthcare-team')
  })

  it('toggles active status', async () => {
    const { user } = customRender(CreateView)
    await waitForWonderlandComponents(expect)

    const activeSwitch = await screen.findByRole('checkbox', { name: 'Ativo' })
    expect(activeSwitch).toBeChecked()

    await user.click(activeSwitch)
    expect(activeSwitch).not.toBeChecked()

    await user.click(activeSwitch)
    expect(activeSwitch).toBeChecked()
  })

  it('shows validation errors when form is invalid', async () => {
    const { user } = customRender(CreateView)
    await waitForWonderlandComponents(expect)

    // Try to submit without required fields
    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    await user.click(saveButton)
    expect(saveButton).toBeDisabled()

    // Fill invalid member association
    await user.type(
      await screen.findByRole('textbox', { name: 'Limite de Membros Associados' }),
      '0'
    )
    expect(saveButton).toBeDisabled()
  })
})
