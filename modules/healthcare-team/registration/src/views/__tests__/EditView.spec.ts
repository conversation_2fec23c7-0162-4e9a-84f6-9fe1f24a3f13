import { customRender, screen, waitFor } from '@healthcare-team-registration/tests'
import { server } from '@commons/services/mockServer'
import {
  getById,
  getPhysicians,
  updateHealthcareTeam
} from '@healthcare-team-registration/tests/mocks'
import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'

import EditView from '@healthcare-team-registration/views/EditView.vue'
import { Segments } from '@healthcare-team-registration/constants/healthcareTeam'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

const mockPush = vi.fn()

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    }),
    useRouter: vi.fn(() => ({
      push: mockPush
    }))
  }
})

describe('EditView', () => {
  const mockSnackbarAdd = vi.fn()

  const defaultProps = {
    provide: {
      snackbar: {
        value: {
          $el: {
            add: mockSnackbarAdd
          }
        }
      }
    }
  }

  beforeAll(() => server.listen())
  afterEach(() => {
    server.resetHandlers()
    vi.clearAllMocks()
  })
  afterAll(() => server.close())
  beforeEach(() => server.use(getPhysicians, getById, updateHealthcareTeam.successHandler))

  it('renders properly', async () => {
    customRender(EditView, defaultProps)
    await waitForWonderlandComponents(expect)

    expect(await screen.findByText('Voltar')).toBeInTheDocument()
    expect(await screen.findByText('Editar Time de Saúde')).toBeInTheDocument()

    const idInput = await screen.findByRole('textbox', { name: 'ID' })
    expect(idInput).toHaveValue('a0bb6bd7-e52e-479f-99c1-d546f9c79b00')
    expect(idInput).toBeDisabled()
    expect(await screen.findByRole('checkbox', { name: 'Ativo' })).toBeChecked()
    const segment = await screen.findByRole('combobox', { name: 'Segmento' })
    expect(segment).toHaveValue('DEFAULT')
    expect(segment).toHaveDisplayValue('Padrão')
    expect(
      await screen.findByRole('textbox', { name: 'Limite de Membros Associados' })
    ).toHaveValue('123')

    expect(await screen.findByRole('combobox', { name: 'Médica(o)' })).toHaveValue({
      id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79123',
      name: 'Dr. John Doe',
      type: 'MANAGER_PHYSICIAN'
    })
    expect(await screen.findByTestId('mock-address-form')).toBeInTheDocument()

    // Verify that the save button is initially disabled
    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    expect(saveButton).toBeEnabled()
  })

  it('enables save button when changes are made', async () => {
    const { user } = customRender(EditView, defaultProps)
    await waitForWonderlandComponents(expect)

    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    expect(saveButton).toBeEnabled()

    // Make a change to enable the button
    const memberLimit = await screen.findByRole('textbox', { name: 'Limite de Membros Associados' })
    await user.clear(memberLimit)
    await user.type(memberLimit, '10')

    // Verify button is now enabled
    await waitFor(async () => expect(saveButton).toBeEnabled())
  })

  it('updates segment selection', async () => {
    const { user } = customRender(EditView, defaultProps)
    await waitForWonderlandComponents(expect)

    const segment = await screen.findByRole('combobox', { name: 'Segmento' })
    await user.selectOptions(segment, Segments.PEDIATRIC)

    expect(segment).toHaveValue('PEDIATRIC')
  })

  it('toggles active status', async () => {
    const { user } = customRender(EditView, defaultProps)
    await waitForWonderlandComponents(expect)

    const activeSwitch = await screen.findByRole('checkbox', { name: 'Ativo' })
    await user.click(activeSwitch)

    expect(activeSwitch).not.toBeChecked()
  })

  it('saves form successfully', async () => {
    const { user } = customRender(EditView, defaultProps)
    await waitForWonderlandComponents(expect)

    // Make a change to enable the save button
    const memberLimit = await screen.findByRole('textbox', { name: 'Limite de Membros Associados' })
    await user.clear(memberLimit)
    await user.type(memberLimit, '10')

    // Wait for the button to be enabled
    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    await waitFor(() => expect(saveButton).toBeEnabled())

    // Click save button
    await user.click(saveButton)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith({ name: 'healthcare-team/list' })
    })

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Time de saúde atualizado com sucesso',
        icon: 'icCheckOutlined'
      })
    })
  })

  it('handles save error', async () => {
    const { user } = customRender(EditView, defaultProps)
    await waitForWonderlandComponents(expect)

    // Make a change to enable the save button
    const memberLimit = await screen.findByRole('textbox', { name: 'Limite de Membros Associados' })
    await user.clear(memberLimit)
    await user.type(memberLimit, '10')

    // Wait for the button to be enabled
    const saveButton = await screen.findByRole('button', { name: 'Salvar' })
    await waitFor(() => expect(saveButton).toBeEnabled())

    // Set up error handler and click save button
    server.use(updateHealthcareTeam.errorHandler)
    await user.click(saveButton)

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Erro ao atualizar o time de saúde',
        icon: 'icAlertCircleOutlined'
      })
    })

    await waitFor(() => {
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  it('renders address form component', async () => {
    customRender(EditView, defaultProps)
    await waitForWonderlandComponents(expect)

    const addressForm = await screen.findByTestId('mock-address-form')
    expect(addressForm).toBeInTheDocument()
  })
})
