<template>
  <ListLayout>
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="onCreate">Criar</WButton>
    </template>
    <template #list>
      <div class="filters">
        <WSelect aria-label="Status" :model-value="statusFilter" @WChange="statusChange">
          <option value="" selected>Status</option>
          <option v-for="item in Status" :key="item.label" :value="item.value">
            {{ item.label }}
          </option>
        </WSelect>
        <WListControllers
          has-pagination
          margin="none"
          has-items-per-page-select
          hide-input
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WPaginationPrevious="setPrev"
        >
          <div slot="select-items-per-page">
            <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
              <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
            </WSelect>
          </div>
        </WListControllers>
      </div>
      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.physicianStaff" size="medium">
              {{ item.physician?.name }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.segment" size="medium">
              {{ SegmentLabels[item.segment] }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.maxMemberAssociation" size="medium">
              {{ item.maxMemberAssociation }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.active" size="medium">
              <WIcon :icon="item.active ? 'icSuccess' : 'icClose'" />
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.edit" size="medium">
              <WButton
                icon-button
                icon="icEdit"
                variant="secondary"
                @click="editHealthcareTeam(item.id)"
              />
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuery } from '@tanstack/vue-query'
import { usePagination, useQueryString } from '@alice-health/vue-hooks'
import {
  WButton,
  WIcon,
  WListControllers,
  WSelect,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow
} from '@alice-health/wonderland-vue'
import { ListLayout } from '@commons/index'
import { SegmentLabels, Status } from '@healthcare-team-registration/constants/healthcareTeam'
import {
  getHealthcareTeams,
  type getHealthcareTeamsResponse
} from '@healthcare-team-registration/api/queries'

/**
 * Constants
 */
const limits = [5, 10, 15]

const columnWidthMap = {
  physicianStaff: '300px',
  segment: '200px',
  maxMemberAssociation: '250px',
  active: '60px',
  edit: ''
}

const headers = [
  { id: 1, title: 'Médica(o)', width: columnWidthMap.physicianStaff },
  { id: 2, title: 'Segmento', width: columnWidthMap.segment },
  { id: 3, title: 'Limite de Membros', width: columnWidthMap.maxMemberAssociation },
  { id: 4, title: 'Ativo', width: columnWidthMap.active },
  { id: 5, width: columnWidthMap.edit }
]

/**
 * Computed Variables
 */
const { objectToJsonString } = useQueryString()
const filter = computed(() =>
  statusFilter.value ? objectToJsonString({ active: statusFilter.value.toString() }) : ''
)

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()
const statusFilter = ref(route.query?.active ? Boolean(route.query.active) : true)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data } = useQuery({
  queryKey: ['healthcareTeams', getParams],
  queryFn: () => getHealthcareTeams(getParams.value),
  select
})

/**
 * Functions
 */

const onCreate = () => {
  router.push('/healthcare-team/create')
}

function editHealthcareTeam(id: string) {
  router.push({
    name: 'healthcare-team/update',
    params: { id }
  })
}

function statusChange(event: CustomEvent) {
  statusFilter.value = event.detail
  handleSearch()
}

function handleSearch() {
  const active = statusFilter.value.toString()
  router.push({ query: { ...route.query, page: initials.page, active } })
}

function select({ results, pagination }: getHealthcareTeamsResponse) {
  totalPages.value = pagination.totalPages

  return results
}
</script>

<style scoped lang="scss">
.filters {
  display: grid;
  grid-template-columns: auto auto;
  gap: var(--gl-spacing-04);
}
</style>
