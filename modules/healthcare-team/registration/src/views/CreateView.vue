<template>
  <FormLayout title="Novo Time de Saúde">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'healthcare-team/list' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <WSwitch label="Ativo" v-model="form.active" @WChange="validate('active')" />
      <WSelect label="Segmento" v-model="form.segment" @WChange="validate('segment')">
        <option v-for="segment in Segments" :key="segment" :value="segment">
          {{ SegmentLabels[segment] }}
        </option>
      </WSelect>
      <WTextfield
        label="Limite de Membros Associados"
        :value="memberAssociation"
        @WChange="handleMemberAssociationChange"
      />
      <WAutocomplete
        label="Médica(o)"
        aria-label="Médica(o)"
        :value="selectedPhysician"
        :items="physicianItems"
        search-by-key="name"
        @WChange="handlePhysicianChange"
        @WSelect="handlePhysicianSelect"
      />
    </template>
    <template #actions>
      <WButton
        variant="cta"
        size="large"
        @click="validateAndSave"
        :loading="saveIsPending"
        :disabled="invalid"
      >
        Salvar
      </WButton>
      <WButton
        variant="secondary"
        size="large"
        @click="saveAndCreateAddress"
        :loading="saveIsPending"
        :disabled="invalid"
      >
        Salvar e cadastrar endereço
      </WButton>
    </template>
  </FormLayout>
</template>

<script setup lang="ts">
import type { Ref } from 'vue'
import type { SnackbarComponentProps } from '@commons/index'
import type { Team } from '@healthcare-team-registration/models'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'
import type { Physician } from '@healthcare-team-registration/models'

import { inject, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { useValidation } from '@alice-health/vue-hooks'
import {
  WAutocomplete,
  WButton,
  WLink,
  WSelect,
  WSwitch,
  WTextfield
} from '@alice-health/wonderland-vue'
import { FormLayout } from '@commons/index'
import { Segments, SegmentLabels } from '@healthcare-team-registration/constants/healthcareTeam'
import { HealthcareTeamForm } from '@healthcare-team-registration/schemas/healthcareTeamRegistrationForm'
import { getPhysicians } from '@healthcare-team-registration/api/queries'
import { createHealthcareTeam } from '@healthcare-team-registration/api/mutations'

/**
 * Custom types
 */

type Form = Omit<Team, 'id' | 'createdAt' | 'updatedAt'>

/**
 * Refs
 */

const form: Ref<Form> = ref({
  active: true,
  segment: Segments.DEFAULT,
  maxMemberAssociation: 0,
  physician: null
})
const goToEditAddress = ref(false)

/**
 * Computeds
 */

const memberAssociation = computed(() => String(form.value.maxMemberAssociation))

const physicianItems = computed(() =>
  (physicians?.value || []).map((p: Physician) => ({
    id: p.id,
    name: p.name,
    type: p.type || ''
  }))
)

const selectedPhysician = computed(() =>
  form.value.physician
    ? {
        id: form.value.physician.id,
        name: form.value.physician.name,
        type: form.value.physician.type || ''
      }
    : ''
)

/*
 * Injections
 */

const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: HealthcareTeamForm,
  form
})

const router = useRouter()
const queryClient = useQueryClient()

const { data: physicians } = useQuery({
  queryKey: ['physicians'],
  queryFn: getPhysicians
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createHealthcareTeam'],
  mutationFn: () => createHealthcareTeam(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */

function handleMemberAssociationChange(value: CustomEvent) {
  if (!value.detail) {
    form.value.maxMemberAssociation = 0
    validate('maxMemberAssociation')
    return
  }
  form.value.maxMemberAssociation = parseInt(value.detail)
  validate('maxMemberAssociation')
}

function handlePhysicianChange(event: WAutocompleteCustomEvent<string>) {
  if (!event.detail) {
    form.value.physician = null
    validate('physician')
  }
}

function handlePhysicianSelect(
  event: WAutocompleteCustomEvent<{ id: string; name: string; type: string }>
) {
  form.value.physician = event.detail
  validate('physician')
}

function validateAndSave() {
  const schema = validateAll()
  if (schema.success) save()
}

function saveAndCreateAddress() {
  validateAndSave()
  goToEditAddress.value = true
}

async function onError() {
  goToEditAddress.value = false
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar o time de saúde',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess(data: Team) {
  await queryClient.invalidateQueries({ queryKey: ['healthcareTeams'] })
  snackbar?.value?.$el.add({
    message: 'Time de saúde cadastrado com sucesso',
    icon: 'icCheckOutlined'
  })
  if (goToEditAddress.value) {
    router.push(`/healthcare-team/update/${data.id}`)
  } else {
    router.push({ name: 'healthcare-team/list' })
  }
}
</script>
