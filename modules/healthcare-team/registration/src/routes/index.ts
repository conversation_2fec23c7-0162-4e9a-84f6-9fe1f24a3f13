import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const healthcareTeamRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/healthcare-team',
      name: 'healthcare-team',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'healthcare-team/list',
          component: async () => await import('@healthcare-team-registration/views/ListView.vue')
        },
        {
          path: 'create',
          name: 'healthcare-team/create',
          component: async () => await import('@healthcare-team-registration/views/CreateView.vue')
        },
        {
          path: 'update/:id',
          name: 'healthcare-team/update',
          component: async () => await import('@healthcare-team-registration/views/EditView.vue')
        }
      ]
    }
  ]
}

export default healthcareTeamRoutes
