import { string, object, boolean, number } from 'zod'

export const HealthcareTeamForm = object({
  id: string().optional().default(''),
  active: boolean().default(true),
  segment: string().min(1, { message: 'Segmento é obrigatório' }),
  maxMemberAssociation: number().positive({
    message: 'O limite de membros associados é obrigatório'
  }),
  physician: object({
    id: string(),
    name: string().min(1, { message: 'Médica(o) é obrigatório' }),
    type: string().optional().default('')
  })
    .nullable()
    .transform((value, ctx): { id: string; name: string; type: string | null } | null => {
      if (value == null)
        ctx.addIssue({
          code: 'custom',
          message: 'X Cannot be null'
        })
      return value
    }),
  address: object({
    id: string().optional().default(''),
    description: string().optional().default(''),
    street: string().min(1, { message: '<PERSON>ua é obrigatório' }),
    number: string().min(1, { message: 'Número é obrigatório' }),
    neighborhood: string().min(1, { message: 'Bairro é obrigatório' }),
    city: string().min(1, { message: 'Cidade é obrigatório' }),
    state: string().min(1, { message: 'Estado é obrigatório' }),
    zipcode: string().min(1, { message: 'CEP é obrigatório' }),
    complement: string().optional().nullable(),
    referencedModelId: string().optional().default(''),
    referencedModelClass: string().optional().default(''),
    active: boolean().default(true),
    lat: string().optional().nullable(),
    lng: string().optional().nullable(),
    createdAt: string().optional().default(''),
    updatedAt: string().optional().default('')
  }).optional()
})
