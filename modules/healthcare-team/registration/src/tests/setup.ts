import { vi } from 'vitest'
import { defineComponent } from 'vue'

const mockAddressForm = defineComponent({
  name: 'AddressForm',
  props: {
    address: {
      type: Object,
      required: false
    },
    referencedModelId: {
      type: String,
      required: false
    }
  },
  template: '<div data-testid="mock-address-form">Mock Address Form</div>'
})

vi.mock('@healthcare-team-address/components/AddressForm.vue', () => ({
  default: mockAddressForm
}))
