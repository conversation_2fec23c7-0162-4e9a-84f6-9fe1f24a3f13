import type { Team } from '@healthcare-team-registration/models'
import { http, HttpResponse } from 'msw'

const successHandler = http.post(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareTeam`,
  async ({ request }) => {
    const body = (await request.json()) as Team
    const id = 'mock-id-123'

    return HttpResponse.json({
      ...body,
      id
    })
  }
)

const errorHandler = http.post(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareTeam`,
  () => {
    return HttpResponse.error()
  }
)

export const createHealthcareTeam = {
  successHandler,
  errorHandler
}

export default successHandler
