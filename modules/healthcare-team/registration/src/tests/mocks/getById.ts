import type { Team } from '@healthcare-team-registration/models'

import { HttpResponse, http } from 'msw'

const getById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareTeam/:id`,
  ({ params }) => {
    const id = params.id as string

    const team: Team = {
      id,
      active: true,
      segment: 'DEFAULT',
      maxMemberAssociation: 123,
      physician: {
        id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79123',
        name: 'Dr. <PERSON>',
        type: 'MANAGER_PHYSICIAN'
      }
    }

    return HttpResponse.json(team)
  }
)

export default getById
