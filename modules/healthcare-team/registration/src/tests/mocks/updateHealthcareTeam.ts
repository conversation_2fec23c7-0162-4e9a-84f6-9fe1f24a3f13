import type { Team } from '@healthcare-team-registration/models'
import { http, HttpResponse } from 'msw'

const successHandler = http.put<{ id: string }>(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareTeam/:id`,
  async ({ params, request }) => {
    const { id } = params
    const body = (await request.json()) as Team

    return HttpResponse.json({
      ...body,
      id
    })
  }
)

const errorHandler = http.put(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareTeam/:id`,
  () => HttpResponse.error()
)

export const updateHealthcareTeam = {
  successHandler,
  errorHandler
}
