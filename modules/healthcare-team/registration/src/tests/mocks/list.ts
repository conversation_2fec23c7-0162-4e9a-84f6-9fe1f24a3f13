import type { Team } from '@healthcare-team-registration/models'

import { HttpResponse, http } from 'msw'

const listOfTeams: Team[] = [
  {
    id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
    active: true,
    segment: 'DEFAULT',
    maxMemberAssociation: 123,
    physician: {
      id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79123',
      name: 'Dr. <PERSON>',
      type: 'MANAGER_PHYSICIAN'
    }
  },
  {
    id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79abc',
    active: false,
    segment: 'PEDIATRIC',
    maxMemberAssociation: 12,
    physician: {
      id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79789',
      name: 'Dr. <PERSON>',
      type: 'MANAGER_PHYSICIAN'
    }
  }
]

const list = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareTeam`, () => {
  return HttpResponse.json({
    pagination: {
      totalPages: 1,
      pageSize: 1
    },
    results: listOfTeams
  })
})

export default list
