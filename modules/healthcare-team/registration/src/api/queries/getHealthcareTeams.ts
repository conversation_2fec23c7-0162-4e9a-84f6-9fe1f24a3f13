import type { PaginatedResponse } from '@commons/index'

import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'
import type { Team } from '@healthcare-team-registration/models'

export type getHealthcareTeamsResponse = PaginatedResponse<Team[]>

export const getHealthcareTeams = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getHealthcareTeamsResponse>(`/healthcareTeam${qs}`)
  return data
}
