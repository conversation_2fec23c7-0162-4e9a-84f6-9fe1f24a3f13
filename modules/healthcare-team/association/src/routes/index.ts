import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const teamAssociationRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/association',
      name: 'association',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'association-list',
          component: async () => await import('@healthcare-team-association/views/ListView.vue')
        }
      ]
    }
  ]
}

export default teamAssociationRoutes
