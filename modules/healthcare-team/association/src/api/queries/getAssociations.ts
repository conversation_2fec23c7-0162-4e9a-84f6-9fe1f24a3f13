import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'
import type { Association } from '@healthcare-team-association/models'

export type getAssociationsResponse = {
  results: Association[]
  pagination: {
    totalPages: number
  }
}

export const getAssociations = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getAssociationsResponse>(`/healthcareTeamAssociation${qs}`)
  return data
}
