import { HttpResponse, http } from 'msw'

const listOfTeamAssociation = [
  {
    id: 'a3847b4d-be2f-40b6-bd14-6742fa98dd00',
    person: {
      id: '1',
      fullName: '<PERSON>'
    },
    healthcareTeam: {
      id: '1',
      description: '<PERSON>'
    },
    multiStaffIds: []
  }
]

const list = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/healthcareTeamAssociation`,
  () => {
    return HttpResponse.json({
      pagination: {
        totalPages: 1,
        pageSize: 1
      },
      results: listOfTeamAssociation
    })
  }
)

export default list
