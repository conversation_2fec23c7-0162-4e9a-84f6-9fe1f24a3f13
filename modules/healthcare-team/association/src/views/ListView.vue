<template>
  <ListLayout>
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="onCreate">Criar</WButton>
    </template>

    <template #list>
      <WListControllers
        has-pagination
        margin="none"
        has-items-per-page-select
        input-placeholder="Buscar"
        :hide-input="false"
        :value="term"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WPaginationNext="setNext"
        @WInputChange="searchChange"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">
              {{ limit }}
            </option>
          </WSelect>
        </div>
      </WListControllers>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell size="large" v-for="header in headers" :key="header.title">
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell size="medium">
              {{ item.person.fullName }}
            </WTableBodyCell>
            <WTableBodyCell style="word-break: break-all" size="medium">
              {{ item.healthcareTeam.description }}
            </WTableBodyCell>
            <WTableBodyCell size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="edit(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>
<script setup lang="ts">
import {
  getAssociations,
  type getAssociationsResponse
} from '@healthcare-team-association/api/queries'

import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect
} from '@alice-health/wonderland-vue'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { ListLayout } from '@commons/index'

/**
 * Constants
 */
const limits = [5, 10, 15]

const headers = [{ title: 'Membro' }, { title: 'Time de Saúde' }, { title: '' }]

/**
 * Computed Variables
 */
const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')
const filter = computed(() => (term.value ? objectToJsonString({ q: term.value }) : ''))

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()
const searchTerm = ref('')
const searchTermDebounced = useDebounce(searchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data } = useQuery({
  queryKey: ['association', getParams],
  queryFn: () => getAssociations(getParams.value),
  select
})

/**
 * Functions
 */

const onCreate = () => {
  router.push({ name: 'association-create' })
}

function edit(id: string) {
  router.push({
    name: 'association-edit',
    params: { id }
  })
}

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function select({ results, pagination }: getAssociationsResponse) {
  totalPages.value = pagination.totalPages

  return results
}

/**
 * Lifecycle
 */

watch(searchTermDebounced, handleSearch)
</script>
