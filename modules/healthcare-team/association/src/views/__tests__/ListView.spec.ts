import { customRender, screen, waitFor } from '@healthcare-team-association/tests'
import { server } from '@commons/services/mockServer'
import { list } from '@healthcare-team-association/tests/mocks'

import ListView from '@healthcare-team-association/views/ListView.vue'

const mockPush = vi.fn()

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRouter: vi.fn(() => ({
      push: mockPush
    }))
  }
})

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(list))

  it('renders associations list properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()
    expect(await screen.findByRole('textbox')).toBeInTheDocument()

    expect(screen.getByRole('cell', { name: 'Luigi Moreira' })).toBeInTheDocument()
    expect(screen.getByRole('cell', { name: 'João Gabriel Ramos' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'icEdit' })).toBeInTheDocument()
  })

  it('should go to edit association', async () => {
    customRender(ListView)

    const editButton = await screen.findByRole('button', { name: 'icEdit' })

    expect(editButton).toBeInTheDocument()

    editButton.click()

    await waitFor(() =>
      expect(mockPush).toHaveBeenCalledWith({
        name: 'association-edit',
        params: { id: 'a3847b4d-be2f-40b6-bd14-6742fa98dd00' }
      })
    )
  })

  it('should go to create association', async () => {
    customRender(ListView)

    const createButton = await screen.findByRole('button', { name: 'Criar' })

    expect(createButton).toBeInTheDocument()

    createButton.click()

    await waitFor(() =>
      expect(mockPush).toHaveBeenCalledWith({
        name: 'association-create'
      })
    )
  })

  it('should update URL with search term when filtering', async () => {
    const { user } = customRender(ListView)

    const searchInput = await screen.findByPlaceholderText('Buscar')

    expect(searchInput).toBeInTheDocument()

    await user.type(searchInput, 'Luigi')

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith({
        query: {
          page: 1,
          term: 'Luigi'
        }
      })
    })
  })
})
