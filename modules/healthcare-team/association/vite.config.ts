/// <reference types='vitest' />
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin'
import { fileURLToPath } from 'url'
import path from 'path'

export default defineConfig({
  root: __dirname,
  cacheDir: '../../../node_modules/.vite/modules/healthcare-team/association',

  plugins: [
    vue(),
    nxViteTsPaths(),
    dts({
      entryRoot: 'src',
      tsConfigFilePath: path.join(__dirname, 'tsconfig.lib.json'),
      skipDiagnostics: true
    })
  ],

  resolve: {
    alias: {
      '@commons': fileURLToPath(new URL('../../core/commons/src', import.meta.url)),
      '@healthcare-team-association': fileURLToPath(new URL('./src', import.meta.url)),
      '@http': fileURLToPath(new URL('../../core/http/src', import.meta.url))
    }
  },

  build: {
    outDir: '../../../dist/modules/healthcare-team/association',
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true
    },
    lib: {
      // Could also be a dictionary or array of multiple entry points.
      entry: 'src/index.ts',
      name: 'healthcare-team-association',
      fileName: 'index',
      // Change this to the formats you want to support.
      // Don't forget to update your package.json as well.
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      // External packages that should not be bundled into your library.
      external: ['vite-tsconfig-paths']
    }
  }
})
