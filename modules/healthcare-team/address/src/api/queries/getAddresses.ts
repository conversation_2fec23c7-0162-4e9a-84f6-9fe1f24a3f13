import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'
import type { AddressResumed } from '@healthcare-team-address/models'

export const getAddresses = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<AddressResumed[]>(`/structuredAddress/address/search${qs}`)
  return data
}
