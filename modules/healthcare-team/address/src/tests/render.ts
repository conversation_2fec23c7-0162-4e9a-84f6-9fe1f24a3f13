import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'

export * from '@alice-health/unit-presets/src/testing-library'

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  const routerInstance = createRouter({
    history: createWebHistory('/'),
    routes: [
      { path: '/', name: 'home', component: async () => ({}) },
      { path: '/healthcare-team', name: 'healthcare-team/list', component: async () => ({}) },
      {
        path: '/healthcare-team/create',
        name: 'healthcare-team/create',
        component: async () => ({})
      },
      {
        path: '/healthcare-team/update/:id',
        name: 'healthcare-team/update',
        component: async () => ({})
      }
    ]
  })

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}
