import { HttpResponse, http } from 'msw'

const mockAddressExpanded = {
  id: '123',
  country: 'BR',
  state: 'TS',
  city: 'Test City',
  street: 'Test Street',
  number: '123',
  neighbourhood: 'Test Neighborhood',
  postalCode: '12345-678',
  lat: '0',
  lng: '0'
}

const getAddress = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/structuredAddress/address/:placeId`,
  ({ params }) => {
    const placeId = params.placeId as string

    if (placeId === '123') {
      return HttpResponse.json(mockAddressExpanded)
    }

    return new HttpResponse(null, { status: 404 })
  }
)

export default getAddress
