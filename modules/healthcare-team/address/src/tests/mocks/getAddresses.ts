import { HttpResponse, http } from 'msw'

const mockAddressResumed = {
  placeId: '123',
  description: 'Test Street, 123',
  mainText: 'Test Street',
  secondaryText: 'Test Neighborhood, Test City'
}

const getAddresses = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/structuredAddress/address/search`,
  ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get('q')

    if (query === 'Test Street') {
      return HttpResponse.json([mockAddressResumed])
    }

    return HttpResponse.json([])
  }
)

export default getAddresses
