import { HttpResponse, http } from 'msw'
import type { Address } from '@healthcare-team-address/models'

const mockAddress: Address = {
  id: '123',
  street: 'Test Street',
  number: '123',
  neighborhood: 'Test Neighborhood',
  city: 'Test City',
  state: 'TS',
  zipcode: '12345-678',
  complement: 'Test Complement',
  active: true,
  latitude: '0',
  longitude: '0',
  createdAt: '2024-01-01',
  updatedAt: '2024-01-01',
  referencedModelId: '456',
  referencedModelClass: 'HEALTHCARE_TEAM'
}

type AddressRequestBody = Partial<Omit<Address, 'id' | 'createdAt' | 'updatedAt'>>

const createAddress = http.post(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/structuredAddress`,
  async ({ request }) => {
    const requestBody = (await request.json()) as AddressRequestBody

    // Return error if only number is provided
    if (requestBody.number && !requestBody.street) {
      return new HttpResponse(null, { status: 400 })
    }

    const responseBody: Address = {
      ...mockAddress,
      ...requestBody
    }
    return HttpResponse.json(responseBody)
  }
)

const updateAddress = http.put(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/structuredAddress/:id`,
  async ({ params, request }) => {
    const id = params.id as string
    const requestBody = (await request.json()) as AddressRequestBody

    if (id === '123') {
      const responseBody: Address = {
        ...mockAddress,
        ...requestBody
      }
      return HttpResponse.json(responseBody)
    }

    return new HttpResponse(null, { status: 404 })
  }
)

export { createAddress, updateAddress }
