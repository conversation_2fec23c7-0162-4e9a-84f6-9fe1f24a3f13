import { boolean, object, string } from 'zod'

export const AddressForm = object({
  id: string().optional().nullable(),
  street: string().min(1, { message: 'Rua é obrigatório' }),
  number: string().min(1, { message: 'Número é obrigatório' }),
  neighborhood: string().min(1, { message: 'Bairro é obrigatório' }),
  city: string().min(1, { message: 'Cidade é obrigatório' }),
  state: string().min(1, { message: 'Estado é obrigatório' }),
  zipcode: string().min(1, { message: 'CEP é obrigatório' }),
  complement: string().optional(),
  referencedModelId: string().optional().default(''),
  referencedModelClass: string().optional().default(''),
  active: boolean().default(true),
  latitude: string().optional().nullable(),
  longitude: string().optional().nullable(),
  createdAt: string().optional().nullable(),
  updatedAt: string().optional().nullable()
})
