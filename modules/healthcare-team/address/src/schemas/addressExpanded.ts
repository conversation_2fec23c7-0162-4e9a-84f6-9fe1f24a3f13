import { object, string } from 'zod'

export const AddressExpanded = object({
  id: string().min(1),
  country: string().min(1),
  state: string().min(1),
  city: string().min(1),
  street: string().optional().default(''),
  number: string().optional().default(''),
  neighbourhood: string().min(1),
  postalCode: string().min(1),
  lat: string().optional().default(''),
  lng: string().optional().default('')
})
