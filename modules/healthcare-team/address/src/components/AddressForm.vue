<template>
  <div class="address-form__card">
    <WTitle variant="heavy" size="small">Endereço</WTitle>
    <div class="double-column">
      <WSwitch :disabled="!isEditing" label="Ativo" v-model="form.active" />
      <WButton v-if="!isEditing" variant="secondary" icon="icEdit" @click="switchIsEditing">
        Editar
      </WButton>
    </div>
    <WAutocomplete
      v-if="isEditing"
      label="Endereço"
      leading-icon="icSearch"
      placeholder="Busque pelo logradouro e número ou pelo nome do local"
      searchByKey="description"
      :items="addresses || []"
      :loading="isAddressesRefetching"
      :value="addressSelected"
      @WChange="handleAddressesChanged"
      @WSelect="handleAddressSelected"
    />
    <WTextfield disabled label="Logradouro" v-model="form.street" />
    <div class="double-column double-column--stretch">
      <WTextfield
        :disabled="!isEditing"
        label="Número"
        input-mode="numeric"
        v-model="form.number"
      />
      <WTextfield :disabled="!isEditing" label="Complemento" v-model="form.complement" />
    </div>
    <div class="double-column double-column--stretch">
      <WTextfield disabled label="Bairro" v-model="form.neighborhood" />
      <WTextfield disabled label="CEP" mask-type="cep" v-model="form.zipcode" />
    </div>
    <div class="double-column double-column--stretch">
      <WTextfield disabled label="Cidade" v-model="form.city" />
      <WTextfield disabled label="Estado" v-model="form.state" />
    </div>
    <div class="double-column">
      <WButton
        v-if="isEditing"
        variant="cta"
        :loading="createIsPending || updateIsPending"
        @click="handleSaveAddress"
      >
        Salvar endereço
      </WButton>
      <WButton v-if="isEditing" variant="secondary" @click="switchIsEditing"> Cancelar</WButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watch, type Ref } from 'vue'
import { useDebounce } from '@alice-health/vue-hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import type { SnackbarComponentProps } from '@commons/index'
import { WAutocomplete, WButton, WSwitch, WTextfield, WTitle } from '@alice-health/wonderland-vue'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'

import { createAddress, updateAddress } from '@healthcare-team-address/api/mutations'
import { getAddresses, getAddress } from '@healthcare-team-address/api/queries'
import type { Address, AddressResumed } from '@healthcare-team-address/models'

/**
 * Props
 */

interface AddressFormProps {
  address?: Address
  referencedModelId: string
}
const props = defineProps<AddressFormProps>()

/**
 * Refs
 */

const emptyAddress = () => ({
  id: null,
  street: '',
  number: '',
  neighborhood: '',
  city: '',
  state: '',
  zipcode: '',
  complement: '',
  referencedModelId: props.referencedModelId,
  referencedModelClass: 'HEALTHCARE_TEAM',
  active: false,
  latitude: '',
  longitude: '',
  createdAt: null,
  updatedAt: null
})
const isEditing = ref<boolean>(false)
const addressSearchTerm = ref<string | undefined>('')
const addressSelected: Ref<string> = ref(props.address?.id || '')
const form = ref<Address>(
  props.address
    ? {
        ...props.address
      }
    : emptyAddress()
)

/**
 * Computed Variables
 */

const getAddressFilter = computed(() =>
  addressSearchTermDebounced.value ? addressSearchTermDebounced.value : ''
)

const getAddressParams = computed(() => ({
  q: getAddressFilter.value
}))

/*
 * Injections
 */

const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const queryClient = useQueryClient()
const addressSearchTermDebounced = useDebounce(addressSearchTerm, 800)

const {
  data: addresses,
  isRefetching: isAddressesRefetching,
  refetch: refetchAddresses
} = useQuery({
  queryKey: ['structuredAddresses', getAddressFilter, addressSearchTermDebounced],
  queryFn: () => getAddresses(getAddressParams.value),
  enabled: false
})

const { data: addressExpanded, refetch: refetchAddressExpanded } = useQuery({
  queryKey: ['structuredAddress', addressSelected],
  queryFn: () => getAddress(addressSelected.value || ''),
  enabled: false
})

const { mutate: mutationCreateAddress, isPending: createIsPending } = useMutation({
  mutationKey: ['createAddress'],
  mutationFn: () => createAddress(form.value),
  onSuccess,
  onError
})

const { mutate: mutationUpdateAddress, isPending: updateIsPending } = useMutation({
  mutationKey: ['updateAddress'],
  mutationFn: () => updateAddress(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */
function handleAddressesChanged(event: WAutocompleteCustomEvent<string>) {
  addressSearchTerm.value = event.detail
  if (!event.detail) {
    addressSelected.value = ''
  }
}

function handleAddressSelected(event: CustomEvent<AddressResumed>) {
  addressSelected.value = event.detail.placeId
  refetchAddressExpanded()
}

function switchIsEditing() {
  isEditing.value = !isEditing.value
}

function handleSaveAddress() {
  if (form.value.id) {
    mutationUpdateAddress()
    return
  }
  mutationCreateAddress()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao salvar o endereço',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['healthcareTeam'], exact: false })
  snackbar?.value?.$el.add({
    message: 'Endereço salvo com sucesso',
    icon: 'icCheckOutlined'
  })
  switchIsEditing()
}

/**
 * Watchers
 */
watch(addressExpanded, () => {
  if (!addressExpanded.value) return
  form.value = {
    ...form.value,
    street: addressExpanded.value.street,
    number: addressExpanded.value.number,
    neighborhood: addressExpanded.value.neighbourhood,
    city: addressExpanded.value.city,
    state: addressExpanded.value.state,
    zipcode: addressExpanded.value.postalCode,
    complement: '',
    active: true,
    latitude: addressExpanded.value.lat,
    longitude: addressExpanded.value.lng
  }
})

watch(addressSearchTermDebounced, () => {
  refetchAddresses()
})
</script>

<style lang="scss" scoped>
.address-form__card {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-06);
  background-color: var(--gl-color-off-gray-00);
  padding: var(--gl-spacing-04);
  border-radius: var(--gl-border-radius-md);
}

.double-column {
  display: flex;
  gap: var(--gl-spacing-04);
  justify-content: space-between;
  &--stretch > * {
    flex: 1;
  }
}
</style>
