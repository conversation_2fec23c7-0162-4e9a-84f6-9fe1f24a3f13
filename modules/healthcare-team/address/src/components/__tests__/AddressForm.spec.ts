import { waitForWonderlandComponents } from '@alice-health/unit-presets/src/design-system'
import { customRender, screen, waitFor, within } from '@healthcare-team-address/tests'
import { server } from '@commons/services/mockServer'
import {
  getAddresses,
  getAddress,
  createAddress,
  updateAddress
} from '@healthcare-team-address/tests/mocks'
import type { Address } from '@healthcare-team-address/models'
import AddressForm from '../AddressForm.vue'

describe('AddressForm', () => {
  const mockSnackbarAdd = vi.fn()

  const mockAddress: Address = {
    id: '123',
    street: 'Test Street',
    number: '123',
    neighborhood: 'Test Neighborhood',
    city: 'Test City',
    state: 'TS',
    zipcode: '12345-678',
    complement: 'Test Complement',
    active: true,
    latitude: '0',
    longitude: '0',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    referencedModelId: '456',
    referencedModelClass: 'HEALTHCARE_TEAM'
  }

  beforeAll(() => server.listen())
  afterEach(() => {
    server.resetHandlers()
    vi.clearAllMocks()
  })
  afterAll(() => server.close())
  beforeEach(() => {
    server.use(getAddresses, getAddress, createAddress, updateAddress)
  })

  const renderWithMocks = (props = {}) => {
    return customRender(AddressForm, {
      props: {
        referencedModelId: '456',
        ...props
      },
      provide: {
        snackbar: {
          value: {
            $el: {
              add: mockSnackbarAdd
            }
          }
        }
      }
    })
  }

  it('renders properly', async () => {
    renderWithMocks()
    await waitForWonderlandComponents(expect)

    expect(screen.getByText('Endereço')).toBeInTheDocument()

    const activeSwitch = await screen.findByRole('checkbox', { name: 'Ativo' })
    expect(activeSwitch).toBeInTheDocument()
    expect(activeSwitch).toBeDisabled()

    const autocomplete = screen.queryByLabelText('Endereço')
    expect(autocomplete).not.toBeInTheDocument()

    // Everything should be disabled but the edit button
    const editButton = await screen.findByRole('button', { name: 'Editar' })
    expect(editButton).toBeInTheDocument()
    expect(editButton).toBeEnabled()

    const streetInput = await screen.findByRole('textbox', { name: 'Logradouro' })
    expect(streetInput).toBeInTheDocument()
    expect(streetInput).toBeDisabled()

    const numberInput = await screen.findByRole('textbox', { name: 'Número' })
    expect(numberInput).toBeInTheDocument()
    expect(numberInput).toBeDisabled()

    const complementInput = await screen.findByRole('textbox', { name: 'Complemento' })
    expect(complementInput).toBeInTheDocument()
    expect(complementInput).toBeDisabled()

    const neighborhoodInput = await screen.findByRole('textbox', { name: 'Bairro' })
    expect(neighborhoodInput).toBeInTheDocument()
    expect(neighborhoodInput).toBeDisabled()

    const zipcodeInput = await screen.findByRole('textbox', { name: 'CEP' })
    expect(zipcodeInput).toBeInTheDocument()
    expect(zipcodeInput).toBeDisabled()

    const cityInput = await screen.findByRole('textbox', { name: 'Cidade' })
    expect(cityInput).toBeInTheDocument()
    expect(cityInput).toBeDisabled()

    const stateInput = await screen.findByRole('textbox', { name: 'Estado' })
    expect(stateInput).toBeInTheDocument()
    expect(stateInput).toBeDisabled()

    const saveButton = screen.queryByRole('button', { name: 'Salvar endereço' })
    expect(saveButton).not.toBeInTheDocument()

    const cancelButton = screen.queryByRole('button', { name: 'Cancelar' })
    expect(cancelButton).not.toBeInTheDocument()
  })

  it('handles edit mode', async () => {
    const { user } = renderWithMocks()
    await waitForWonderlandComponents(expect)

    await user.click(await screen.findByRole('button', { name: 'Editar' }))

    expect(await screen.findByRole('button', { name: 'Salvar endereço' })).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: 'Cancelar' })).toBeInTheDocument()

    const streetInput = await screen.findByRole('textbox', { name: 'Logradouro' })
    expect(streetInput).toBeInTheDocument()
    expect(streetInput).toBeDisabled()

    // Only the number and complement inputs should be enabled
    const numberInput = await screen.findByRole('textbox', { name: 'Número' })
    expect(numberInput).toBeInTheDocument()
    expect(numberInput).toBeEnabled()

    const complementInput = await screen.findByRole('textbox', { name: 'Complemento' })
    expect(complementInput).toBeInTheDocument()
    expect(complementInput).toBeEnabled()

    const neighborhoodInput = await screen.findByRole('textbox', { name: 'Bairro' })
    expect(neighborhoodInput).toBeInTheDocument()
    expect(neighborhoodInput).toBeDisabled()

    const zipcodeInput = await screen.findByRole('textbox', { name: 'CEP' })
    expect(zipcodeInput).toBeInTheDocument()
    expect(zipcodeInput).toBeDisabled()

    const cityInput = await screen.findByRole('textbox', { name: 'Cidade' })
    expect(cityInput).toBeInTheDocument()
    expect(cityInput).toBeDisabled()

    const stateInput = await screen.findByRole('textbox', { name: 'Estado' })
    expect(stateInput).toBeInTheDocument()
    expect(stateInput).toBeDisabled()

    const saveButton = await screen.findByRole('button', { name: 'Salvar endereço' })
    expect(saveButton).toBeInTheDocument()
    expect(saveButton).toBeEnabled()

    const cancelButton = await screen.findByRole('button', { name: 'Cancelar' })
    expect(cancelButton).toBeInTheDocument()
    expect(cancelButton).toBeEnabled()

    await user.click(cancelButton)

    expect(screen.queryByRole('button', { name: 'Salvar endereço' })).not.toBeInTheDocument()
    expect(screen.queryByRole('button', { name: 'Cancelar' })).not.toBeInTheDocument()
  })

  it.skip('handles address search, selection and save', async () => {
    const { user } = renderWithMocks()
    await waitForWonderlandComponents(expect)

    // Enter edit mode
    await user.click(await screen.findByRole('button', { name: 'Editar' }))

    const autocomplete = await screen.findByLabelText('Endereço')
    const autocompleteInput = within(autocomplete).getByPlaceholderText(
      'Busque pelo logradouro e número ou pelo nome do local'
    )

    // Type the search query
    await user.type(autocompleteInput, 'Test Street')

    // Click the option
    await user.click(await screen.findByText(/Test Street, 123/))

    // Wait for each field to be filled with the correct value from the expanded address
    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'Logradouro' })).toHaveValue('Test Street')
    })

    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'Número' })).toHaveValue('123')
    })

    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'Bairro' })).toHaveValue('Test Neighborhood')
    })

    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'CEP' })).toHaveValue('12345-678')
    })

    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'Cidade' })).toHaveValue('Test City')
    })

    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'Estado' })).toHaveValue('TS')
    })

    // Submit form
    await user.click(await screen.findByRole('button', { name: 'Salvar endereço' }))

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Endereço salvo com sucesso',
        icon: 'icCheckOutlined'
      })
    })
  })

  it('initializes form with existing address data', async () => {
    renderWithMocks({ address: mockAddress })
    await waitForWonderlandComponents(expect)

    const streetInput = await screen.findByRole('textbox', { name: 'Logradouro' })
    const numberInput = await screen.findByRole('textbox', { name: 'Número' })
    const complementInput = await screen.findByRole('textbox', { name: 'Complemento' })
    const neighborhoodInput = await screen.findByRole('textbox', { name: 'Bairro' })
    const zipcodeInput = await screen.findByRole('textbox', { name: 'CEP' })
    const cityInput = await screen.findByRole('textbox', { name: 'Cidade' })
    const stateInput = await screen.findByRole('textbox', { name: 'Estado' })

    expect(streetInput).toHaveValue(mockAddress.street)
    expect(numberInput).toHaveValue(mockAddress.number)
    expect(complementInput).toHaveValue(mockAddress.complement)
    expect(neighborhoodInput).toHaveValue(mockAddress.neighborhood)
    expect(zipcodeInput).toHaveValue(mockAddress.zipcode)
    expect(cityInput).toHaveValue(mockAddress.city)
    expect(stateInput).toHaveValue(mockAddress.state)
  })

  it('handles error on save', async () => {
    const { user } = renderWithMocks()
    await waitForWonderlandComponents(expect)

    // Enter edit mode
    await user.click(await screen.findByRole('button', { name: 'Editar' }))
    await waitFor(() => {
      expect(screen.getByRole('textbox', { name: 'Número' })).toBeEnabled()
    })

    // Fill form with invalid data to trigger error (only number)
    await user.type(await screen.findByRole('textbox', { name: 'Número' }), '123')

    // Submit form
    await user.click(await screen.findByRole('button', { name: 'Salvar endereço' }))

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Erro ao salvar o endereço',
        icon: 'icAlertCircleOutlined'
      })
    })
  })

  it('successfully updates existing address', async () => {
    const { user } = renderWithMocks({ address: mockAddress })
    await waitForWonderlandComponents(expect)

    // Enter edit mode
    await user.click(await screen.findByRole('button', { name: 'Editar' }))

    // Update form
    const numberInput = await screen.findByRole('textbox', { name: 'Número' })
    await user.type(numberInput, '456')

    // Submit form
    await user.click(await screen.findByRole('button', { name: 'Salvar endereço' }))

    await waitFor(() => {
      expect(mockSnackbarAdd).toHaveBeenCalledWith({
        message: 'Endereço salvo com sucesso',
        icon: 'icCheckOutlined'
      })
    })
  })
})
