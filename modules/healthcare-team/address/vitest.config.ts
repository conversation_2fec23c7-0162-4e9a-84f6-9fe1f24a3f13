import vue from '@vitejs/plugin-vue'
import { fileURLToPath } from 'url'
import { searchForWorkspaceRoot } from 'vite'

export default {
  plugins: [vue()],
  server: {
    fs: {
      allow: [searchForWorkspaceRoot(process.cwd()), '../../../vitest.setup.ts']
    }
  },
  test: {
    globals: true,
    environment: 'jsdom',
    cacheDir: '../../../node_modules/.vitest',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    setupFiles: ['../../../vitest.setup.ts'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: '../../../coverage/modules/healthcare-team/address',
      provider: 'v8',
      reporter: ['lcov']
    }
  },
  resolve: {
    alias: {
      '@healthcare-team-address': fileURLToPath(new URL('./src', import.meta.url)),
      '@http': fileURLToPath(new URL('../../core/http/src', import.meta.url)),
      '@alice-backoffice/auth': fileURLToPath(new URL('../../core/auth/src', import.meta.url)),
      '@commons': fileURLToPath(new URL('../../core/commons/src', import.meta.url))
    }
  }
}
