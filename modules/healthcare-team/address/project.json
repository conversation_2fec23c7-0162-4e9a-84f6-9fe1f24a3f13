{"name": "healthcare-team-address", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "modules/healthcare-team/address/src", "projectType": "library", "tags": [], "// targets": "to see all targets run: nx show project healthcare-team-address --web", "targets": {"test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/{projectRoot}", "config": "modules/healthcare-team/address/vitest.config.ts"}, "configurations": {"ci": {"coverage": true, "reportsDirectory": "../../coverage/{projectRoot}"}}}}}