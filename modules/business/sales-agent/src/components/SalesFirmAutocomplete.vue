<template>
  <WAutocomplete
    data-testid="sales-firm-autocomplete"
    class="sales-firm-autocomplete"
    :value="salesFirmSelected"
    placeholder="Busque pelo nome da corretora"
    :items="salesFirms || []"
    searchByKey="name"
    required
    :loading="isSalesFirmsRefetching"
    @WBur="emit('blur')"
    @WChange="handleSalesFirmChanged"
    @WSelect="handleSalesFirmSelected"
  />
</template>

<script setup lang="ts">
import { computed, ref, type Ref } from 'vue'
import type { SalesFirm } from '../models/SalesFirm'
import { useDebounce, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { getSalesFirms } from '@business-sales-agent/api/queries'
import { SALES_AGENT_FIRMS } from '@business-sales-agent/constants/queryKeys'
import { WAutocomplete } from '@alice-health/wonderland-vue'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'

/**
 * Props
 */
interface SalesFirmAutocompleteProps {
  salesFirm?: SalesFirm
}
const props = defineProps<SalesFirmAutocompleteProps>()

interface SalesFirmAutocompleteEvents {
  (e: 'select', salesFirm?: SalesFirm): void
  (e: 'blur'): void
}
const emit = defineEmits<SalesFirmAutocompleteEvents>()

/**
 * Refs
 */
const salesFirmSearchTerm = ref<string | undefined>(props.salesFirm?.name)
const salesFirmSelected: Ref<SalesFirm | undefined> = ref(props.salesFirm)

/**
 * Computed Variables
 */
const getSalesFirmFilter = computed(() => {
  const filterObject = {
    ...(salesFirmSearchTermDebounced.value ? { q: salesFirmSearchTermDebounced.value } : {})
  }

  return objectToJsonString(filterObject)
})

const getSalesFirmParams = computed(() => ({
  filter: getSalesFirmFilter.value
}))

/**
 * Hooks
 */
const { objectToJsonString } = useQueryString()

const salesFirmSearchTermDebounced = useDebounce(salesFirmSearchTerm, 800)

const { data: salesFirms, isRefetching: isSalesFirmsRefetching } = useQuery({
  queryKey: [...SALES_AGENT_FIRMS, getSalesFirmParams],
  queryFn: () => getSalesFirms(getSalesFirmParams.value),
  placeholderData: props.salesFirm ? [props.salesFirm] : undefined
})

/**
 * Functions
 */
function handleSalesFirmChanged(event: WAutocompleteCustomEvent<string>) {
  salesFirmSearchTerm.value = event.detail
  if (!event.detail) {
    salesFirmSelected.value = undefined
    emit('select', undefined)
  }
}

function handleSalesFirmSelected(event: CustomEvent<SalesFirm>) {
  salesFirmSelected.value = event.detail
  emit('select', event.detail)
}
</script>

<style lang="scss" scoped>
.sales-firm-autocomplete {
  width: 80%;
}
</style>
