import { getSalesFirms } from '@business-sales-agent/tests/mocks'
import { server } from '@commons/services/mockServer'
import { customRender, screen, user, waitFor } from '@business-sales-agent/tests'
import SalesFirmsSelector from '../SalesFirmsSelector.vue'

describe('SalesFirmSelector', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => {
    server.use(getSalesFirms)

    vi.clearAllMocks()
  })

  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(SalesFirmsSelector, { props: { modelValue: [{ id: '', name: '' }] } })

    expect(await screen.findByRole('heading', { name: 'Corretoras' })).toBeInTheDocument()
    expect(await screen.findByRole('button', { name: '<PERSON><PERSON>ona<PERSON>' })).toBeInTheDocument()
    expect(await screen.findByPlaceholderText('Busque pelo nome da corretora'))
    expect(await screen.findByRole('button', { name: 'icDelete' })).toBeDisabled()
  })

  it('add new sales firm field when clicks in add', async () => {
    customRender(SalesFirmsSelector, { props: { modelValue: [{ id: '', name: '' }] } })

    await user.type(
      await screen.findByPlaceholderText('Busque pelo nome da corretora'),
      'Coopers Firm'
    )
    await user.click(await screen.findByText('Coopers Firm'))

    await waitFor(async () =>
      expect(await screen.findByRole('button', { name: 'Adicionar' })).toBeEnabled()
    )
    await user.click(screen.getByRole('button', { name: 'Adicionar' }))

    await waitFor(async () =>
      expect(await screen.findAllByPlaceholderText('Busque pelo nome da corretora')).toHaveLength(2)
    )
  })

  it('remove sales firm field when clicks in remove', async () => {
    customRender(SalesFirmsSelector, { props: { modelValue: [{ id: '', name: '' }] } })

    await user.type(
      await screen.findByPlaceholderText('Busque pelo nome da corretora'),
      'Coopers Firm'
    )
    await user.click(await screen.findByText('Coopers Firm'))

    await user.click(await screen.findByRole('button', { name: 'Adicionar' }))

    await user.click((await screen.findAllByRole('button', { name: 'icDelete' }))[0])

    expect(screen.getAllByPlaceholderText('Busque pelo nome da corretora')).toHaveLength(1)
  })
})
