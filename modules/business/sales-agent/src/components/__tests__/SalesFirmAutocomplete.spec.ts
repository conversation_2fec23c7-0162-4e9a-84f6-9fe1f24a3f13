import { getSalesFirms } from '@business-sales-agent/tests/mocks'
import { server } from '@commons/services/mockServer'
import SalesFirmAutocomplete from '../SalesFirmAutocomplete.vue'
import { customRender, screen } from '@business-sales-agent/tests'
import SalesFirmsSelector from '../SalesFirmsSelector.vue'

describe('SalesFirmAutocomplete', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => {
    server.use(getSalesFirms)

    vi.clearAllMocks()
  })

  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(SalesFirmAutocomplete)

    expect(await screen.findByPlaceholderText('Busque pelo nome da corretora'))
  })
})
