<template>
  <div class="sales-firms-selector">
    <div class="sales-firms-selector__header">
      <WTitle>Corretoras</WTitle>
      <WButton
        variant="cta"
        icon="icAdd"
        iconPosition="trailing"
        :disabled="disableAddButton"
        @click="handleSalesFirmAdd"
      >
        Adicionar
      </WButton>
    </div>

    <div
      v-for="(salesFirm, index) in salesFirmsSelected"
      :key="salesFirm.uniqueId"
      class="sales-firms-selector__item"
    >
      <SalesFirmAutocomplete
        :salesFirm="salesFirm"
        @select="(value) => handleSalesFirmSelected(index, value)"
      />

      <WButton
        iconButton
        icon="icDelete"
        :disabled="hasOneItem"
        @click="handleSalesFirmRemove(index)"
      />
    </div>

    <WHelper v-if="errorText" mode="error">{{ errorText }}</WHelper>
  </div>
</template>

<script setup lang="ts">
import { W<PERSON><PERSON>on, <PERSON><PERSON><PERSON><PERSON>, WTitle } from '@alice-health/wonderland-vue'
import SalesFirmAutocomplete from './SalesFirmAutocomplete.vue'
import type { SalesFirm } from '../models/SalesFirm'
import { computed, ref, toRefs, watch, type Ref } from 'vue'
import { salesAgentSchema } from '../schemas/salesAgentSchema'

/**
 * Props
 */
interface SalesFirmSelected extends SalesFirm {
  uniqueId: string
}

interface SalesFirmsSelectorProps {
  modelValue: SalesFirm[]
}

const props = defineProps<SalesFirmsSelectorProps>()
const { modelValue } = toRefs(props)

interface SalesFirmsSelectorEvents {
  (e: 'update:modelValue', salesFirms: SalesFirm[]): void
  (e: 'change', salesFirms: SalesFirm[]): void
}
const emit = defineEmits<SalesFirmsSelectorEvents>()

/**
 * Refs
 */
const salesFirmsSelected: Ref<SalesFirmSelected[]> = ref(
  modelValue.value.map((salesFirm) => ({ ...salesFirm, uniqueId: crypto.randomUUID() }))
)

const errorText = ref('')

/**
 * Computed
 */
const disableAddButton = computed(() => {
  return salesFirmsSelected.value.some((item) => !item.id)
})

const hasOneItem = computed(() => salesFirmsSelected.value.length === 1)

/**
 * Methods
 */
function updateValue(items: SalesFirmSelected[]) {
  salesFirmsSelected.value = items
  emit('update:modelValue', salesFirmsSelected.value)
}

function handleSalesFirmSelected(index: number, salesFirm?: SalesFirm) {
  const items = [...salesFirmsSelected.value]

  if (!salesFirm) {
    items[index] = { id: '', name: '', uniqueId: items[index].uniqueId }
  } else {
    items[index] = { ...salesFirm, uniqueId: items[index].uniqueId }
  }

  updateValue(items)

  validate(items)
}

function handleSalesFirmAdd() {
  const items = [...salesFirmsSelected.value]
  items.push({ uniqueId: crypto.randomUUID(), id: '', name: '' })

  updateValue(items)

  validate(items)
}

function handleSalesFirmRemove(index: number) {
  const items = [...salesFirmsSelected.value]
  items.splice(index, 1)

  updateValue(items)

  validate(items)
}

function validate(salesFirms: SalesFirmSelected[]) {
  const validationResult = salesAgentSchema.shape.salesFirms.safeParse(salesFirms)

  if (!validationResult.success) {
    const error = validationResult.error.errors.find((error) =>
      error.path.includes('customSalesFirms')
    )
    errorText.value = error?.message || ''
  } else {
    errorText.value = ''
  }
}

watch(modelValue, (newValue) => {
  emit('change', newValue)
})
</script>

<style lang="scss" scoped>
.sales-firms-selector {
  margin: var(--gl-spacing-02) 0;
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-04);
  justify-content: space-between;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
