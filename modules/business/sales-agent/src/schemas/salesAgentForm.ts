import { boolean, object, string, enum as zEnum } from 'zod'

const baseSalesAgentForm = {
  name: string().min(1),
  documentNumber: string().min(1),
  email: string().email(),
  phoneNumber: string().min(1),
  birthDate: string().date().optional(),
  salesFirmId: string().min(1),
  isActiveInCampaign: boolean(),
  status: zEnum(['BLOCKED', 'ACTIVE', 'INACTIVE']),
  hasPortalAccess: boolean().optional()
}

export const SalesAgentForm = object({
  ...baseSalesAgentForm
})
