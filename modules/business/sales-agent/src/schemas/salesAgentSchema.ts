import { array, boolean, object, string, enum as zEnum } from 'zod'

const salesFirmSchema = object({
  id: string().uuid(),
  name: string()
})

export const salesAgentSchema = object({
  name: string().min(1),
  documentNumber: string().min(1),
  email: string().email(),
  phoneNumber: string().min(1),
  birthDate: string().date().optional(),
  salesFirms: array(salesFirmSchema)
    .min(1)
    .refine(
      (salesFirms) => {
        return salesFirms.every((firm) => firm.id !== '')
      },
      {
        message:
          'Corretora não selecionada. Por favor, selecione uma corretora ou remova o campo. É necessário informar pelo menos uma corretora.',
        path: ['customSalesFirms']
      }
    )
    .refine(
      (salesFirms) => {
        const ids = salesFirms.map((firm) => firm.id)
        const uniqueIds = new Set(ids)
        return ids.length === uniqueIds.size
      },
      {
        message: 'Corretora duplicada. Cada corretora deve ser selecionada apenas uma vez.',
        path: ['customSalesFirms']
      }
    ),
  status: zEnum(['BLOCKED', 'ACTIVE', 'INACTIVE']),
  isActiveInCampaign: boolean(),
  hasPortalAccess: boolean().optional()
})
