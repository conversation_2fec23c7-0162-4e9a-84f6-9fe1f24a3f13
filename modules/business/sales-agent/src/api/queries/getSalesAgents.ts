import { useQueryString } from '@alice-health/vue-hooks'

import type { PaginatedResponse } from '@commons/index'
import type { SalesAgent } from '@business-sales-agent/models/SalesAgent'
import http from '@http/index'

export type SalesAgentResponse = PaginatedResponse<SalesAgent[]>

export const getSalesAgents = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<SalesAgentResponse>(`/salesAgent${qs}`)

  return data
}
