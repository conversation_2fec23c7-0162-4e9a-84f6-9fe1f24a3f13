import { useQueryString } from '@alice-health/vue-hooks'

import type { SalesFirm } from '@business-sales-agent/models/SalesFirm'
import http from '@http/index'

export type SalesFirmResponse = SalesFirm[]

export const getSalesFirms = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<SalesFirm[]>(`/salesAgent/salesFirm${qs}`)

  return data
}
