import http from '@http/index'
import { updateSalesAgent } from '../updateSalesAgent'
import type { Mock } from 'vitest'
import type { SalesAgentPayload } from '@business-sales-agent/models'

vi.mock('@http/index')

describe('updateSalesAgent', () => {
  it('should request with correct params', async () => {
    ;(http.put as Mock).mockResolvedValue({})

    const payload = {
      id: '123',
      name: '<PERSON><PERSON>'
    } as unknown as SalesAgentPayload

    updateSalesAgent('123', payload)

    expect(http.put).toHaveBeenCalledWith('/salesAgent/123', payload)
  })
})
