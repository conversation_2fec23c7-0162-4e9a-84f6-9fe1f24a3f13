import http from '@http/index'
import { createSalesAgent } from '../createSalesAgent'
import type { Mock } from 'vitest'
import type { SalesAgentPayload } from '@business-sales-agent/models'

vi.mock('@http/index')

describe('createSalesAgent', () => {
  it('should request with correct params', async () => {
    ;(http.post as Mock).mockResolvedValue({})
    const payload = {
      id: '123',
      name: '<PERSON><PERSON>'
    } as unknown as SalesAgentPayload

    createSalesAgent(payload)

    expect(http.post).toHaveBeenCalledWith('/salesAgent', payload)
  })
})
