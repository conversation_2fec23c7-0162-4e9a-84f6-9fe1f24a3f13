import createSalesAgent from './mutations/createSalesAgent'
import updateSalesAgent from './mutations/updateSalesAgent'

import getSalesAgentById from './queries/getSalesAgentById'
import getSalesAgents from './queries/getSalesAgents'
import getSalesFirms from './queries/getSalesFirms'

const handlers = [
  getSalesFirms,
  createSalesAgent,
  updateSalesAgent,
  getSalesAgentById,
  getSalesAgents
]

export {
  handlers,
  createSalesAgent,
  updateSalesAgent,
  getSalesAgentById,
  getSalesAgents,
  getSalesFirms
}
