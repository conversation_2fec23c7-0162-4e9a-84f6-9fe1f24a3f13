import { HttpResponse, http } from 'msw'

import type { SalesFirm } from '@business-sales-agent/models/SalesFirm'

const getSalesFirms = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesAgent/salesFirm`,
  () => {
    const salesFirms: SalesFirm[] = [
      { name: 'Coopers Firm', id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00' },
      { name: '<PERSON><PERSON> Firm', id: 'b0bb6bd7-e52e-479f-99c1-d546f9c79b01' },
      { name: 'Johns Firm', id: 'c0bb6bd7-e52e-479f-99c1-d546f9c79b02' }
    ]

    return HttpResponse.json(salesFirms)
  }
)

export default getSalesFirms
