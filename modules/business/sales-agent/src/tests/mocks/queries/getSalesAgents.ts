import { HttpResponse, http } from 'msw'

import type { SalesAgent } from '@business-sales-agent/models/SalesAgent'

const getSalesAgents = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesAgent`,
  () => {
    const salesAgents: SalesAgent[] = [
      {
        id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
        name: '<PERSON>',
        email: '<EMAIL>',
        phoneNumber: '***********',
        documentNumber: '***********',
        birthDate: '1990-02-01',
        salesFirms: [
          { name: '<PERSON><PERSON>rm', id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00' },
          { name: '<PERSON><PERSON>', id: 'b0bb6bd7-e52e-479f-99c1-d546f9c79b01' },
          { name: '<PERSON>rm', id: 'c0bb6bd7-e52e-479f-99c1-d546f9c79b02' }
        ],
        isActiveInCampaign: true,
        hasPortalAccess: true,
        totalLeadsFromCampaign: 10,
        leadsFromCampaignRound: 5,
        status: 'ACTIVE',
        createdAt: new Date(2000, 1, 1).toISOString(),
        updatedAt: new Date(2001, 1, 1).toISOString()
      },
      {
        id: 'b0bb6bd7-e52e-479f-99c1-d546f9c79b01',
        name: 'Jane Sellers',
        email: '<EMAIL>',
        phoneNumber: '11988887778',
        documentNumber: '12345678901',
        birthDate: '1992-02-15',
        salesFirms: [{ name: 'Janes Firm', id: 'b0bb6bd7-e52e-479f-99c1-d546f9c79b01' }],
        isActiveInCampaign: false,
        hasPortalAccess: true,
        totalLeadsFromCampaign: 0,
        leadsFromCampaignRound: 0,
        status: 'ACTIVE',
        createdAt: new Date(2000, 1, 1).toISOString(),
        updatedAt: new Date(2001, 1, 1).toISOString()
      },
      {
        id: 'c0bb6bd7-e52e-479f-99c1-d546f9c79b02',
        name: 'John Sellers',
        email: '<EMAIL>',
        phoneNumber: '11988887779',
        documentNumber: '12345678902',
        isActiveInCampaign: true,
        hasPortalAccess: true,
        totalLeadsFromCampaign: 1,
        leadsFromCampaignRound: 1,
        status: 'ACTIVE',
        birthDate: '1994-06-01',
        salesFirms: [{ name: 'Johns Firm', id: 'c0bb6bd7-e52e-479f-99c1-d546f9c79b02' }],
        createdAt: new Date(2000, 1, 1).toISOString(),
        updatedAt: new Date(2001, 1, 1).toISOString()
      }
    ]

    return HttpResponse.json({
      pagination: {
        totalPages: 1,
        pageSize: 10
      },
      results: salesAgents
    })
  }
)

export default getSalesAgents
