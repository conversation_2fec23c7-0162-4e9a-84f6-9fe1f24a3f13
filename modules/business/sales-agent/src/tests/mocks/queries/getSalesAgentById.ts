import { HttpResponse, http } from 'msw'

import type { SalesAgent } from '@business-sales-agent/models/SalesAgent'

const getSalesAgentById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesAgent/:id`,
  ({ params }) => {
    const id = params.id as string

    const salesAgent: SalesAgent = {
      id,
      name: '<PERSON> Sellers',
      email: '<EMAIL>',
      phoneNumber: '***********',
      documentNumber: '***********',
      birthDate: '1990-02-01',
      salesFirms: [
        { name: '<PERSON><PERSON> Firm', id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00' },
        { name: '<PERSON><PERSON>', id: 'b0bb6bd7-e52e-479f-99c1-d546f9c79b01' },
        { name: '<PERSON> Firm', id: 'c0bb6bd7-e52e-479f-99c1-d546f9c79b02' }
      ],
      isActiveInCampaign: true,
      hasPortalAccess: true,
      totalLeadsFromCampaign: 10,
      leadsFromCampaignRound: 5,
      status: 'ACTIVE',
      createdAt: new Date(2000, 1, 1).toISOString(),
      updatedAt: new Date(2001, 1, 1).toISOString()
    }

    return HttpResponse.json(salesAgent)
  }
)

export default getSalesAgentById
