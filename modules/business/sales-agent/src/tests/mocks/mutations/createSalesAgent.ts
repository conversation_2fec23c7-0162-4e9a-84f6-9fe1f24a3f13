import { HttpResponse, http } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesAgent`

const duplicatedEmailErrorHandler = () =>
  new HttpResponse(JSON.stringify({ code: 'email_duplicated_error' }), { status: 400 })

const duplicatedPhoneErrorHander = () =>
  new HttpResponse(JSON.stringify({ code: 'phone_number_duplicated_error' }), { status: 400 })

const duplicatedEmailAndPhoneErrorHandler = () =>
  new HttpResponse(JSON.stringify({ code: 'email_and_phone_number_duplicated_error' }), {
    status: 400
  })

const createSalesAgent = http.post(endpoint, () => HttpResponse.json())

export const createSalesAgentWithDuplicatedEmail = http.post(endpoint, duplicatedEmailErrorHandler)

export const createSalesAgentWithDuplicatedPhone = http.post(endpoint, duplicatedPhoneErrorHander)

export const createSalesAgentWithDuplicatedEmailAndPhone = http.post(
  endpoint,
  duplicatedEmailAndPhoneErrorHandler
)

export default createSalesAgent
