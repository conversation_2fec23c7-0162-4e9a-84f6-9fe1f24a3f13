import { HttpResponse, http } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesAgent/:id`

const duplicatedEmailErrorHandler = () =>
  new HttpResponse(JSON.stringify({ code: 'email_duplicated_error' }), { status: 400 })

const duplicatedPhoneErrorHander = () =>
  new HttpResponse(JSON.stringify({ code: 'phone_number_duplicated_error' }), { status: 400 })

const duplicatedEmailAndPhoneErrorHandler = () =>
  new HttpResponse(JSON.stringify({ code: 'email_and_phone_number_duplicated_error' }), {
    status: 400
  })

const updateSalesAgent = http.put(endpoint, () => HttpResponse.json({}))

export const updateSalesAgentWithDuplicatedEmail = http.put(endpoint, duplicatedEmailErrorHandler)

export const updateSalesAgentWithDuplicatedPhone = http.put(endpoint, duplicatedPhoneErrorHander)

export const updateSalesAgentWithDuplicatedEmailAndPhone = http.put(
  endpoint,
  duplicatedEmailAndPhoneErrorHandler
)

export default updateSalesAgent
