import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const salesAgentRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/business/sales-agent',
      beforeEnter,
      name: 'business/sales-agent',
      component: async () => await import('@business-sales-agent/views/ListView.vue')
    },
    {
      path: '/business/sales-agent/create',
      beforeEnter,
      name: 'business/sales-agent-create',
      component: async () => await import('@business-sales-agent/views/CreateView.vue')
    },
    {
      path: '/business/sales-agent/:id',
      beforeEnter,
      name: 'business/sales-agent-edit',
      component: async () => await import('@business-sales-agent/views/EditView.vue')
    }
  ]
}

export default salesAgentRoutes
