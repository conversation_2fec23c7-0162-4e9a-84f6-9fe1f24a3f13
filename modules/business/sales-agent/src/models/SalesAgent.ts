import type { salesAgentSchema } from '@business-sales-agent/schemas/salesAgentSchema'
import type { SalesFirm } from './SalesFirm'
import type { z } from 'zod'

export type SalesAgentForm = z.infer<typeof salesAgentSchema>

export type SalesAgent = Omit<SalesAgentForm, 'salesFirmIds'> & {
  id: string
  salesFirms: SalesFirm[]
  totalLeadsFromCampaign: number
  leadsFromCampaignRound: number
  createdAt: string
  updatedAt: string
}

export type SalesAgentPayload = Omit<SalesAgentForm, 'salesFirms'> & { salesFirmIds: string[] }
