<template>
  <ListLayout title="Corretores">
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="goToCreateSalesAgent">Criar</WButton>
    </template>

    <template #list>
      <div class="filters">
        <WAutocomplete
          :value="salesFirm"
          placeholder="Nome da corretora"
          :items="salesFirms || []"
          search-by-key="name"
          :loading="isSalesFirmsRefetching"
          @WChange="handleSalesFirmChanged"
          @WSelect="handleSalesFirmSelected"
        />

        <WListControllers
          has-pagination
          margin="none"
          has-items-per-page-select
          input-placeholder="Busca pelo nome ou email"
          :hide-input="false"
          :value="searchTerm"
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WInputChange="searchTermChange"
          @WPaginationPrevious="setPrev"
        >
          <div slot="select-items-per-page">
            <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
              <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
            </WSelect>
          </div>
        </WListControllers>
      </div>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>

        <WTableBody slot="body">
          <WTableRow v-for="item in salesAgents" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.name" size="medium">
              {{ item.name }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.documentNumber" size="medium">
              {{ item.documentNumber }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.email" size="medium">
              {{ item.email }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.phoneNumber" size="medium">
              {{ item.phoneNumber }}
            </WTableBodyCell>

            <WTableBodyCell :width="columnWidthMap.salesFirm" size="medium">
              {{ getSalesFirmsName(item.salesFirms) }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="goToEditSalesAgent(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import {
  WAutocomplete,
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect
} from '@alice-health/wonderland-vue'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'

import { ListLayout } from '@commons/index'
import { getSalesAgents, getSalesFirms } from '../api/queries'
import { SALES_AGENT_FIRMS, SALES_AGENTS } from '../constants/queryKeys'
import type { SalesFirm } from '../models/SalesFirm'

/**
 * Constants
 */
const limits = [5, 10, 15]

const columnWidthMap = {
  name: '20%',
  documentNumber: '15%',
  email: '20%',
  phoneNumber: '15%',
  salesFirm: '20%',
  edit: '10%'
}

const headers = [
  { id: 1, title: 'Nome', width: columnWidthMap.name },
  { id: 2, title: 'Número do documento', width: columnWidthMap.documentNumber },
  { id: 3, title: 'E-mail', width: columnWidthMap.email },
  { id: 4, title: 'Telefone', width: columnWidthMap.phoneNumber },
  { id: 5, title: 'Corretora', width: columnWidthMap.salesFirm },
  { id: 6, width: columnWidthMap.edit }
]

/**
 * Refs
 */
const searchTerm = ref('')
const salesFirmSearchTerm = ref('')
const salesFirm: Ref<SalesFirm | undefined> = ref()

/**
 * Computed Variables
 */
const getSalesFirmFilter = computed(() => ({
  filter: salesFirmSearchTermDebounced.value
    ? objectToJsonString({ q: salesFirmSearchTermDebounced.value })
    : ''
}))

const getSalesAgentsFilter = computed(() => {
  const filterObject = {
    ...(searchTermDebounced.value ? { q: searchTermDebounced.value } : {}),
    ...(salesFirm.value?.id ? { salesFirmId: salesFirm.value.id } : {})
  }

  return objectToJsonString(filterObject)
})

const getSalesAgentsParams = computed(() => ({
  filter: getSalesAgentsFilter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()

const { objectToJsonString } = useQueryString()

const searchTermDebounced = useDebounce(searchTerm, 800)
const salesFirmSearchTermDebounced = useDebounce(salesFirmSearchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data: salesFirms, isRefetching: isSalesFirmsRefetching } = useQuery({
  queryKey: [...SALES_AGENT_FIRMS, getSalesFirmFilter],
  queryFn: () => getSalesFirms(getSalesFirmFilter.value)
})

const { data: salesAgents } = useQuery({
  queryKey: [...SALES_AGENTS, getSalesAgentsParams],
  queryFn: () => getSalesAgents(getSalesAgentsParams.value),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages
    return results
  }
})

/**
 * Functions
 */
function goToCreateSalesAgent() {
  router.push({ name: 'business/sales-agent-create' })
}

function goToEditSalesAgent(id: string) {
  router.push({
    name: 'business/sales-agent-edit',
    params: { id }
  })
}

function getSalesFirmsName(salesFirms: SalesFirm[]) {
  return salesFirms.map((salesFirm) => salesFirm.name).join(', ')
}

function searchTermChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function updateRouteQuery() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function handleSalesFirmChanged(event: WAutocompleteCustomEvent<string>) {
  salesFirmSearchTerm.value = event.detail

  if (!event.detail) {
    handleSalesFirmSelected(event)
  }
}

function handleSalesFirmSelected(event: CustomEvent) {
  salesFirm.value = event.detail
}

/**
 * Watchers
 */
watch(searchTermDebounced, updateRouteQuery)
</script>

<style scoped lang="scss">
.filters {
  display: grid;
  grid-template-columns: auto auto;
  gap: var(--gl-spacing-04);
}
</style>
