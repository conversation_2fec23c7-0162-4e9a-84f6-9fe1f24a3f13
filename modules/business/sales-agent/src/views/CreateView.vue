<template>
  <FormLayout title="Criar corretor">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'business/sales-agent' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>

    <template #form>
      <WTextfield
        label="Nome"
        v-model="form.name"
        @WBlur="validate('name')"
        :invalid="hasError('name')"
        :errorText="getErrors('name')"
      />
      <WTextfield
        label="CPF"
        v-model="form.documentNumber"
        maskType="cpf"
        @WBlur="validate('documentNumber')"
        :invalid="hasError('documentNumber')"
        :errorText="getErrors('documentNumber')"
      />
      <WDatepicker
        data-testid="birthdate-datepicker"
        ref="datepicker"
        label="Data de nascimento"
        dateFormat="d/m/Y"
        :maxDate="minBirthDate"
        @w-change="handleBirthDateChanged"
        :invalid="hasError('birthDate')"
        :errorText="getErrors('birthDate')"
      />
      <WTextfield
        label="E-mail"
        v-model="form.email"
        @WBlur="validate('email')"
        :invalid="emailInvalid || hasError('email')"
        :errorText="emailErrorText || getErrors('email')"
      />
      <WTextfield
        label="Telefone"
        v-model="form.phoneNumber"
        maskType="cel"
        @WBlur="validate('phoneNumber')"
        :invalid="phoneNumberInvalid || hasError('phoneNumber')"
        :errorText="phoneNumberErrorText || getErrors('phoneNumber')"
      />
      <SalesFirmsSelector v-model="form.salesFirms" @change="validate('salesFirms')" />
      <WSwitch v-model="form.isActiveInCampaign" label="Corretor Elite" />
      <WSwitch v-model="form.hasPortalAccess" label="Acesso ao portal do corretor" />
      <div class="switch-container">
        <WSwitch v-model="isActive" label="Ativo" />
        <WTooltip position="right" label="Se o(a) corretor(a) está destivado(a)">
          <WIcon icon="icHelp" />
        </WTooltip>
      </div>
    </template>

    <template #actions>
      <WButton
        variant="cta"
        size="large"
        @click="validateAndCreate"
        :loading="createIsPending"
        :disabled="invalid"
      >
        Criar
      </WButton>
    </template>
  </FormLayout>
</template>

<script setup lang="ts">
import { computed, inject, ref, type Ref } from 'vue'
import { useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { WTextfield, WButton, WLink, WDatepicker, WSwitch, WTooltip, WIcon } from '@alice-health/wonderland-vue'
import type { WDatepickerCustomEvent } from '@alice-health/wonderland'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'
import type { SalesAgentForm, SalesAgentPayload } from '../models/SalesAgent'
import { salesAgentSchema } from '../schemas/salesAgentSchema'
import { createSalesAgent } from '../api/mutations'
import { SALES_AGENTS } from '../constants/queryKeys'
import type { SalesFirm } from '../models/SalesFirm'
import SalesFirmsSelector from '@business-sales-agent/components/SalesFirmsSelector.vue'
import type { ApiResponseErrorCode } from '@business-sales-agent/constants/apiResponseErrorCodes'
import type { AxiosError } from 'axios'

/*
 * Injections
 */

const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<SalesAgentForm> = ref({
  name: '',
  documentNumber: '',
  email: '',
  phoneNumber: '',
  birthDate: '',
  salesFirmId: '',
  salesFirms: [{ id: '', name: '' }] as SalesFirm[],
  status: 'INACTIVE',
  isActiveInCampaign: false,
  hasPortalAccess: false
})

const datepicker = ref()

const updateError = ref<ApiResponseErrorCode>()

/**
 * Computed
 */
const minBirthDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 18)
  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  return `${day}/${month}/${year}`
})

const isActive = computed({
  get() {
    return form.value.status === 'ACTIVE'
  },
  set(value: boolean) {
    form.value.status = value ? 'ACTIVE' : 'INACTIVE'
  }
})

const emailInvalid = computed(
  () =>
    updateError.value === 'email_duplicated_error' ||
    updateError.value === 'email_and_phone_number_duplicated_error'
)

const emailErrorText = computed(() =>
  emailInvalid.value ? 'Email inválido. O e-mail informado já está em uso' : ''
)

const phoneNumberInvalid = computed(
  () =>
    updateError.value === 'phone_number_duplicated_error' ||
    updateError.value === 'email_and_phone_number_duplicated_error'
)

const phoneNumberErrorText = computed(() =>
  phoneNumberInvalid.value ? 'Número inválido. O número informado já está em uso' : ''
)

/**
 * Hooks
 */
const router = useRouter()

const queryClient = useQueryClient()

const { mutate: create, isPending: createIsPending } = useMutation({
  mutationFn: (payload: SalesAgentPayload) => createSalesAgent(payload),
  onSuccess,
  onError
})

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: salesAgentSchema,
  form
})

/**
 * Functions
 */
async function handleBirthDateChanged(
  event: WDatepickerCustomEvent<{
    dates: string[]
  }>
) {
  const date = await formatDateField(new Date(event.detail?.dates[0]))
  form.value.birthDate = date
}

async function formatDateField(date: Date) {
  const parsedDate = await datepicker?.value?.$el.parse(date, 'Y-m-d')
  const formattedDate = parsedDate.toISOString().split('T')[0]
  return formattedDate
}

function validateAndCreate() {
  const schema = validateAll()
  updateError.value = undefined

  if (schema.success)
    create({
      name: form.value.name,
      documentNumber: form.value.documentNumber,
      email: form.value.email,
      phoneNumber: form.value.phoneNumber,
      birthDate: form.value.birthDate,
      salesFirmIds: form.value.salesFirms.map((salesFirm) => salesFirm.id),
      status: form.value.status,
      isActiveInCampaign: form.value.isActiveInCampaign,
      hasPortalAccess: form.value.hasPortalAccess
    })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: SALES_AGENTS })
  router.push({ name: 'business/sales-agent' })

  snackbar?.value?.$el.add({
    message: `Corretor cadastrado com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onError(error: AxiosError) {
  if (error.response) {
    const { code } = error.response.data as { code: ApiResponseErrorCode }
    updateError.value = code
  }

  snackbar?.value?.$el.add({
    message: `Erro ao cadastrar o corretor`,
    icon: 'icAlertCircleOutlined'
  })
}
</script>

<style scoped lang="scss">
.switch-container {
  display: flex;
  gap: 0 var(--gl-spacing-02);
  align-items: center;
}
</style>
