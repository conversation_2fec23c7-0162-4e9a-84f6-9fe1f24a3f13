import EditView from '@business-sales-agent/views/EditView.vue'
import { customRender, screen } from '@business-sales-agent/tests'
import { server } from '@commons/services/mockServer'
import { getSalesAgentById, getSalesAgents, getSalesFirms } from '@business-sales-agent/tests/mocks'
import { updateSalesAgentWithDuplicatedEmailAndPhone } from '@business-sales-agent/tests/mocks/mutations/updateSalesAgent'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => {
    server.listen()

    const mockDate = new Date(2022, 7, 20)
    vi.setSystemTime(mockDate)
  })
  afterAll(() => {
    server.close()

    vi.useRealTimers()
  })

  beforeEach(() => {
    server.use(getSalesAgents, getSalesFirms, getSalesAgentById)

    vi.clearAllMocks()
  })

  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('heading', {
        name: 'Editar corretor'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('textbox', { name: 'Nome' })).toHaveValue('Cooper Sellers')
    expect(screen.getByRole('textbox', { name: 'CPF' })).toHaveValue('123.456.789-00')
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toHaveValue('<EMAIL>')
    expect(screen.getByRole('textbox', { name: 'Telefone' })).toHaveValue('(11) 98888-7777')
    expect(screen.getByRole('textbox', { name: 'Data de nascimento' })).toBeInTheDocument()
    expect(screen.getByRole('checkbox', { name: 'Corretor Elite' })).toBeChecked()
    expect(screen.getByRole('checkbox', { name: 'Acesso ao portal do corretor' })).toBeChecked()
    expect(screen.getAllByPlaceholderText('Busque pelo nome da corretora')).toHaveLength(3)
    expect(screen.getByRole('checkbox', { name: 'Ativo' })).toBeInTheDocument()
    expect(screen.getByRole('checkbox', { name: 'Bloqueado' })).toBeInTheDocument()
    expect(
      screen.getByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })

  it('should allow blocking and (in)activating user', async () => {
    const { user } = customRender(EditView)

    await user.click(await screen.findByText('Ativo'))
    await user.click(await screen.findByText('Bloqueado'))

    expect(await screen.findByRole('checkbox', { name: 'Bloqueado' })).toBeChecked()
    expect(await screen.findByRole('checkbox', { name: 'Ativo' })).not.toBeChecked()
  })

  it('shows an error when updating to an existing email or phone', async () => {
    server.use(updateSalesAgentWithDuplicatedEmailAndPhone)

    const { user } = customRender(EditView)

    const saveButton = await screen.findByRole('button', {
      name: 'Salvar'
    })

    await user.click(saveButton)

    expect(
      await screen.findByText('Email inválido. O e-mail informado já está em uso')
    ).toBeInTheDocument()

    expect(
      await screen.findByText('Número inválido. O número informado já está em uso')
    ).toBeInTheDocument()
  })
})
