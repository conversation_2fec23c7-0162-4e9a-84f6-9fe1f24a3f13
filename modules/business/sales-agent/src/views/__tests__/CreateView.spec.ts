import CreateView from '@business-sales-agent/views/CreateView.vue'
import { customRender, user, screen, waitFor, fireEvent } from '@business-sales-agent/tests'
import { server } from '@commons/services/mockServer'
import { createSalesAgent, getSalesFirms } from '@business-sales-agent/tests/mocks'
import { createSalesAgentWithDuplicatedEmailAndPhone } from '@business-sales-agent/tests/mocks/mutations/createSalesAgent'

describe('CreateView', () => {
  beforeAll(() => {
    server.listen()

    const mockDate = new Date(2022, 7, 20)
    vi.setSystemTime(mockDate)
  })
  afterAll(() => {
    server.close()

    vi.useRealTimers()
  })

  beforeEach(() => {
    server.use(createSalesAgent, getSalesFirms)

    vi.clearAllMocks()
  })

  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Criar corretor'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('textbox', { name: 'Nome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'CPF' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Telefone' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Data de nascimento' })).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Busque pelo nome da corretora'))
    expect(screen.getByRole('switch', { name: 'Ativo' })).toBeInTheDocument()

    expect(
      screen.getByRole('button', {
        name: /adicionar/i
      })
    ).toBeInTheDocument()

    expect(
      screen.getByRole('button', {
        name: 'Criar'
      })
    ).toBeDisabled()
  })

  it('disable create button after fill from', async () => {
    customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Criar corretor'
      })
    ).toBeInTheDocument()

    await user.click(screen.getByTestId('birthdate-datepicker'))
    await waitFor(async () => expect(await screen.findByText('15')).toBeInTheDocument())
    await user.click(screen.getByText('15'))

    await user.type(screen.getByRole('textbox', { name: '' }), 'Coopers Firm')
    await user.click(screen.getByText('Coopers Firm'))

    await user.type(screen.getByRole('textbox', { name: 'Nome' }), 'Maria')
    await user.type(screen.getByRole('textbox', { name: 'CPF' }), '987654321')
    await user.type(screen.getByRole('textbox', { name: 'E-mail' }), '<EMAIL>')
    await user.type(screen.getByRole('textbox', { name: 'Telefone' }), '11999999999')
    await user.click(screen.getByRole('button', { name: 'Criar' }))

    await waitFor(() =>
      expect(
        screen.getByRole('button', {
          name: 'Criar'
        })
      ).toBeEnabled()
    )
  })

  it('shows an error when creating with an existing email or phone', async () => {
    server.use(createSalesAgentWithDuplicatedEmailAndPhone)

    const { user } = customRender(CreateView)

    const datePicker = await screen.findByTestId('birthdate-datepicker')
    await user.click(datePicker)
    await user.click(await screen.findByText('15'))

    const nameField = screen.getByRole('textbox', { name: 'Nome' })
    const documentField = screen.getByRole('textbox', { name: 'CPF' })
    const emailField = screen.getByRole('textbox', { name: 'E-mail' })
    const phoneField = screen.getByRole('textbox', { name: 'Telefone' })
    const activeSwitch = screen.getByRole('checkbox', { name: 'Ativo' })

    await user.type(screen.getByRole('textbox', { name: '' }), 'Coopers Firm')
    await user.click(screen.getByText('Coopers Firm'))

    await user.type(nameField, 'Maria')
    await user.type(documentField, '987654321')
    await user.type(emailField, '<EMAIL>')
    await user.type(phoneField, '11999999999')

    await user.click(activeSwitch)

    /**
     * Trigger validation
     */

    await fireEvent.blur(nameField)
    await fireEvent.blur(documentField)
    await fireEvent.blur(emailField)
    await fireEvent.blur(phoneField)

    const createButton = await screen.findByRole('button', {
      name: 'Criar'
    })

    await user.click(createButton)

    expect(
      await screen.findByText('Email inválido. O e-mail informado já está em uso')
    ).toBeInTheDocument()

    expect(
      await screen.findByText('Número inválido. O número informado já está em uso')
    ).toBeInTheDocument()
  })
})
