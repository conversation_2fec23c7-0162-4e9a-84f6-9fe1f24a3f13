import ListView from '@business-sales-agent/views/ListView.vue'
import { customRender, screen } from '@business-sales-agent/tests'
import { server } from '@commons/services/mockServer'
import { getSalesFirms, getSalesAgents } from '@business-sales-agent/tests/mocks'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getSalesFirms, getSalesAgents))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: '<PERSON>ria<PERSON>'
      })
    ).toBeInTheDocument()

    expect(screen.getByText('Nome')).toBeInTheDocument()
    expect(screen.getByText('Número do documento')).toBeInTheDocument()
    expect(screen.getByText('E-mail')).toBeInTheDocument()
    expect(screen.getByText('Telefone')).toBeInTheDocument()
    expect(screen.getByText('Corretora')).toBeInTheDocument()

    expect(screen.getByText('Cooper Sellers')).toBeInTheDocument()
    expect(screen.getByText('***********')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('11988887777')).toBeInTheDocument()
    expect(screen.getByText('Coopers Firm, Janes Firm, Johns Firm')).toBeInTheDocument()
  })
})
