<template>
  <div v-if="!isLoading">
    <FormLayout title="Editar corretor">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'business/sales-agent' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>

      <template #form>
        <WTextfield label="ID" :value="data?.id" disabled />
        <WTextfield
          label="Nome"
          v-model="form.name"
          @WBlur="validate('name')"
          :invalid="hasError('name')"
          :errorText="getErrors('name')"
        />
        <WTextfield
          label="CPF"
          v-model="form.documentNumber"
          maskType="cpf"
          @WBlur="validate('documentNumber')"
          :invalid="hasError('documentNumber')"
          :errorText="getErrors('documentNumber')"
        />
        <WTextfield
          label="E-mail"
          v-model="form.email"
          @WBlur="validate('email')"
          :invalid="emailInvalid || hasError('email')"
          :errorText="emailErrorText || getErrors('email')"
        />
        <WTextfield
          label="Telefone"
          mask-pattern="(00) 90000-0000"
          v-model="form.phoneNumber"
          maskType="cel"
          @WBlur="validate('phoneNumber')"
          :invalid="phoneNumberInvalid || hasError('phoneNumber')"
          :errorText="phoneNumberErrorText || getErrors('phoneNumber')"
        />
        <WDatepicker
          ref="datepicker"
          label="Data de nascimento"
          dateFormat="d/m/Y"
          :maxDate="minBirthDate"
          :value="formatToLocateDateString(form.birthDate)"
          @w-change="handleBirthDateChanged"
          :invalid="hasError('birthDate')"
          :errorText="getErrors('birthDate')"
        />
        <SalesFirmsSelector v-model="form.salesFirms" @change="validate('salesFirms')" />
        <WSwitch v-model="form.isActiveInCampaign" label="Corretor Elite" />
        <WSwitch v-model="form.hasPortalAccess" label="Acesso ao portal do corretor" />
        <div class="switch-container">
          <WSwitch v-model="isActive" label="Ativo" />
          <WTooltip position="right" label="Se o(a) corretor(a) está destivado(a)">
            <WIcon icon="icHelp" />
          </WTooltip>
        </div>
        <div class="switch-container">
          <WSwitch v-model="isBlocked" label="Bloqueado" />
          <WTooltip
            position="right"
            label="Se o(a) corretor(a) está bloqueado(a) para vendas (ex:. cometeu alguma fraude)"
          >
            <WIcon icon="icHelp" />
          </WTooltip>
        </div>
        <WTextfield
          label="Total de leads de campanha"
          :value="String(data?.totalLeadsFromCampaign)"
          disabled
        />
        <WTextfield
          label="Total de lead do ciclo atual da campanha"
          :value="String(data?.leadsFromCampaignRound)"
          disabled
        />
        <WTextfield
          label="Data de criação"
          :value="formatToLocateDateString(data?.createdAt)"
          disabled
        />
        <WTextfield
          label="Última atualização"
          :value="formatToLocateDateString(data?.updatedAt)"
          disabled
        />
      </template>

      <template #actions>
        <WButton
          variant="cta"
          size="large"
          @click="validateAndSave"
          :loading="isUpdatePending"
          :disabled="invalid"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watchEffect, type Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import {
  WTextfield,
  WButton,
  WLink,
  WDatepicker,
  WSwitch,
  WIcon,
  WTooltip
} from '@alice-health/wonderland-vue'
import type { WDatepickerCustomEvent } from '@alice-health/wonderland'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'
import type { SalesFirm, SalesAgentForm, SalesAgentPayload } from '../models'
import { salesAgentSchema } from '../schemas/salesAgentSchema'
import { getSalesAgentById } from '../api/queries'
import { updateSalesAgent } from '../api/mutations'
import { SALES_AGENT } from '../constants/queryKeys'
import { SALES_AGENTS } from '../constants/queryKeys'
import SalesFirmsSelector from '@business-sales-agent/components/SalesFirmsSelector.vue'
import type { ApiResponseErrorCode } from '@business-sales-agent/constants/apiResponseErrorCodes'
import type { AxiosError } from 'axios'

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<SalesAgentForm> = ref({
  name: '',
  documentNumber: '',
  email: '',
  phoneNumber: '',
  birthDate: '',
  salesFirmId: '',
  salesFirms: [] as SalesFirm[],
  status: 'INACTIVE',
  isActiveInCampaign: false,
  hasPortalAccess: false
})

const updateError = ref<ApiResponseErrorCode>()

const datepicker = ref()

/**
 * Computed
 */

const id = computed(() => route.params.id as string)

const isBlocked = computed({
  get() {
    return form.value.status === 'BLOCKED'
  },
  set(value: boolean) {
    form.value.status = value ? 'BLOCKED' : 'ACTIVE'
  }
})

const isActive = computed({
  get() {
    return form.value.status === 'ACTIVE'
  },
  set(value: boolean) {
    form.value.status = value ? 'ACTIVE' : 'INACTIVE'
  }
})

const emailInvalid = computed(
  () =>
    updateError.value === 'email_duplicated_error' ||
    updateError.value === 'email_and_phone_number_duplicated_error'
)

const emailErrorText = computed(() =>
  emailInvalid.value ? 'Email inválido. O e-mail informado já está em uso' : ''
)

const phoneNumberInvalid = computed(
  () =>
    updateError.value === 'phone_number_duplicated_error' ||
    updateError.value === 'email_and_phone_number_duplicated_error'
)

const phoneNumberErrorText = computed(() =>
  phoneNumberInvalid.value ? 'Número inválido. O número informado já está em uso' : ''
)

const minBirthDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 18)
  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  return `${day}/${month}/${year}`
})

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()

const queryClient = useQueryClient()

const { data, isLoading } = useQuery({
  queryKey: [...SALES_AGENT, id.value],
  queryFn: () => getSalesAgentById(id.value),
  select: (data) => ({
    ...data,
    documentNumber: data.documentNumber.replace(/\D/g, ''),
    phoneNumber: data.phoneNumber.replace(/\D/g, '')
  })
})

const { mutate: update, isPending: isUpdatePending } = useMutation({
  mutationFn: ({ id, payload }: { id: string; payload: SalesAgentPayload }) =>
    updateSalesAgent(id, payload),
  onSuccess: () => onSuccess('atualizado'),
  onError: (error: AxiosError) => {
    if (error.response) {
      const { code } = error.response.data as { code: ApiResponseErrorCode }
      updateError.value = code
    }

    onError('atualizar')
  }
})

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: salesAgentSchema,
  form,
  invalidInitialValue: false
})

/**
 * Functions
 */
async function handleBirthDateChanged(
  event: WDatepickerCustomEvent<{
    dates: string[]
  }>
) {
  const date = await formatDateField(new Date(event.detail?.dates[0]))
  form.value.birthDate = date
}

function formatToLocateDateString(date: string | undefined) {
  if (!date) return ''
  return new Date(date).toLocaleDateString('pt-br', { dateStyle: 'short' })
}

async function formatDateField(date: Date) {
  const parsedDate = await datepicker?.value?.$el.parse(date, 'Y-m-d')
  const formattedDate = parsedDate.toISOString().split('T')[0]
  return formattedDate
}

function validateAndSave() {
  const schema = validateAll()
  updateError.value = undefined

  if (schema.success)
    update({
      id: id.value,
      payload: {
        name: form.value.name,
        documentNumber: form.value.documentNumber,
        email: form.value.email,
        phoneNumber: form.value.phoneNumber,
        birthDate: form.value.birthDate,
        salesFirmIds: form.value.salesFirms.map((salesFirm) => salesFirm.id),
        status: form.value.status,
        isActiveInCampaign: form.value.isActiveInCampaign,
        hasPortalAccess: form.value.hasPortalAccess
      }
    })
}

async function onSuccess(action: string) {
  await Promise.all([
    queryClient.invalidateQueries({ queryKey: SALES_AGENTS }),
    queryClient.invalidateQueries({ queryKey: [...SALES_AGENT, id.value] })
  ])
  router.push({ name: 'business/sales-agent' })

  snackbar?.value?.$el.add({
    message: `Corretor ${action} com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} o corretor`,
    icon: 'icAlertCircleOutlined'
  })
}

watchEffect(() => {
  if (data.value) {
    form.value = {
      name: data.value.name,
      documentNumber: data.value.documentNumber,
      email: data.value.email,
      phoneNumber: data.value.phoneNumber,
      birthDate: data.value.birthDate,
      salesFirms: data.value.salesFirms,
      status: data.value.status,
      isActiveInCampaign: data.value.isActiveInCampaign,
      hasPortalAccess: data.value.hasPortalAccess
    }
  }
})
</script>

<style scoped lang="scss">
.switch-container {
  display: flex;
  gap: 0 var(--gl-spacing-02);
  align-items: center;
}
</style>
