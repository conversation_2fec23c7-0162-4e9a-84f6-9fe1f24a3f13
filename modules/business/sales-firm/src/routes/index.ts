import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const salesFirmRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/business/sales-firm',
      name: 'business/sales-firm',
      beforeEnter,
      component: async () => await import('@business-sales-firm/views/ListView.vue')
    },
    {
      path: '/business/sales-firm/create',
      name: 'business/sales-firm-create',
      beforeEnter,
      component: async () => await import('@business-sales-firm/views/CreateView.vue')
    },
    {
      path: '/business/sales-firm/:id',
      name: 'business/sales-firm-edit',
      beforeEnter,
      component: async () => await import('@business-sales-firm/views/EditView.vue')
    }
  ]
}

export default salesFirmRoutes
