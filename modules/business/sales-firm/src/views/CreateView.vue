<template>
  <FormLayout title="Criar corretora">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'business/sales-firm' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>

    <template #form>
      <WTextfield
        label="Nome"
        v-model="form.name"
        @WBlur="validate('name')"
        :invalid="hasError('name')"
        :errorText="getErrors('name')"
      />
      <WTextfield
        label="Nome legal"
        v-model="form.legalName"
        @WBlur="validate('legalName')"
        :invalid="hasError('legalName')"
        :errorText="getErrors('legalName')"
      />
      <WTextfield
        label="CNPJ"
        mask-type="cnpj"
        v-model="form.cnpj"
        @WBlur="validate('cnpj')"
        :invalid="hasError('cnpj')"
        :errorText="getErrors('cnpj')"
      />
      <WTextfield
        label="E-mail"
        v-model="form.email"
        @WBlur="validate('email')"
        :invalid="hasError('email')"
        :errorText="getErrors('email')"
      />
      <WTextfield
        label="Telefone"
        mask-pattern="(00) 90000-0000"
        v-model="form.phoneNumber"
        @WBlur="validate('phoneNumber')"
        :invalid="hasError('phoneNumber')"
        :errorText="getErrors('phoneNumber')"
      />
    </template>

    <template #actions>
      <WButton
        variant="cta"
        size="large"
        @click="validateAndCreate"
        :loading="createIsPending"
        :disabled="invalid"
      >
        Criar
      </WButton>
    </template>
  </FormLayout>
</template>

<script setup lang="ts">
import { inject, ref, type Ref } from 'vue'
import { useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { WTextfield, WButton, WLink } from '@alice-health/wonderland-vue'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'
import type { SalesFirm } from '../models/SalesFirm'
import { SalesFirmForm } from '../schemas/salesFirmForm'
import { createSalesFirm } from '../api/mutations'
import { SALES_FIRMS } from '../constants/queryKeys'

/**
 * Custom types
 */
type Form = Omit<SalesFirm, 'id' | 'createdAt' | 'updatedAt'>

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<Form> = ref({
  name: '',
  legalName: '',
  cnpj: '',
  email: '',
  phoneNumber: ''
})

/**
 * Hooks
 */
const router = useRouter()

const queryClient = useQueryClient()

const { mutate: create, isPending: createIsPending } = useMutation({
  mutationFn: () => createSalesFirm(form.value),
  onSuccess,
  onError
})

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: SalesFirmForm,
  form
})

/**
 * Functions
 */

function validateAndCreate() {
  const schema = validateAll()

  if (schema.success) create()
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: SALES_FIRMS })
  router.push({ name: 'business/sales-firm' })

  snackbar?.value?.$el.add({
    message: `Corretora cadastrada com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onError() {
  snackbar?.value?.$el.add({
    message: `Erro ao cadastrar a corretora`,
    icon: 'icAlertCircleOutlined'
  })
}
</script>
