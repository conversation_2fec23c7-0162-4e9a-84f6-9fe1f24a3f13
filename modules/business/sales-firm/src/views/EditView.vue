<template>
  <div v-if="!isLoading">
    <FormLayout title="Editar corretora">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'business/sales-firm' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>

      <template #form>
        <WTextfield label="ID" :value="data?.id" disabled />
        <WTextfield
          label="Nome"
          v-model="form.name"
          @WBlur="validate('name')"
          :invalid="hasError('name')"
          :errorText="getErrors('name')"
        />
        <WTextfield
          label="Nome legal"
          v-model="form.legalName"
          @WBlur="validate('legalName')"
          :invalid="hasError('legalName')"
          :errorText="getErrors('legalName')"
        />
        <WTextfield
          label="CNPJ"
          v-model="form.cnpj"
          mask-type="cnpj"
          @WBlur="validate('cnpj')"
          :invalid="hasError('cnpj')"
          :errorText="getErrors('cnpj')"
        />
        <WTextfield
          label="E-mail"
          v-model="form.email"
          @WBlur="validate('email')"
          :invalid="hasError('email')"
          :errorText="getErrors('email')"
        />
        <WTextfield
          label="Telefone"
          v-model="form.phoneNumber"
          mask-pattern="(00) 90000-0000"
          @WBlur="validate('phoneNumber')"
          :invalid="hasError('phoneNumber')"
          :errorText="getErrors('phoneNumber')"
        />
        <WTextfield
          label="Data de criação"
          :value="formatToLocateDateString(data?.createdAt)"
          disabled
        />
        <WTextfield
          label="Última atualização"
          :value="formatToLocateDateString(data?.updatedAt)"
          disabled
        />
      </template>

      <template #actions>
        <WButton
          variant="cta"
          size="large"
          :disabled="invalid"
          @click="validateAndSave"
          :loading="isUpdatePending"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, type Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { WTextfield, WButton, WLink } from '@alice-health/wonderland-vue'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'
import type { SalesFirm } from '../models/SalesFirm'
import { SalesFirmForm } from '../schemas/salesFirmForm'
import { getSalesFirmById } from '../api/queries'
import { updateSalesFirm } from '../api/mutations'
import { SALES_FIRM } from '../constants/queryKeys'
import { SALES_FIRMS } from '../constants/queryKeys'

/**
 * Custom types
 */
type Form = Omit<SalesFirm, 'id' | 'createdAt' | 'updatedAt'>

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<Form> = ref({
  name: '',
  legalName: '',
  cnpj: '',
  email: '',
  phoneNumber: ''
})

/**
 * Computed Variables
 */
const id = computed(() => route.params.id as string)

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()

const queryClient = useQueryClient()

const { data, isLoading } = useQuery({
  queryKey: [...SALES_FIRM, id.value],
  queryFn: () => getSalesFirmById(id.value),
  select: (data) => (form.value = data)
})

const { mutate: update, isPending: isUpdatePending } = useMutation({
  mutationFn: () => updateSalesFirm(id.value, form.value),
  onSuccess: () => onSuccess('atualizada'),
  onError: () => onError('atualizar')
})

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: SalesFirmForm,
  form,
  invalidInitialValue: false
})

/**
 * Functions
 */
function formatToLocateDateString(date: string | undefined) {
  if (!date) return ''
  return new Date(date).toLocaleDateString('pt-br', { dateStyle: 'short' })
}

function validateAndSave() {
  const schema = validateAll()

  if (schema.success) update()
}

async function onSuccess(action: string) {
  await Promise.all([
    queryClient.invalidateQueries({ queryKey: SALES_FIRMS }),
    queryClient.invalidateQueries({ queryKey: [...SALES_FIRM, id.value] })
  ])
  router.push({ name: 'business/sales-firm' })

  snackbar?.value?.$el.add({
    message: `Corretora ${action} com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} a corretora`,
    icon: 'icAlertCircleOutlined'
  })
}
</script>
