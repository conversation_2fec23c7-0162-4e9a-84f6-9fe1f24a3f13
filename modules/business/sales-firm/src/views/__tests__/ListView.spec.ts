import ListView from '@business-sales-firm/views/ListView.vue'
import { customRender, screen } from '@business-sales-firm/tests'
import { server } from '@commons/services/mockServer'
import { getSalesFirms } from '@business-sales-firm/tests/mocks'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getSalesFirms))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByText('Nome')).toBeInTheDocument()
    expect(screen.getByText('Nome legal')).toBeInTheDocument()
    expect(screen.getByText('CNPJ')).toBeInTheDocument()
    expect(screen.getByText('E-mail')).toBeInTheDocument()
    expect(screen.getByText('Telefone')).toBeInTheDocument()

    expect(screen.getByText('Coopers Firm')).toBeInTheDocument()
    expect(screen.getByText('Coopers Firm LTDA')).toBeInTheDocument()
    expect(screen.getByText('***********')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('11988887777')).toBeInTheDocument()
  })
})
