import CreateView from '@business-sales-firm/views/CreateView.vue'
import { customRender, user, screen, waitFor } from '@business-sales-firm/tests'
import { server } from '@commons/services/mockServer'
import { createSalesFirm } from '@business-sales-firm/tests/mocks'

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(createSalesFirm))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: '<PERSON>riar corretora'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('textbox', { name: 'Nome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Nome legal' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'CNPJ' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Telefone' })).toBeInTheDocument()

    expect(
      screen.getByRole('button', {
        name: 'Criar'
      })
    ).toBeDisabled()
  })

  it('disable create button after fill from', async () => {
    customRender(CreateView)

    await user.type(await screen.findByRole('textbox', { name: 'Nome' }), 'Maria')
    await user.type(screen.getByRole('textbox', { name: 'Nome legal' }), 'Maria LDTA')
    await user.type(screen.getByRole('textbox', { name: 'CNPJ' }), '123456777')
    await user.type(screen.getByRole('textbox', { name: 'E-mail' }), '<EMAIL>')
    await user.type(screen.getByRole('textbox', { name: 'Telefone' }), '11999999999')

    await waitFor(
      () => {
        screen.getByRole('button', {
          name: 'Criar'
        })
      },
      { timeout: 1000 }
    )
  })
})
