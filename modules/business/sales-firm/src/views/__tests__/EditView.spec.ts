import EditView from '@business-sales-firm/views/EditView.vue'
import { customRender, screen } from '@business-sales-firm/tests'
import { server } from '@commons/services/mockServer'
import { getSalesFirmById } from '@business-sales-firm/tests/mocks'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getSalesFirmById))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('heading', {
        name: 'Editar corretora'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('textbox', { name: 'ID' })).toBeDisabled()
    expect(screen.getByRole('textbox', { name: 'Nome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Nome legal' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'CNPJ' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Telefone' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Data de criação' })).toBeDisabled()
    expect(screen.getByRole('textbox', { name: 'Última atualização' })).toBeDisabled()

    expect(
      screen.getByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })
})
