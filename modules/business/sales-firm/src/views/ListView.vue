<template>
  <ListLayout title="Corretoras">
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="goToCreateSalesFirm">Criar</WButton>
    </template>

    <template #list>
      <WListControllers
        has-pagination
        margin="none"
        has-items-per-page-select
        input-placeholder="Busca pelo nome ou CNPJ"
        :hide-input="false"
        :value="searchTerm"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WPaginationNext="setNext"
        @WInputChange="searchTermChange"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
          </WSelect>
        </div>
      </WListControllers>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>

        <WTableBody slot="body">
          <WTableRow v-for="item in salesFirms" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.name" size="medium">
              {{ item.name }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.legalName" size="medium">
              {{ item.legalName }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.cnpj" size="medium">
              {{ item.cnpj }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.email" size="medium">
              {{ item.email }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.phoneNumber" size="medium">
              {{ item.phoneNumber }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="goToEditSalesFirm(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import {
  WAutocomplete,
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect
} from '@alice-health/wonderland-vue'

import { ListLayout } from '@commons/index'
import { getSalesFirms } from '../api/queries'
import { SALES_FIRMS } from '../constants/queryKeys'

/**
 * Constants
 */
const limits = [5, 10, 15]

const columnWidthMap = {
  name: '20%',
  legalName: '20%',
  cnpj: '15%',
  email: '20%',
  phoneNumber: '15%',
  edit: '10%'
}

const headers = [
  { id: 1, title: 'Nome', width: columnWidthMap.name },
  { id: 2, title: 'Nome legal', width: columnWidthMap.legalName },
  { id: 3, title: 'CNPJ', width: columnWidthMap.cnpj },
  { id: 4, title: 'E-mail', width: columnWidthMap.email },
  { id: 5, title: 'Telefone', width: columnWidthMap.phoneNumber },
  { id: 6, width: columnWidthMap.edit }
]

/**
 * Refs
 */
const searchTerm = ref('')

/**
 * Computed Variables
 */
const getSalesFirmsFilter = computed(() => {
  const filterObject = {
    ...(searchTermDebounced.value ? { q: searchTermDebounced.value } : {})
  }

  return objectToJsonString(filterObject)
})

const getSalesFirmsParams = computed(() => ({
  filter: getSalesFirmsFilter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()

const { objectToJsonString } = useQueryString()

const searchTermDebounced = useDebounce(searchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data: salesFirms } = useQuery({
  queryKey: [...SALES_FIRMS, getSalesFirmsParams],
  queryFn: () => getSalesFirms(getSalesFirmsParams.value),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages
    return results
  }
})

/**
 * Functions
 */
function goToCreateSalesFirm() {
  router.push({ name: 'business/sales-firm-create' })
}

function goToEditSalesFirm(id: string) {
  router.push({
    name: 'business/sales-firm-edit',
    params: { id }
  })
}

function searchTermChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function updateRouteQuery() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

/**
 * Watchers
 */
watch(searchTermDebounced, updateRouteQuery)
</script>
