import { HttpResponse, http } from 'msw'

import type { SalesFirm } from '@business-sales-firm/models/SalesFirm'

const getSalesFirmById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesFirm/:id`,
  ({ params }) => {
    const id = params.id as string

    const salesFirm: SalesFirm = {
      id,
      name: 'Coopers Firm',
      legalName: 'Coopers Firm LTDA',
      cnpj: '***********',
      email: '<EMAIL>',
      phoneNumber: '***********',
      createdAt: new Date(2000, 1, 1).toISOString(),
      updatedAt: new Date(2001, 1, 1).toISOString()
    }
    return HttpResponse.json(salesFirm)
  }
)

export default getSalesFirmById
