import { HttpResponse, http } from 'msw'

import type { SalesFirm } from '@business-sales-firm/models/SalesFirm'

const getSalesFirms = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesFirm`,
  () => {
    const salesFirms: SalesFirm[] = [
      {
        id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
        name: '<PERSON><PERSON> Firm',
        legalName: 'Coopers Firm LTDA',
        cnpj: '***********',
        email: '<EMAIL>',
        phoneNumber: '***********',
        createdAt: new Date(2000, 1, 1).toISOString(),
        updatedAt: new Date(2001, 1, 1).toISOString()
      },
      {
        id: 'b0bb6bd7-e52e-479f-99c1-d546f9c79b01',
        name: '<PERSON><PERSON>',
        legalName: '<PERSON><PERSON>rm LTDA',
        cnpj: '***********',
        email: '<EMAIL>',
        phoneNumber: '***********',
        createdAt: new Date(2000, 1, 1).toISOString(),
        updatedAt: new Date(2001, 1, 1).toISOString()
      },
      {
        id: 'c0bb6bd7-e52e-479f-99c1-d546f9c79b02',
        name: 'Johns Firm',
        legalName: 'Johns Firm LTDA',
        cnpj: '12345678902',
        email: '<EMAIL>',
        phoneNumber: '11988887779',
        createdAt: new Date(2000, 1, 1).toISOString(),
        updatedAt: new Date(2001, 1, 1).toISOString()
      }
    ]

    return HttpResponse.json({
      pagination: {
        totalPages: 1,
        pageSize: 10
      },
      results: salesFirms
    })
  }
)

export default getSalesFirms
