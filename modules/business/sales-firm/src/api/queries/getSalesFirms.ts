import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'

import type { SalesFirm } from '@business-sales-firm/models'
import type { PaginatedResponse } from '@commons/index'

export type SalesFirmResponse = PaginatedResponse<SalesFirm[]>

export const getSalesFirms = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<SalesFirmResponse>(`/salesFirm${qs}`)

  return data
}
