import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const salesFirmStaffRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/business/sales-firm-staff',
      beforeEnter,
      name: 'business/sales-firm-staff',
      component: async () => await import('@business-sales-firm-staff/views/ListView.vue')
    },
    {
      path: '/business/sales-firm-staff/create',
      beforeEnter,
      name: 'business/sales-firm-staff-create',
      component: async () => await import('@business-sales-firm-staff/views/CreateView.vue')
    },
    {
      path: '/business/sales-firm-staff/:id',
      beforeEnter,
      name: 'business/sales-firm-staff-edit',
      component: async () => await import('@business-sales-firm-staff/views/EditView.vue')
    }
  ]
}

export default salesFirmStaffRoutes
