import { object, string, enum as zodEnum } from 'zod'

export const STATUS = ['ACTIVE', 'INACTIVE'] as const
export const StatusEnum = zodEnum(STATUS)

const baseFields = {
  firstName: string().min(1),
  lastName: string().min(1),
  email: string().email(),
  status: StatusEnum
}

export const SalesFirmStaffCreateForm = object({
  ...baseFields,
  salesFirmId: string()
})

export const SalesFirmStaffEditForm = object({
  ...baseFields
})

export const SalesFirmStaff = object({
  ...baseFields,
  id: string(),
  salesFirmId: string(),
  salesFirmName: string(),
  createdAt: string().optional().default(''),
  updatedAt: string().optional().default('')
})
