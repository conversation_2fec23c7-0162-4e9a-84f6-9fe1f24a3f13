import type { SalesFirmStaffsResponse } from '@business-sales-firm-staff/api/queries'
import type { SalesFirmStaff, SalesFirmStaffList } from '@business-sales-firm-staff/models'

export const salesFirmStaff: SalesFirmStaff = {
  id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
  firstName: '<PERSON>',
  lastName: ' <PERSON>',
  email: 'micha<PERSON><PERSON>@test.com',
  salesFirmName: 'Dunder Mifflin',
  salesFirmId: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
  status: 'ACTIVE',
  createdAt: new Date(2000, 1, 1).toISOString(),
  updatedAt: new Date(2001, 1, 1).toISOString()
}

export const salesFirmStaffList: SalesFirmStaffList[] = [
  {
    id: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
    name: '<PERSON>',
    email: 'mi<PERSON><PERSON><PERSON>@dundermifflin.com',
    salesFirmName: 'Dunder-Mifflin',
    status: 'ACTIVE'
  },
  {
    id: 'b0bb6bd7-e52e-479f-99c1-d546f9c79b01',
    name: 'Dwight Schrute',
    email: '<EMAIL>',
    salesFirmName: 'Dunder-Mifflin',
    status: 'ACTIVE'
  },
  {
    id: 'c0bb6bd7-e52e-479f-99c1-d546f9c79b02',
    name: 'Jim Halpert',
    email: '<EMAIL>',
    salesFirmName: 'Dunder-Mifflin',
    status: 'ACTIVE'
  },
  {
    id: 'd0bb6bd7-e52e-479f-99c1-d546f9c79b03',
    name: 'Pam Beesly',
    email: '<EMAIL>',
    salesFirmName: 'Dunder-Mifflin',
    status: 'ACTIVE'
  },
  {
    id: 'e0bb6bd7-e52e-479f-99c1-d546f9c79b04',
    name: 'Robert California',
    email: '<EMAIL>',
    salesFirmName: 'Dunder-Mifflin',
    status: 'INACTIVE'
  }
]

export const salesFirmStaffListResponse: SalesFirmStaffsResponse = {
  pagination: {
    totalPages: 1,
    pageSize: 10
  },
  results: salesFirmStaffList
}
