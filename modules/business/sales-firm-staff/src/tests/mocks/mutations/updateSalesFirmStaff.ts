import { HttpResponse, http } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesFirmStaff/:id`

const updateSalesFirmStaff = http.put(endpoint, () => HttpResponse.json())

const duplicatedEmailErrorHandler = () =>
  new HttpResponse(JSON.stringify({ code: 'email_duplicated_error' }), { status: 400 })

export const updateSalesFirmStaffWithDuplicatedEmail = http.put(
  endpoint,
  duplicatedEmailErrorHandler
)

export default updateSalesFirmStaff
