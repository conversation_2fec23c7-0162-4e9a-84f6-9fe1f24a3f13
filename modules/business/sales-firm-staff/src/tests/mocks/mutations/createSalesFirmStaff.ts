import { HttpResponse, http } from 'msw'

const endpoint = `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/salesFirmStaff`

const createSalesFirmStaff = http.post(endpoint, () => HttpResponse.json())

const duplicatedEmailErrorHandler = () =>
  new HttpResponse(JSON.stringify({ code: 'email_duplicated_error' }), { status: 400 })

export const createSalesFirmStaffWithDuplicatedEmail = http.post(
  endpoint,
  duplicatedEmailErrorHandler
)

export default createSalesFirmStaff
