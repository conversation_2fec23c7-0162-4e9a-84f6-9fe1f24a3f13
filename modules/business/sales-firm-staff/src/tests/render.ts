import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'

export * from '@alice-health/unit-presets/src/testing-library'
import user from '@testing-library/user-event'

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  const routerInstance = createRouter({
    history: createWebHistory('/'),
    routes: [
      { path: '/', name: 'home', component: async () => ({}) },
      { path: '/sales-firm-staff', name: 'business/sales-firm-staff', component: async () => ({}) },
      {
        path: '/sales-firm-staff/create',
        name: 'business/sales-firm-staff-create',
        component: async () => ({})
      },
      {
        path: '/sales-firm-staff/:id',
        name: 'business/sales-firm-staff-edit',
        component: async () => ({})
      }
    ]
  })

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}

export { user }
