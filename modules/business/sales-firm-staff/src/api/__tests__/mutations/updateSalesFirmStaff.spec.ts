import type { Mock } from 'vitest'

import http from '@http/index'
import { salesFirmStaff } from '@business-sales-firm-staff/tests/mocks/models'
import { updateSalesFirmStaff } from '@business-sales-firm-staff/api/mutations'
import type { SalesFirmStaffEdit } from '@business-sales-firm-staff/models'

vi.mock('@http/index')

describe('API function: updateSalesFirmStaff', () => {
  it('return sales firm staff', async () => {
    const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'
    const request: SalesFirmStaffEdit = {
      firstName: 'Michael',
      lastName: 'Scott',
      email: 'micha<PERSON><PERSON>@dundermifflin.com',
      status: 'ACTIVE'
    }
    const api = http.put as Mock
    api.mockImplementationOnce((url) => {
      return url === `/salesFirmStaff/${id}`
        ? Promise.resolve({ data: salesFirmStaff })
        : new Error('Invalid URL')
    })

    const data = await updateSalesFirmStaff(id, request)

    expect(data).toEqual(salesFirmStaff)
  })
})
