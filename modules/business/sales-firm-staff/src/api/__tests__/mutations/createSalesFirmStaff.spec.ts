import type { Mock } from 'vitest'

import http from '@http/index'
import { salesFirmStaff } from '@business-sales-firm-staff/tests/mocks/models'
import { createSalesFirmStaff } from '@business-sales-firm-staff/api/mutations'
import type { SalesFirmStaffCreate } from '@business-sales-firm-staff/models'

vi.mock('@http/index')

describe('API function: createSalesFirmStaff', () => {
  it('return sales firm staff', async () => {
    const request: SalesFirmStaffCreate = {
      firstName: 'Michael',
      lastName: '<PERSON>',
      email: 'micha<PERSON><PERSON>@dundermifflin.com',
      salesFirmId: 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00',
      status: 'ACTIVE'
    }
    const api = http.post as Mock
    api.mockImplementationOnce((url, data) =>
      url === '/salesFirmStaff' && data === request
        ? Promise.resolve({ data: salesFirmStaff })
        : new Error('Invalid URL')
    )

    const data = await createSalesFirmStaff(request)

    expect(data).toEqual(salesFirmStaff)
  })
})
