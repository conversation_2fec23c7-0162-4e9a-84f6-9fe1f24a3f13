import type { Mo<PERSON> } from 'vitest'

import { getSalesFirms } from '@business-sales-firm-staff/api/queries'
import http from '@http/index'
import { salesFirms } from '@business-sales-firm-staff/tests/mocks/models'

vi.mock('@http/index')

describe('API function: getSalesFirms', () => {
  it('return sales firms', async () => {
    const api = http.get as Mock
    api.mockImplementationOnce((url) =>
      url === '/salesFirmStaff/salesFirm?filter=%7B%7D'
        ? Promise.resolve({ data: salesFirms })
        : new Error('Invalid URL')
    )

    const data = await getSalesFirms({
      filter: '{}'
    })

    expect(data).toEqual(salesFirms)
  })

  it('return sales firms filtered by search term', async () => {
    const api = http.get as Mock
    api.mockImplementationOnce((url) =>
      url === '/salesFirmStaff/salesFirm?filter=%7B%22q%22%3A%22dun%22%7D'
        ? Promise.resolve({ data: salesFirms })
        : new Error('Invalid URL')
    )

    const data = await getSalesFirms({
      filter: '{"q":"dun"}'
    })

    expect(data).toEqual(salesFirms)
  })
})
