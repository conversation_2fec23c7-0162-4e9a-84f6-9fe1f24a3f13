import type { Mock } from 'vitest'

import { getSalesFirmStaffById } from '@business-sales-firm-staff/api/queries'
import http from '@http/index'
import { salesFirmStaff } from '@business-sales-firm-staff/tests/mocks/models'

vi.mock('@http/index')

describe('API function: getSalesFirmStaffById', () => {
  it('return sales firm staff by ID', async () => {
    const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'
    const api = http.get as Mock
    api.mockImplementationOnce((url) =>
      url === `/salesFirmStaff/${id}`
        ? Promise.resolve({ data: salesFirmStaff })
        : new Error('Invalid URL')
    )

    const data = await getSalesFirmStaffById(id)

    expect(data).toEqual(salesFirmStaff)
  })
})
