import type { Mock } from 'vitest'

import { getSalesFirmStaffs } from '@business-sales-firm-staff/api/queries'
import http from '@http/index'
import { salesFirmStaffListResponse } from '@business-sales-firm-staff/tests/mocks/models'

vi.mock('@http/index')

describe('API function: getSalesFirmStaffs', () => {
  it('return sales firm staffs', async () => {
    const api = http.get as Mock
    api.mockImplementationOnce((url) =>
      url === '/salesFirmStaff?filter=%7B%7D&page=1&pageSize=10'
        ? Promise.resolve({ data: salesFirmStaffListResponse })
        : new Error('Invalid URL')
    )

    const data = await getSalesFirmStaffs({
      filter: '{}',
      page: '1',
      pageSize: '10'
    })

    expect(data).toEqual(salesFirmStaffListResponse)
  })

  it('return sales firm staffs filtered by search term and sales firm id', async () => {
    const api = http.get as Mock
    api.mockImplementationOnce((url) =>
      url ===
      '/salesFirmStaff?filter=%7B%22q%22%3A%22coo%22%2C%22salesFirmId%22%3A%22a0bb6bd7-e52e-479f-99c1-d546f9c79b00%22%7D&page=1&pageSize=10'
        ? Promise.resolve({ data: salesFirmStaffListResponse })
        : new Error('Invalid URL')
    )

    const data = await getSalesFirmStaffs({
      filter: '{"q":"coo","salesFirmId":"a0bb6bd7-e52e-479f-99c1-d546f9c79b00"}',
      page: '1',
      pageSize: '10'
    })

    expect(data).toEqual(salesFirmStaffListResponse)
  })
})
