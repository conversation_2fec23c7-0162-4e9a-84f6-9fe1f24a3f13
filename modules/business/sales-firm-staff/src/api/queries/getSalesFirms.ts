import { useQueryString } from '@alice-health/vue-hooks'

import http from '@http/index'
import type { SalesFirm } from '../../models'

export type SalesFirmResponse = SalesFirm[]

export const getSalesFirms = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<SalesFirmResponse>(`/salesFirmStaff/salesFirm${qs}`)

  return data
}
