import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'

import type { SalesFirmStaffList } from '@business-sales-firm-staff/models'
import type { PaginatedResponse } from '@commons/index'

export type SalesFirmStaffsResponse = PaginatedResponse<SalesFirmStaffList[]>

export const getSalesFirmStaffs = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<SalesFirmStaffsResponse>(`/salesFirmStaff${qs}`)

  return data
}
