import http from '@http/index'
import type { SalesFirmStaff, SalesFirmStaffEdit } from '@business-sales-firm-staff/models'

export const updateSalesFirmStaff = async (id: string, form: SalesFirmStaffEdit) => {
  const { data } = await http.put<SalesFirmStaff>(`/salesFirmStaff/${id}`, {
    firstName: form.firstName,
    lastName: form.lastName,
    email: form.email,
    status: form.status
  })

  return data
}
