import EditView from '@business-sales-firm-staff/views/EditView.vue'
import { customRender, screen } from '@business-sales-firm-staff/tests'
import { server } from '@commons/services/mockServer'
import { getSalesFirmStaffById } from '@business-sales-firm-staff/tests/mocks'
import { updateSalesFirmStaffWithDuplicatedEmail } from '@business-sales-firm-staff/tests/mocks/mutations/updateSalesFirmStaff'

const id = 'a0bb6bd7-e52e-479f-99c1-d546f9c79b00'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getSalesFirmStaffById))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('heading', {
        name: 'Editar funcionário da corretora'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('textbox', { name: 'ID' })).toBeDisabled()
    expect(screen.getByRole('textbox', { name: 'Nome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Sobrenome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    screen.getByRole('checkbox', {
      name: /ativo/i
    })
    expect(screen.getByRole('textbox', { name: 'Data de criação' })).toBeDisabled()
    expect(screen.getByRole('textbox', { name: 'Última atualização' })).toBeDisabled()

    expect(
      screen.getByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })

  it('Shows an error when updating to an existing email or phone', async () => {
    server.use(updateSalesFirmStaffWithDuplicatedEmail)

    const { user } = customRender(EditView)

    const saveButton = await screen.findByRole('button', {
      name: 'Salvar'
    })

    await user.click(saveButton)

    expect(
      await screen.findByText('Email inválido. O e-mail informado já está em uso')
    ).toBeInTheDocument()
  })
})
