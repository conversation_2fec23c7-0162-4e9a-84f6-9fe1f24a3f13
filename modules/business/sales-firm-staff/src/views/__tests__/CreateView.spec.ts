import CreateView from '@business-sales-firm-staff/views/CreateView.vue'
import { customRender, fireEvent, screen, waitFor } from '@business-sales-firm-staff/tests'
import { server } from '@commons/services/mockServer'
import { createSalesFirmStaff, getSalesFirms } from '@business-sales-firm-staff/tests/mocks'
import { createSalesFirmStaffWithDuplicatedEmail } from '@business-sales-firm-staff/tests/mocks/mutations/createSalesFirmStaff'

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(createSalesFirmStaff, getSalesFirms))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Criar funcionário da corretora'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('combobox')).toHaveProperty('label', 'Corretora')
    expect(screen.getByRole('textbox', { name: 'Nome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Sobrenome' })).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'E-mail' })).toBeInTheDocument()
    expect(
      screen.getByRole('checkbox', {
        name: /ativo/i
      })
    ).toBeInTheDocument()

    expect(
      screen.getByRole('button', {
        name: 'Criar'
      })
    ).toBeDisabled()
  })

  it.skip('shows an error when creating with an existing email', async () => {
    server.use(createSalesFirmStaffWithDuplicatedEmail)

    const { user } = customRender(CreateView)

    const nameField = await screen.findByRole('textbox', { name: 'Nome' })
    const surnameField = await screen.findByRole('textbox', { name: 'Sobrenome' })
    const emailField = await screen.findByRole('textbox', { name: 'E-mail' })

    await user.type(await screen.findByRole('textbox', { name: '' }), 'Vance Refrigeration')
    await user.click(await screen.findByText('Vance Refrigeration'))

    await user.type(nameField, 'Bob')
    await user.type(surnameField, 'Vance')
    await user.type(emailField, '<EMAIL>')

    /**
     * Trigger validation
     */

    await fireEvent.blur(nameField)
    await fireEvent.blur(surnameField)
    await fireEvent.blur(emailField)

    const createButton = await screen.findByRole('button', {
      name: 'Criar'
    })

    await user.click(createButton)

    await waitFor(async () =>
      expect(
        await screen.findByText('Email inválido. O e-mail informado já está em uso')
      ).toBeInTheDocument()
    )
  })
})
