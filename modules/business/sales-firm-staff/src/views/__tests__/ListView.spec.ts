import ListView from '@business-sales-firm-staff/views/ListView.vue'
import { customRender, screen } from '@business-sales-firm-staff/tests'
import { server } from '@commons/services/mockServer'
import { getSalesFirms, getSalesFirmStaffs } from '@business-sales-firm-staff/tests/mocks'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  beforeEach(() => server.use(getSalesFirmStaffs, getSalesFirms))
  afterEach(() => server.resetHandlers())

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON>ria<PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByText('Nome')).toBeInTheDocument()
    expect(screen.getByText('Corretora')).toBeInTheDocument()
    expect(screen.getByText('E-mail')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()

    expect(
      await screen.findByRole('row', {
        name: /michael scott dunder-mifflin michaelscott@dundermifflin\.com icsuccess icedit/i
      })
    ).toBeInTheDocument()
  })
})
