<template>
  <div v-if="!isLoading">
    <FormLayout title="Editar funcionário da corretora">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'business/sales-firm-staff' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>

      <template #form>
        <WTextfield label="ID" :value="data?.id" disabled />

        <WTextfield
          label="Nome"
          v-model="form.firstName"
          @WBlur="validate('firstName')"
          :invalid="hasError('firstName')"
          :errorText="getErrors('firstName')"
        />

        <WTextfield
          label="Sobrenome"
          v-model="form.lastName"
          @WBlur="validate('lastName')"
          :invalid="hasError('lastName')"
          :errorText="getErrors('lastName')"
        />

        <WTextfield
          label="E-mail"
          v-model="form.email"
          @WBlur="validate('email')"
          :invalid="emailInvalid || hasError('email')"
          :errorText="emailErrorText || getErrors('email')"
        />

        <WSwitch v-model="status" label="Ativo" @w-change="handleStatusChange" />

        <WTextfield label="Corretora" :value="data?.salesFirmName" disabled />
        <WTextfield
          label="Data de criação"
          :value="formatToLocateDateString(data?.createdAt)"
          disabled
        />
        <WTextfield
          label="Última atualização"
          :value="formatToLocateDateString(data?.updatedAt)"
          disabled
        />
      </template>

      <template #actions>
        <WButton
          variant="cta"
          size="large"
          @click="validateAndSave"
          :loading="isUpdatePending"
          :disabled="invalid"
        >
          Salvar
        </WButton>
      </template>
    </FormLayout>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, type Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { WTextfield, WButton, WLink, WSwitch } from '@alice-health/wonderland-vue'
import type { WSwitchCustomEvent } from '@alice-health/wonderland'

import type { SnackbarComponentProps } from '@commons/index'
import { FormLayout } from '@commons/index'

import type { ApiResponseErrorCode } from '@business-sales-firm-staff/constants/apiResponseErrorCodes'
import { updateSalesFirmStaff } from '@business-sales-firm-staff/api/mutations'
import { getSalesFirmStaffById } from '@business-sales-firm-staff/api/queries'
import { SALES_FIRM_STAFF, SALES_FIRM_STAFFS } from '@business-sales-firm-staff/constants/queryKeys'
import type { SalesFirmStaffEdit } from '@business-sales-firm-staff/models'
import { SalesFirmStaffEditForm } from '@business-sales-firm-staff/schemas/salesFirmStaffForm'
import type { AxiosError } from 'axios'

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<SalesFirmStaffEdit> = ref({
  firstName: '',
  lastName: '',
  email: '',
  status: 'ACTIVE'
})

const status = ref(true)
const updateError = ref<ApiResponseErrorCode>()

/**
 * Computed Variables
 */
const id = computed(() => route.params.id as string)

const emailInvalid = computed(
  () =>
    updateError.value === 'email_duplicated_error' ||
    updateError.value === 'email_and_phone_number_duplicated_error'
)

const emailErrorText = computed(() =>
  emailInvalid.value ? 'Email inválido. O e-mail informado já está em uso' : ''
)

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()

const queryClient = useQueryClient()

const { data, isLoading } = useQuery({
  queryKey: [...SALES_FIRM_STAFF, id.value],
  queryFn: () => getSalesFirmStaffById(id.value),
  select: (data) => {
    form.value = {
      firstName: data?.firstName,
      lastName: data?.lastName,
      email: data?.email,
      status: data?.status
    }
    status.value = data?.status === 'ACTIVE'
    return data
  }
})

const { mutate: update, isPending: isUpdatePending } = useMutation({
  mutationFn: () => updateSalesFirmStaff(id.value, form.value),
  onSuccess: () => onSuccess('atualizado'),
  onError: (error: AxiosError) => {
    if (error.response) {
      const { code } = error.response.data as { code: ApiResponseErrorCode }
      updateError.value = code
    }

    onError('atualizar')
  }
})

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: SalesFirmStaffEditForm,
  form,
  invalidInitialValue: false
})

/**
 * Functions
 */
function formatToLocateDateString(date: string | undefined) {
  if (!date) return ''
  return new Date(date).toLocaleDateString('pt-br', { dateStyle: 'short' })
}

async function handleStatusChange(event: WSwitchCustomEvent<boolean>) {
  form.value.status = event.detail ? 'ACTIVE' : 'INACTIVE'
}

function validateAndSave() {
  const schema = validateAll()
  updateError.value = undefined

  if (schema.success) update()
}

async function onSuccess(action: string) {
  await Promise.all([
    queryClient.invalidateQueries({ queryKey: SALES_FIRM_STAFFS }),
    queryClient.invalidateQueries({ queryKey: [...SALES_FIRM_STAFF, id.value] })
  ])
  router.push({ name: 'business/sales-firm-staff' })

  snackbar?.value?.$el.add({
    message: `Funcionário da corretora ${action} com sucesso`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} o funcionário da corretora `,
    icon: 'icAlertCircleOutlined'
  })
}
</script>
