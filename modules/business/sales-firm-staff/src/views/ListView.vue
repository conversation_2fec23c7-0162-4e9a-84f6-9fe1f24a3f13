<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
  <ListLayout title="Funcionário da corretora">
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="goToCreateSalesFirmStaff"
        >Criar</WButton
      >
    </template>

    <template #list>
      <div class="filters">
        <WAutocomplete
          :value="salesFirm"
          placeholder="Nome da corretora"
          :items="salesFirms || []"
          search-by-key="name"
          :loading="isSalesFirmsRefetching"
          @WChange="handleSalesFirmChanged"
          @WSelect="handleSalesFirmSelected"
        />

        <WListControllers
          has-pagination
          margin="none"
          has-items-per-page-select
          input-placeholder="Busca pelo nome ou email"
          :hide-input="false"
          :value="searchTerm"
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WInputChange="searchTermChange"
          @WPaginationPrevious="setPrev"
        >
          <template v-slot:select-items-per-page>
            <div>
              <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
                <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
              </WSelect>
            </div>
          </template>
        </WListControllers>
      </div>

      <WTable slot="header">
        <WTableHeader>
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>

        <WTableBody slot="body">
          <WTableRow v-for="item in salesFirmStaffs" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.name" size="medium">
              {{ item.name }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.salesFirmName" size="medium">
              {{ item.salesFirmName }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.email" size="medium">
              {{ item.email }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.status" size="medium" align="center">
              <WIcon :icon="item.status === 'ACTIVE' ? 'icSuccess' : 'icClose'" />
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="goToEditSalesFirmStaff(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import {
  WAutocomplete,
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect,
  WIcon
} from '@alice-health/wonderland-vue'
import type { WAutocompleteCustomEvent } from '@alice-health/wonderland'

import { ListLayout } from '@commons/index'
import { SALES_FIRM_STAFF_FIRMS, SALES_FIRM_STAFFS } from '../constants/queryKeys'
import { getSalesFirms, getSalesFirmStaffs } from '../api/queries'
import type { SalesFirm } from '@business-sales-firm-staff/models'

/**
 * Constants
 */
const limits = [5, 10, 15]

const columnWidthMap = {
  name: '30%',
  salesFirmName: '30%',
  email: '30%',
  status: '5%',
  edit: '5%'
}

const headers = [
  { id: 1, title: 'Nome', width: columnWidthMap.name },
  { id: 2, title: 'Corretora', width: columnWidthMap.salesFirmName },
  { id: 3, title: 'E-mail', width: columnWidthMap.email },
  { id: 4, title: 'Status', width: columnWidthMap.status },
  { id: 5, width: columnWidthMap.edit }
]

/**
 * Refs
 */
const searchTerm = ref('')
const salesFirmSearchTerm = ref('')
const salesFirm: Ref<SalesFirm | undefined> = ref()

/**
 * Computed Variables
 */
const getSalesFirmFilter = computed(() => {
  const filterObject = {
    ...(salesFirmSearchTermDebounced.value ? { q: salesFirmSearchTermDebounced.value } : {})
  }

  return objectToJsonString(filterObject)
})

const getSalesFirmParams = computed(() => ({
  filter: getSalesFirmFilter.value
}))

const getSalesFirmStaffFilter = computed(() => {
  const filterObject = {
    ...(searchTermDebounced.value ? { q: searchTermDebounced.value } : {}),
    ...(salesFirm.value?.id ? { salesFirmId: salesFirm.value.id } : {})
  }

  return objectToJsonString(filterObject)
})

const getSalesFirmStaffParams = computed(() => ({
  filter: getSalesFirmStaffFilter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()

const { objectToJsonString } = useQueryString()

const searchTermDebounced = useDebounce(searchTerm, 800)
const salesFirmSearchTermDebounced = useDebounce(salesFirmSearchTerm, 800)

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data: salesFirms, isRefetching: isSalesFirmsRefetching } = useQuery({
  queryKey: [...SALES_FIRM_STAFF_FIRMS, getSalesFirmParams],
  queryFn: () => getSalesFirms(getSalesFirmParams.value)
})

const { data: salesFirmStaffs } = useQuery({
  queryKey: [...SALES_FIRM_STAFFS, getSalesFirmStaffParams],
  queryFn: () => getSalesFirmStaffs(getSalesFirmStaffParams.value),
  select: ({ results, pagination }) => {
    totalPages.value = pagination.totalPages
    return results
  }
})

/**
 * Functions
 */
function goToCreateSalesFirmStaff() {
  router.push({ name: 'business/sales-firm-staff-create' })
}

function goToEditSalesFirmStaff(id: string) {
  router.push({
    name: 'business/sales-firm-staff-edit',
    params: { id }
  })
}

function searchTermChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function updateRouteQuery() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function handleSalesFirmChanged(event: WAutocompleteCustomEvent<string>) {
  salesFirmSearchTerm.value = event.detail

  if (!event.detail) {
    handleSalesFirmSelected(event)
  }
}

function handleSalesFirmSelected(event: CustomEvent) {
  salesFirm.value = event.detail
}

/**
 * Watchers
 */
watch(searchTermDebounced, updateRouteQuery)
</script>

<style scoped lang="scss">
.filters {
  display: grid;
  grid-template-columns: auto auto;
  gap: var(--gl-spacing-04);
}
</style>
