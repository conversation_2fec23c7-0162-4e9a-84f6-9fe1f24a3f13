<template>
  <FormLayout title="Criar funcionário da corretora">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'business/sales-firm-staff' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>

    <template #form>
      <WAutocomplete
        :value="salesFirm"
        label="Corretora"
        placeholder="Busque pelo nome da corretora"
        :items="salesFirms || []"
        search-by-key="name"
        disable-clearable
        :loading="isSalesFirmsRefetching"
        :invalid="hasError('salesFirmId')"
        @WChange="handleSalesFirmChanged"
        @WSelect="handleSalesFirmSelected"
      />
      <WTextfield
        label="Nome"
        v-model="form.firstName"
        @WBlur="validate('firstName')"
        :invalid="hasError('firstName')"
        :errorText="getErrors('firstName')"
      />
      <WTextfield
        label="Sobrenome"
        v-model="form.lastName"
        @WBlur="validate('lastName')"
        :invalid="hasError('lastName')"
        :errorText="getErrors('lastName')"
      />
      <WTextfield
        label="E-mail"
        v-model="form.email"
        @WBlur="validate('email')"
        :invalid="emailInvalid || hasError('email')"
        :errorText="emailErrorText || getErrors('email')"
      />
      <WSwitch v-model="status" label="Ativo" @w-change="handleStatusChange" />
    </template>

    <template #actions>
      <WButton
        variant="cta"
        size="large"
        @click="validateAndCreate"
        :loading="createIsPending"
        :disabled="invalid"
      >
        Criar
      </WButton>
    </template>
  </FormLayout>
</template>

<script setup lang="ts">
import { computed, inject, ref, type Ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDebounce, useQueryString, useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { WAutocomplete, WTextfield, WButton, WLink, WSwitch } from '@alice-health/wonderland-vue'
import type { WAutocompleteCustomEvent, WSwitchCustomEvent } from '@alice-health/wonderland'

import type { SnackbarComponentProps } from '@commons/index'

import { FormLayout } from '@commons/index'
import type { ApiResponseErrorCode } from '@business-sales-firm-staff/constants/apiResponseErrorCodes'
import { createSalesFirmStaff } from '@business-sales-firm-staff/api/mutations'
import { getSalesFirms } from '@business-sales-firm-staff/api/queries'
import {
  SALES_FIRM_STAFF_FIRMS,
  SALES_FIRM_STAFFS
} from '@business-sales-firm-staff/constants/queryKeys'
import type { SalesFirmStaffCreate, SalesFirm } from '@business-sales-firm-staff/models'
import { SalesFirmStaffCreateForm } from '@business-sales-firm-staff/schemas/salesFirmStaffForm'
import type { AxiosError } from 'axios'

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<SalesFirmStaffCreate> = ref({
  firstName: '',
  lastName: '',
  salesFirmId: '',
  email: '',
  status: 'ACTIVE'
})

const status = ref(true)
const salesFirmSearchTerm = ref('')
const salesFirm: Ref<SalesFirm | undefined> = ref()
const updateError = ref<ApiResponseErrorCode>()

/**
 * Computed Variables
 */
const getSalesFirmFilter = computed(() => {
  const filterObject = {
    ...(salesFirmSearchTermDebounced.value ? { q: salesFirmSearchTermDebounced.value } : {})
  }

  return objectToJsonString(filterObject)
})

const getSalesFirmParams = computed(() => ({
  filter: getSalesFirmFilter.value
}))

const emailInvalid = computed(() => updateError.value === 'email_duplicated_error')

const emailErrorText = computed(() =>
  emailInvalid.value ? 'Email inválido. O e-mail informado já está em uso' : ''
)

/**
 * Hooks
 */
const router = useRouter()

const { objectToJsonString } = useQueryString()

const queryClient = useQueryClient()

const salesFirmSearchTermDebounced = useDebounce(salesFirmSearchTerm, 800)

const { data: salesFirms, isRefetching: isSalesFirmsRefetching } = useQuery({
  queryKey: [...SALES_FIRM_STAFF_FIRMS, getSalesFirmParams],
  queryFn: () => getSalesFirms(getSalesFirmParams.value)
})

const { mutate: create, isPending: createIsPending } = useMutation({
  mutationFn: () => createSalesFirmStaff(form.value),
  onSuccess,
  onError
})

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: SalesFirmStaffCreateForm,
  form
})

/**
 * Functions
 */
function handleSalesFirmChanged(event: WAutocompleteCustomEvent<string>) {
  salesFirmSearchTerm.value = event.detail
}

function handleSalesFirmSelected(event: CustomEvent<SalesFirm>) {
  salesFirm.value = event.detail
  form.value.salesFirmId = event.detail.id
}

function handleStatusChange(event: WSwitchCustomEvent<boolean>) {
  form.value.status = event.detail ? 'ACTIVE' : 'INACTIVE'
}

function validateAndCreate() {
  const schema = validateAll()
  updateError.value = undefined

  if (schema.success) create()
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: SALES_FIRM_STAFFS })
  router.push({ name: 'business/sales-firm-staff' })

  snackbar?.value?.$el.add({
    message: 'Funcionário da corretora cadastrado com sucesso',
    icon: 'icAlertCircleOutlined'
  })
}

async function onError(error: AxiosError) {
  if (error.response) {
    const { code } = error.response.data as { code: ApiResponseErrorCode }
    updateError.value = code
  }

  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar funionário da corretora',
    icon: 'icAlertCircleOutlined'
  })
}
</script>
