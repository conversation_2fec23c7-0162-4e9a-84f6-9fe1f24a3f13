import { z } from 'zod'

import {
  SalesFirmStaff,
  SalesFirmStaffEditForm,
  STATUS,
  SalesFirmStaffCreateForm
} from '../schemas/salesFirmStaffForm'

export type Status = (typeof STATUS)[number]

export type SalesFirmStaffCreate = z.infer<typeof SalesFirmStaffCreateForm>

export type SalesFirmStaffEdit = z.infer<typeof SalesFirmStaffEditForm>

export type SalesFirmStaff = {
  id: string
  salesFirmId: string
  salesFirmName: string
  firstName: string
  lastName: string
  email: string
  status: Status
  createdAt: string
  updatedAt: string
}

export type SalesFirmStaffList = {
  id: string
  name: string
  email: string
  salesFirmName: string
  status: Status
}
