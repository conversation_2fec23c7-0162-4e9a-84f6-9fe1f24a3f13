import http from '@http/index'
import type { Staff } from '@company-staff/models'

export const updateStaff = async (id: string, form: Partial<Staff>) => {
  const { data } = await http.put<Staff>(`/companyStaff/${id}`, {
    firstName: form.firstName,
    lastName: form.lastName,
    email: form.email,
    companyId: form.companyId,
    role: form.role,
    accessLevel: form.accessLevel
  })

  return data
}
