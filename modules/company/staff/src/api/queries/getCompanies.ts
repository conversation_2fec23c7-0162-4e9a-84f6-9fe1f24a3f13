import { useQueryString } from '@alice-health/vue-hooks'
import type { PaginatedResponse } from '@commons/index'

import http from '@http/index'
import type { Company } from '@company-staff/models/Company'

export type getCompaniesResponse = PaginatedResponse<Company[]>

export const getCompanies = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getCompaniesResponse>(`/company${qs}`)

  return data
}
