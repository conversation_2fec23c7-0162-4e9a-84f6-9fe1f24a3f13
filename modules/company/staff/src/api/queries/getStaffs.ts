import type { PaginatedResponse } from '@commons/index'
import type { Staff } from '@company-staff/models'

import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'

export type getStaffsResponse = PaginatedResponse<Staff[]>

export const getStaffs = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getStaffsResponse>(`/companyStaff${qs}`)
  return data
}
