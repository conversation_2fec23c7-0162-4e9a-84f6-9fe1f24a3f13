<template>
  <ListLayout>
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="onCreate">Criar</WButton>
    </template>
    <template #list>
      <div class="list-filters">
        <WAutocomplete
          placeholder="Nome ou CNPJ da empresa"
          :loading="isRefetchingCompanies"
          @WChange="handleInputCompany"
          :items="companies"
          search-by-key="name"
          @WSelect="companySelected"
        ></WAutocomplete>
        <WListControllers
          has-pagination
          margin="none"
          has-items-per-page-select
          input-placeholder="Busca pelo nome ou email"
          :hide-input="false"
          :value="term"
          :total-pages="totalPages"
          :current-page="currentPage"
          :disable-next-button="disableNextButton"
          :disable-previous-button="disablePrevButton"
          @WPaginationNext="setNext"
          @WInputChange="searchChange"
          @WPaginationPrevious="setPrev"
        >
          <div slot="select-items-per-page">
            <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
              <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
            </WSelect>
          </div>
        </WListControllers>
      </div>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.name" size="medium">
              {{ item.firstName }} {{ item.lastName }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.company" size="medium">
              {{ item.company?.name }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.email" size="medium">
              {{ item.email }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.role" size="medium">
              {{ item.role }}
            </WTableBodyCell>

            <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="end">
              <div style="display: inline-block">
                <WButton
                  :block="false"
                  icon-button
                  variant="secondary"
                  icon="icEdit"
                  @click="editStaff(item.id)"
                />
              </div>
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>

<script setup lang="ts">
import {
  WAutocomplete,
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect
} from '@alice-health/wonderland-vue'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDebounce, usePagination, useQueryString } from '@alice-health/vue-hooks'
import { ListLayout } from '@commons/index'
import { getCompanies, getStaffs, type getStaffsResponse } from '@company-staff/api/queries'
import { useQuery } from '@tanstack/vue-query'
import type { Company } from '@company-staff/models'

/**
 * Constants
 */
const limits = [5, 10, 15]
const columnWidthMap = {
  name: '20%',
  company: '25%',
  email: '25%',
  role: '20%',
  edit: '10%'
}

const headers = [
  { id: 1, title: 'Name', width: columnWidthMap.name },
  { id: 2, title: 'Company', width: columnWidthMap.company },
  { id: 3, title: 'Email', width: columnWidthMap.email },
  { id: 4, title: 'Role', width: columnWidthMap.role },
  { id: 5, width: columnWidthMap.edit }
]

/**
 * Computed Variables
 */
const { objectToJsonString } = useQueryString()
const term = computed(() => route.query?.term?.toString() || '')

const filter = computed(() => {
  if (term.value) {
    if (companyId.value) {
      return objectToJsonString({ q: term.value, companyId: companyId.value })
    }

    return objectToJsonString({ q: term.value })
  } else if (companyId.value) {
    return objectToJsonString({ companyId: companyId.value })
  }
  return ''
})

const getParams = computed(() => ({
  filter: filter.value,
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()
const companyId = ref('')
const searchTerm = ref('')
const searchTermCompany = ref('')
const searchTermDebounced = useDebounce(searchTerm, 800)
const searchCompanyDebounced = useDebounce(searchTermCompany, 800)

const companyFilter = computed(() => ({
  filter: searchCompanyDebounced.value
    ? objectToJsonString({ q: searchCompanyDebounced.value })
    : ''
}))

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  initials,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data, refetch } = useQuery({
  queryKey: ['staffs', getParams.value],
  queryFn: () => getStaffs(getParams.value),
  select
})

const {
  data: companies,
  refetch: refetchCompanies,
  isRefetching: isRefetchingCompanies
} = useQuery({
  queryKey: ['companies', companyFilter.value],
  queryFn: () => getCompanies(companyFilter.value),
  select: (data) => data.results
})

/**
 * Functions
 */

const onCreate = () => {
  router.push('/company/staff/create')
}

function editStaff(id: string) {
  router.push({
    name: 'company/staff-edit',
    params: { id }
  })
}

function searchChange(event: CustomEvent) {
  searchTerm.value = event.detail
}

function handleSearch() {
  const term = searchTermDebounced.value
  router.push({ query: { ...route.query, page: initials.page, term } })
}

function select({ pagination, results }: getStaffsResponse) {
  totalPages.value = pagination.totalPages

  return results
}

function handleInputCompany(event: CustomEvent) {
  searchTermCompany.value = event.detail

  companySelected(event)
}

function companySelected(event: CustomEvent) {
  const company = companies.value?.find((data: Company) => data.name === event.detail)

  companyId.value = company?.id || ''

  refetch()
}

/**
 * Lifecycle
 */

watch(searchTermDebounced, handleSearch)
watch(searchCompanyDebounced, () => refetchCompanies())
watch(() => route.params, refetch)
</script>
<style scoped lang="scss">
.list-filters {
  display: grid;
  grid-template-columns: auto auto;
  gap: var(--gl-spacing-04);
}
</style>
