<template>
  <div v-if="!isLoading">
    <FormLayout title="Editar staff">
      <template #nav>
        <router-link v-slot="{ href }" :to="{ name: 'company/staff' }">
          <WLink :href="href">Voltar</WLink>
        </router-link>
      </template>
      <template #form>
        <WSelect label="Company" :disabled="true">
          <option value="" selected>{{ selectedCompany }}</option>
        </WSelect>
        <WTextfield
          label="Nome"
          v-model="form.firstName"
          placeholder="Informe o nome"
          @WBlur="validate('firstName')"
          :invalid="hasError('firstName')"
          :errorText="getErrors('firstName')"
        />
        <WTextfield
          label="Sobrenome"
          v-model="form.lastName"
          placeholder="Informe o sobrenome"
          @WBlur="validate('lastName')"
          :invalid="hasError('lastName')"
          :errorText="getErrors('lastName')"
        />
        <WTextfield
          label="Email"
          v-model="form.email"
          placeholder="Informe o email"
          @WBlur="validate('email')"
          :invalid="hasError('email')"
          :errorText="getErrors('email')"
        />

        <WSelect
          label="Role"
          v-model="form.role"
          @WChange="validate('role')"
          :invalid="hasError('role')"
          :errorText="getErrors('role')"
          v-if="roles"
        >
          <option value="" selected>Selecione uma role</option>
          <option v-for="item in roles" :key="item.id" :value="item.id">
            {{ item.value }}
          </option>
        </WSelect>

        <WSelect
          label="Nível de acesso"
          v-model="form.accessLevel"
          @WChange="validate('accessLevel')"
          :invalid="hasError('accessLevel')"
          :errorText="getErrors('accessLevel')"
          v-if="accessLevel"
        >
          <option value="" selected>Selecione um nível de acesso</option>
          <option v-for="item in accessLevel" :key="item.id" :value="item.id">
            {{ item.value }}
          </option>
        </WSelect>
      </template>

      <template #actions>
        <WButton
          variant="cta"
          size="large"
          :disabled="invalid"
          @click="validateAndSave"
          :loading="saveIsPending"
        >
          Salvar
        </WButton>
        <WButton variant="secondary" size="large" @click="openDialog" :loading="removeIsPending">
          Deletar
        </WButton>
      </template>
    </FormLayout>

    <WDialog
      ref="confirm"
      dialog-title="Deletar staff"
      description="Tem certeza que deseja deletar?"
      dialog-id="delete"
      confirm-label="Deletar"
      cancel-label="Voltar"
      @WClosed="submit"
    />
  </div>
</template>

<script setup lang="ts">
import { FormLayout, type SnackbarComponentProps } from '@commons/index'
import { WTextfield, WLink, WSelect, WButton, WDialog } from '@alice-health/wonderland-vue'
import type { Staff } from '@company-staff/models'
import { computed, ref, type Ref } from 'vue'
import { inject } from 'vue'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { deleteStaff, updateStaff } from '@company-staff/api/mutations'
import { useRoute, useRouter } from 'vue-router'
import { useValidation } from '@alice-health/vue-hooks'
import { StaffForm } from '@company-staff/schemas/staffForm'
import { getRoles } from '@company-staff/api/queries/getRoles'
import { getStaffById } from '@company-staff/api/queries/getStaffById'
import { getAccessLevel } from '@company-staff/api/queries/getAccessLevel'

/**
 * Custom types
 */
type Form = Omit<Staff, 'id' | 'createdAt' | 'updatedAt'>

/**
 * Refs
 */
const form: Ref<Form> = ref({
  companyId: '',
  firstName: '',
  lastName: '',
  email: '',
  role: '',
  accessLevel: ''
})
const confirm = ref()
const selectedCompany = ref()
const id = computed(() => route.params.id as string)

/*
 * Injections
 */

const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */
const route = useRoute()
const router = useRouter()
const queryClient = useQueryClient()
const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: StaffForm,
  form,
  invalidInitialValue: false
})

const { isLoading } = useQuery({
  queryKey: ['staffById', id.value],
  queryFn: () => getStaffById(id.value),
  select: (data) => {
    form.value = data
    form.value.companyId = data.company?.id || ''
    selectedCompany.value = data.company?.name

    return form.value
  }
})

const { data: roles } = useQuery({
  queryKey: ['roles'],
  queryFn: () => getRoles()
})

const { data: accessLevel } = useQuery({
  queryKey: ['accessLevel'],
  queryFn: () => getAccessLevel()
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['updateStaff'],
  mutationFn: () => updateStaff(id.value, form.value),
  onSuccess: () => onSuccess('atualizada'),
  onError: () => onError('atualizar')
})

const { mutate: remove, isPending: removeIsPending } = useMutation({
  mutationKey: ['deleteStaff'],
  mutationFn: () => deleteStaff(id.value),
  onSuccess: () => onSuccess('deletada'),
  onError: () => onError('deletar')
})

/**
 * Functions
 */

function validateAndSave() {
  const schema = validateAll()

  if (schema.success) save()
}
function openDialog() {
  confirm.value.$el.open()
}

function submit(event: CustomEvent) {
  if (event.detail === 'confirm') remove()
}

async function onError(action: string) {
  snackbar?.value?.$el.add({
    message: `Erro ao ${action} staff`,
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess(action: string) {
  await queryClient.invalidateQueries({ queryKey: ['staff'] })
  router.push({ name: 'company/staff' })

  snackbar?.value?.$el.add({
    message: `Staff ${action} com sucesso`,
    icon: 'icCheckOutlined'
  })
}
</script>
<style scoped lang="scss"></style>
