<template>
  <FormLayout title="Novo staff">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'company/staff' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <WAutocomplete
        label="Company"
        placeholder="Escolha a empresa"
        :loading="isRefetchingCompanies"
        @WChange="handleInput"
        :items="companies"
        @WSelect="companySelected"
        search-by-key="name"
      />
      <WTextfield
        label="Name"
        v-model="form.firstName"
        placeholder="Informe o nome"
        @WBlur="validate('firstName')"
        :invalid="hasError('firstName')"
        :errorText="getErrors('firstName')"
      />
      <WTextfield
        label="Lastname"
        v-model="form.lastName"
        placeholder="Informe o sobrenome"
        @WBlur="validate('lastName')"
        :invalid="hasError('lastName')"
        :errorText="getErrors('lastName')"
      />
      <WTextfield
        label="Email"
        v-model="form.email"
        placeholder="Informe o email"
        @WBlur="validate('email')"
        :invalid="hasError('email')"
        :errorText="getErrors('email')"
      />

      <WSelect
        label="Role"
        v-model="form.role"
        @WChange="validate('role')"
        :invalid="hasError('role')"
        :errorText="getErrors('role')"
        v-if="roles"
      >
        <option value="" selected>Selecione uma role</option>
        <option v-for="item in roles" :key="item.id" :value="item.id">
          {{ item.value }}
        </option>
      </WSelect>

      <WSelect
        label="Nível de acesso"
        v-model="form.accessLevel"
        @WChange="validate('accessLevel')"
        :invalid="hasError('accessLevel')"
        :errorText="getErrors('accessLevel')"
        v-if="accessLevel"
      >
        <option value="" selected>Selecione um nível de acesso</option>
        <option v-for="item in accessLevel" :key="item.id" :value="item.id">
          {{ item.value }}
        </option>
      </WSelect>
    </template>
    <template #actions>
      <WButton
        variant="cta"
        size="large"
        @click="validateAndSave"
        :loading="saveIsPending"
        :disabled="invalid"
      >
        Salvar
      </WButton>
    </template>
  </FormLayout>
</template>

<script setup lang="ts">
import { FormLayout, type SnackbarComponentProps } from '@commons/index'
import { WTextfield, WLink, WSelect, WButton, WAutocomplete } from '@alice-health/wonderland-vue'
import type { Ref } from 'vue'
import type { Company, Staff } from '@company-staff/models'
import { computed, inject, ref, watch } from 'vue'
import { StaffForm } from '@company-staff/schemas/staffForm'
import { useDebounce, useQueryString, useValidation } from '@alice-health/vue-hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { useRouter } from 'vue-router'
import { getCompanies } from '@company-staff/api/queries'
import { createStaff } from '@company-staff/api/mutations'
import { getRoles } from '@company-staff/api/queries/getRoles'
import { getAccessLevel } from '@company-staff/api/queries/getAccessLevel'

/**
 * Refs
 */
const form: Ref<Staff> = ref({
  companyId: '',
  firstName: '',
  lastName: '',
  email: '',
  role: '',
  accessLevel: ''
})

const searchTerm = ref('')

/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Hooks
 */

const { getErrors, hasError, validate, invalid, validateAll } = useValidation({
  formSchema: StaffForm,
  form
})

const router = useRouter()
const queryClient = useQueryClient()
const searchTermDebounced = useDebounce(searchTerm, 800)
const { objectToJsonString } = useQueryString()

const companyFilter = computed(() => ({
  filter: searchTermDebounced.value ? objectToJsonString({ q: searchTermDebounced.value }) : ''
}))

const {
  data: companies,
  refetch: refetchCompanies,
  isRefetching: isRefetchingCompanies
} = useQuery({
  queryKey: ['companies', companyFilter.value],
  queryFn: () => getCompanies(companyFilter.value),
  select: (data) => data.results
})

const { data: roles } = useQuery({
  queryKey: ['roles'],
  queryFn: () => getRoles()
})

const { data: accessLevel } = useQuery({
  queryKey: ['accessLevel'],
  queryFn: () => getAccessLevel()
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createStaff'],
  mutationFn: () => createStaff(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */
function validateAndSave() {
  const schema = validateAll()

  if (schema.success) save()
}

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar staff',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['staff'] })
  router.push({ name: 'company/staff' })
  snackbar?.value?.$el.add({
    message: 'Staff cadastrado com sucesso',
    icon: 'icCheckOutlined'
  })
}

function handleInput(event: CustomEvent) {
  searchTerm.value = event.detail

  companySelected(event)
}

function companySelected(event: CustomEvent) {
  const company = companies.value?.find((data: Company) => data.name === event.detail)

  form.value.companyId = company?.id || ''

  validate('company')
}

watch(searchTermDebounced, () => refetchCompanies())
</script>
