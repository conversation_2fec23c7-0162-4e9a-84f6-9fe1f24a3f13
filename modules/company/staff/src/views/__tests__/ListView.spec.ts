import { customRender, screen } from '@company-staff/tests'
import { server } from '@commons/services/mockServer'
import { list, companyList } from '@company-staff/tests/mocks'

import ListView from '@company-staff/views/ListView.vue'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(list, companyList))

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()

    expect(screen.getByText('Ana Silva')).toBeInTheDocument()
    expect(screen.getByText('LEGAL_REPRESENTATIVE')).toBeInTheDocument()
    expect(screen.getByText('Alice')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(await screen.findByPlaceholderText('Nome ou CNPJ da empresa')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Busca pelo nome ou email')).toBeInTheDocument()
  })
})
