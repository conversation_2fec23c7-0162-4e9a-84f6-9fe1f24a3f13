import { customRender, screen } from '@company-staff/tests'
import { server } from '@commons/services/mockServer'
import EditView from '@company-staff/views/EditView.vue'
import { companyList, getRoles, getAccessLevel, getStaffById } from '@company-staff/tests/mocks'

const id = '*********'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(companyList, getRoles, getAccessLevel, getStaffById))

  it.skip('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON>'
      })
    ).toBeEnabled()

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeEnabled()

    expect(await screen.findByRole('textbox', { name: 'Name' })).toHaveValue('Ana')
    expect(await screen.findByRole('textbox', { name: 'Lastname' })).toHaveValue('Silva')
    expect(await screen.findByRole('textbox', { name: 'Email' })).toHaveValue('<EMAIL>')
    expect(await screen.findByRole('combobox', { name: 'Role' })).toHaveValue('MAIN_COMPANY_STAFF')
    expect(await screen.findByRole('combobox', { name: 'Nível de acesso' })).toHaveValue('ADMIN')
  })
})
