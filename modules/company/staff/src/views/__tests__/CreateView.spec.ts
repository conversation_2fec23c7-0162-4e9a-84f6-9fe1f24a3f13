import CreateView from '@company-staff/views/CreateView.vue'
import { customRender, screen } from '@company-staff/tests'
import { server } from '@commons/services/mockServer'
import { companyList, getRoles, getAccessLevel } from '@company-staff/tests/mocks'

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(companyList, getRoles, getAccessLevel))

  it('renders properly', async () => {
    const { user } = customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Novo staff'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON>'
      })
    ).toBeDisabled()

    await user.type(await screen.findByRole('textbox', { name: 'Name' }), 'Maria')
    await user.type(await screen.findByRole('textbox', { name: 'Lastname' }), 'Campo<PERSON>')
    await user.type(await screen.findByRole('textbox', { name: 'Email' }), '<EMAIL>')
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Role' }), [
      'MAIN_COMPANY_STAFF'
    ])
    await user.selectOptions(await screen.findByRole('combobox', { name: 'Nível de acesso' }), [
      'ADMIN'
    ])
  })
})
