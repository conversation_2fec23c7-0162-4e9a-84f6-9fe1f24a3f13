import type { Company } from '@company-staff/models'
import { HttpResponse, http } from 'msw'

const listOfCompanies: Company[] = [
  {
    id: '88d20ed9-8e0f-4475-a8a4-50a4db63fd00',
    legalName: '<PERSON>',
    name: '<PERSON>'
  }
]

const companyList = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/company`, () => {
  return HttpResponse.json({
    pagination: {
      totalPages: 1,
      pageSize: 1
    },
    results: listOfCompanies
  })
})

export default companyList
