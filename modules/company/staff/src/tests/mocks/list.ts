import type { Staff } from '@company-staff/models'

import { HttpResponse, http } from 'msw'

const listOfStaffs: Staff[] = [
  {
    companyId: '*********',
    firstName: 'Ana',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    role: 'LEGAL_REPRESENTATIVE',
    accessLevel: 'ADMIN',
    company: {
      id: '*********',
      name: '<PERSON>',
      legalName: 'Alice'
    }
  }
]

const list = http.get(`${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/companyStaff`, () => {
  return HttpResponse.json({
    pagination: {
      totalPages: 1,
      pageSize: 1
    },
    results: listOfStaffs
  })
})

export default list
