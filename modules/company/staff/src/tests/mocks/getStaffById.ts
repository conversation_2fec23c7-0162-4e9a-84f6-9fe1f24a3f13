import type { Staff } from '@company-staff/models'
import { HttpResponse, http } from 'msw'

const getStaffById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/companyStaff/:id`,
  ({ params }) => {
    const id = params.id as string

    const staff: Staff = {
      id,
      companyId: '*********',
      firstName: 'Ana',
      lastName: 'Silva',
      email: '<EMAIL>',
      role: 'LEGAL_REPRESENTATIVE',
      accessLevel: 'ADMIN',
      company: {
        id: '*********',
        name: 'Alice',
        legalName: 'Alice'
      }
    }

    return HttpResponse.json(staff)
  }
)

export default getStaffById
