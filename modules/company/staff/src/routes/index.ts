import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const companyStaffRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/company',
      name: 'company',
      beforeEnter,
      children: [
        {
          path: 'staff',
          name: 'company/staff',
          component: async () => await import('@company-staff/views/ListView.vue')
        },
        {
          path: 'staff/create',
          name: 'company/staff-create',
          component: async () => await import('@company-staff/views/CreateView.vue')
        },
        {
          path: 'staff/:id',
          name: 'company/staff-edit',
          component: async () => await import('@company-staff/views/EditView.vue')
        }
      ]
    }
  ]
}

export default companyStaffRoutes
