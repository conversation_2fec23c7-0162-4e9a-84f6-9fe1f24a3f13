import type { ValueItem } from '@commons/index'

import http from '@http/index'
import { queryOptions } from '@tanstack/vue-query'

const getPIIFields = async () => {
  const { data } = await http.get<ValueItem[]>(`/sherlock/getPIIFields`)
  return data
}

export function getPIIFieldsOptions() {
  return queryOptions({
    queryKey: ['getPIIFields'],
    queryFn: () => getPIIFields(),
    initialData: (): ValueItem[] => []
  })
}
