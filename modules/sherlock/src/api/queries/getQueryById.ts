import http from '@http/index'
import { queryOptions } from '@tanstack/vue-query'

interface Query {
  id: string
  description: string
  type: string
  query: string
  piiFields: string[]
  status: string
  createdAt: string
  errorMessage: string
}

const getQueryById = async (id: string) => {
  const { data } = await http.get<Query>(`/sherlock/channels/${id}`)
  return data
}

export function getQueryByIdOptions(id: string) {
  return queryOptions({
    queryKey: ['getQueryById', id],
    queryFn: () => getQueryById(id),
    initialData: (): Query => ({
      id: '',
      description: '',
      type: '',
      query: '',
      piiFields: [''],
      status: '',
      createdAt: '',
      errorMessage: ''
    })
  })
}
