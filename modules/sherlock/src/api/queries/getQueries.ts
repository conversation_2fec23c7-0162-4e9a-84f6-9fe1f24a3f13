import type { PaginatedResponse } from '@commons/index'

import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'
import { queryOptions } from '@tanstack/vue-query'

interface Query {
  id: string
  description: string
  type: string
  status: string
  createdAt: string
}

export type getQueriesResponse = PaginatedResponse<Query[]>

const getQueries = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()

  const qs = params ? `?${createQueryString(params)}` : ''

  const { data } = await http.get<getQueriesResponse>(`/sherlock/channels${qs}`)
  return data
}

export function getQueriesOptions(params: Record<string, string>) {
  return queryOptions({
    queryKey: ['getQueries', params],
    queryFn: () => getQueries(params)
  })
}
