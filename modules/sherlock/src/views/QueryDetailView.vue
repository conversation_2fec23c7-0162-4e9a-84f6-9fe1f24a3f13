<template>
  <FormLayout>
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'sherlock/list' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <WTextfield label="Descrição" :value="data.description" disabled />
      <WTextarea label="Query" :value="data.query" />
      <WTextfield label="Tipo" :value="data.type" disabled />
      <WTextfield label="Status" :value="data.status" disabled />
      <WTextfield label="Mensagem de erro" :value="data.errorMessage" disabled />
      <WTextfield label="Data de criação" :value="formatDate(data.createdAt)" disabled />
      <WParagraph size="large">Campos de PII</WParagraph>
      <WGrid
        :columns="{
          xs: 4,
          sm: 6,
          md: 6,
          lg: 8,
          xl: 8
        }"
      >
        <WGridItem v-for="tag in data.piiFields" :key="tag">
          <WTag :label="getPIILabel(tag)" />
        </WGridItem>
      </WGrid>
    </template>
    <template #actions>
      <WButton icon="icDownload" variant="cta" size="large" @click="download">Baixar</WButton>
      <WButton icon="icTrash" variant="secondary" size="large" @click="download">Deletar</WButton>
    </template>
  </FormLayout>
</template>
<script setup lang="ts">
import { getPIIFieldsOptions, getQueryByIdOptions } from '@sherlock/api/queries'
import {
  WButton,
  WLink,
  WParagraph,
  WTag,
  WTextarea,
  WTextfield,
  WGrid,
  WGridItem
} from '@alice-health/wonderland-vue'
import { useRoute } from 'vue-router'
import { useQuery } from '@tanstack/vue-query'
import { FormLayout } from '@commons/index'

/**
 * Hooks
 */

const route = useRoute()
const { data: PIIFields } = useQuery(getPIIFieldsOptions())
const { data } = useQuery(getQueryByIdOptions(route.params.id as string))

/**
 * Functions
 */
function download() {}

function getPIILabel(field: string) {
  const fieldData = PIIFields.value.findIndex((pii) => pii.value === field)
  return PIIFields.value[fieldData]?.id
}

function formatDate(date: string) {
  return new Date(date).toLocaleDateString('pt-br', { dateStyle: 'short' })
}
</script>
