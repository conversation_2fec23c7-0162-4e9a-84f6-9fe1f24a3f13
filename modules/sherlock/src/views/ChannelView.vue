<template>
  <FormLayout title="Channel">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'sherlock/list' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <WTextfield
        label="Código interno do membro"
        v-model="form.personInternalCode"
        placeholder="Escreva o código interno do membro"
        @WBlur="validate('personInternalCode')"
        :invalid="hasError('personInternalCode')"
        :errorText="getErrors('personInternalCode')"
      />
      <WTextfield
        label="Canais"
        v-model="form.channelIds"
        placeholder="Canais"
        @WBlur="validate('channelIds')"
        :invalid="hasError('channelIds')"
        :errorText="getErrors('channelIds')"
      />
      <WTextfield
        label="Motivo"
        v-model="form.reason"
        placeholder="Escreva um motivo"
        @WBlur="validate('reason')"
        :invalid="hasError('reason')"
        :errorText="getErrors('reason')"
      />
    </template>
    <template #actions>
      <WButton
        variant="cta"
        size="large"
        :disabled="invalid"
        :loading="saveIsPending"
        @click="save"
      >
        Salvar
      </WButton>
    </template>
  </FormLayout>
</template>
<script setup lang="ts">
import type { Ref } from 'vue'
import type { channel } from '@sherlock/models'
import type { SnackbarComponentProps } from '@commons/index'

import { ref, inject } from 'vue'
import { WButton, WLink, WTextfield } from '@alice-health/wonderland-vue'
import { useValidation } from '@alice-health/vue-hooks'
import { channelForm } from '@sherlock/schemas'

import { FormLayout } from '@commons/index'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { useRouter } from 'vue-router'
import { channels } from '@sherlock/api/mutations'
/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<channel> = ref({
  personInternalCode: '',
  channelIds: '',
  reason: ''
})

const router = useRouter()
const queryClient = useQueryClient()

const { getErrors, hasError, validate, invalid } = useValidation({
  formSchema: channelForm,
  form
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createChannels'],
  mutationFn: () => channels(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar a query',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['getQueries'] })
  router.push({ name: 'sherlock/list' })
  snackbar?.value?.$el.add({
    message: 'Query cadastrada com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
