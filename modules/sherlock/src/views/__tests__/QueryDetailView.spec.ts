import { customRender, screen } from '@sherlock/tests'
import { server } from '@commons/services/mockServer'
import EditView from '@sherlock/views/QueryDetailView.vue'
import { getQueryById, getPIIFields } from '@sherlock/tests/mocks'

const id = '03a06367-cd36-4667-ac14-c329894d668b'

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...Object.assign({}, actual),
    useRoute: () => ({
      params: { id }
    })
  }
})

describe('EditView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getPIIFields, getQueryById))

  it('renders properly', async () => {
    customRender(EditView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON><PERSON>'
      })
    ).toBeEnabled()

    expect(
      await screen.findByRole('button', {
        name: 'Deletar'
      })
    ).toBeEnabled()

    expect(await screen.findByRole('textbox', { name: 'Descrição' })).toHaveValue(
      'query used to return data PII'
    )
    expect(await screen.findByRole('textbox', { name: 'Tipo' })).toHaveValue('QUERY')
    expect(await screen.findByRole('textbox', { name: 'Status' })).toHaveValue('ERROR')
    expect(await screen.findByRole('textbox', { name: 'Mensagem de erro' })).toHaveValue(
      'QueryEnrichmentConsumer::enrichQueryResult error'
    )
    expect(await screen.findByRole('textbox', { name: 'Data de criação' })).toHaveValue(
      '06/01/2023'
    )

    expect(screen.getByText('Nome')).toBeInTheDocument()
    expect(screen.getByText('Sobrenome')).toBeInTheDocument()
  })
})
