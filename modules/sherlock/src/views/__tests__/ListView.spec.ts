import { customRender, screen } from '@sherlock/tests'
import { server } from '@commons/services/mockServer'
import { getQueries } from '@sherlock/tests/mocks'

import ListView from '@sherlock/views/ListView.vue'

describe('ListView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getQueries))

  it('renders properly', async () => {
    customRender(ListView)

    expect(
      await screen.findByRole('button', {
        name: '<PERSON>ria<PERSON>'
      })
    ).toBeInTheDocument()

    expect(await screen.findByRole('table')).toBeInTheDocument()

    expect(screen.getByText('03a06367-cd36-4667-ac14-c329894d668b')).toBeInTheDocument()
    expect(screen.getByText('QUERY')).toBeInTheDocument()
    expect(screen.getByText('query used to return data PII')).toBeInTheDocument()
  })
})
