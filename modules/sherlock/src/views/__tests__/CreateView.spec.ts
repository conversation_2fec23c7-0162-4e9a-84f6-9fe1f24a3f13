import CreateView from '@sherlock/views/CreateView.vue'

import { server } from '@commons/services/mockServer'
import { getPIIFields } from '@sherlock/tests/mocks'
import { customRender, screen } from '@sherlock/tests'

describe('CreateView', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  beforeEach(() => server.use(getPIIFields))

  it('renders properly', async () => {
    const { user } = customRender(CreateView)

    expect(
      await screen.findByRole('heading', {
        name: 'Criar query'
      })
    ).toBeInTheDocument()

    expect(
      await screen.findByRole('button', {
        name: '<PERSON><PERSON>'
      })
    ).toBeDisabled()

    await user.type(await screen.findByRole('textbox', { name: 'Query' }), 'Teste')
    await user.type(await screen.findByRole('textbox', { name: '<PERSON><PERSON><PERSON><PERSON>' }), 'Teste')
    await user.click(await screen.findByRole('checkbox', { name: 'Nome' }))
    await user.click(await screen.findByRole('checkbox', { name: 'Sexo' }))

    expect(
      await screen.findByRole('button', {
        name: 'Salvar'
      })
    ).toBeEnabled()
  })
})
