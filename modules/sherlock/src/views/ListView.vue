<template>
  <ListLayout>
    <template #actions>
      <WButton icon="icAdd" variant="cta" size="large" @click="onCreate">Criar</WButton>
    </template>
    <template #list>
      <WListControllers
        :hide-input="true"
        has-pagination
        margin="none"
        has-items-per-page-select
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WPaginationNext="setNext"
        @WPaginationPrevious="setPrev"
      >
        <div slot="select-items-per-page">
          <WSelect placeholder="10" @WChange="setLimit" :model-value="currentLimit">
            <option v-for="limit in limits" :key="limit" :value="limit">{{ limit }}</option>
          </WSelect>
        </div>
      </WListControllers>

      <WTable>
        <WTableHeader slot="header">
          <WTableHeaderCell
            size="large"
            :width="header.width"
            v-for="header in headers"
            :key="header.id"
          >
            {{ header?.title }}
          </WTableHeaderCell>
        </WTableHeader>
        <WTableBody slot="body">
          <WTableRow v-for="item in data" :key="item.id">
            <WTableBodyCell :width="columnWidthMap.id" size="medium">
              {{ item.id }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.description" size="medium">
              {{ item.description }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.type" size="medium">
              {{ item.type }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.status" size="medium">
              {{ item.status }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.createdAt" size="medium">
              {{ formatDate(item.createdAt) }}
            </WTableBodyCell>
            <WTableBodyCell :width="columnWidthMap.edit" size="medium" align="end">
              <WButton icon-button variant="secondary" icon="icEyeShow" @click="detail(item.id)" />
            </WTableBodyCell>
          </WTableRow>
        </WTableBody>
      </WTable>
    </template>
  </ListLayout>
</template>
<script setup lang="ts">
import { getQueriesOptions, type getQueriesResponse } from '@sherlock/api/queries'

import {
  WButton,
  WTable,
  WTableBody,
  WTableBodyCell,
  WTableHeader,
  WTableHeaderCell,
  WTableRow,
  WListControllers,
  WSelect
} from '@alice-health/wonderland-vue'
import { computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePagination } from '@alice-health/vue-hooks'
import { useQuery } from '@tanstack/vue-query'
import { ListLayout } from '@commons/index'

/**
 * Constants
 */
const limits = [5, 10, 15]
const columnWidthMap = {
  id: '30%',
  description: '',
  type: '',
  status: '',
  createdAt: '',
  edit: '10%'
}

const headers = [
  { id: 1, title: 'ID', width: columnWidthMap.id },
  { id: 2, title: 'Descrição', width: columnWidthMap.description },
  { id: 3, title: 'Tipo', width: columnWidthMap.type },
  { id: 4, title: 'Status', width: columnWidthMap.status },
  { id: 5, title: 'Data de criação', width: columnWidthMap.createdAt },
  { id: 6, width: columnWidthMap.edit }
]

/**
 * Computed Variables
 */

const getParams = computed(() => ({
  page: currentPage.value.toString(),
  pageSize: currentLimit.value.toString()
}))

/**
 * Hooks
 */

const router = useRouter()
const route = useRoute()

const {
  setNext,
  setPrev,
  setLimit,
  currentPage,
  currentLimit,
  totalPages,
  disableNextButton,
  disablePrevButton
} = usePagination({ router, route })

const { data, refetch } = useQuery({
  ...getQueriesOptions(getParams.value),
  select
})

/**
 * Functions
 */

const onCreate = () => {
  router.push('/sherlock/create')
}

function select({ results, pagination }: getQueriesResponse) {
  totalPages.value = pagination.totalPages

  return results
}

function formatDate(date: string) {
  return new Date(date).toLocaleDateString('pt-br', { dateStyle: 'short' })
}

function detail(id: string) {
  router.push({ name: 'sherlock/detail', params: { id } })
}

/**
 * Lifecycle
 */

watch(() => route.params, refetch)
</script>
