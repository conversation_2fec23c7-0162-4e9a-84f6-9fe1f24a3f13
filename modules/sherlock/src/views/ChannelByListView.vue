<template>
  <FormLayout title="Canais por lista">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'sherlock/list' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <WTextarea
        label="Canais"
        v-model="form.channelIds"
        placeholder="Insira a lista de ids separados por virgula"
        @WBlur="validate('channelIds')"
        :invalid="hasError('channelIds')"
        :errorText="getErrors('channelIds')"
      />
      <WTextfield
        label="Motivo"
        v-model="form.reason"
        placeholder="Escreva um motivo"
        @WBlur="validate('reason')"
        :invalid="hasError('reason')"
        :errorText="getErrors('reason')"
      />
    </template>
    <template #actions>
      <WButton
        variant="cta"
        size="large"
        :disabled="invalid"
        :loading="saveIsPending"
        @click="save"
      >
        Salvar
      </WButton>
    </template>
  </FormLayout>
</template>
<script setup lang="ts">
import type { Ref } from 'vue'
import type { channelByList } from '@sherlock/models'
import type { SnackbarComponentProps } from '@commons/index'

import { ref, inject } from 'vue'
import { WButton, WLink, WTextarea, WTextfield } from '@alice-health/wonderland-vue'
import { useValidation } from '@alice-health/vue-hooks'
import { channelByListForm } from '@sherlock/schemas'

import { FormLayout } from '@commons/index'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { useRouter } from 'vue-router'
import { channels } from '@sherlock/api/mutations'
/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<channelByList> = ref({
  channelIds: '',
  reason: ''
})

const router = useRouter()
const queryClient = useQueryClient()

const { getErrors, hasError, validate, invalid } = useValidation({
  formSchema: channelByListForm,
  form
})

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createChannels'],
  mutationFn: () => channels(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar a query',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['getQueries'] })
  router.push({ name: 'sherlock/list' })
  snackbar?.value?.$el.add({
    message: 'Query cadastrada com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
