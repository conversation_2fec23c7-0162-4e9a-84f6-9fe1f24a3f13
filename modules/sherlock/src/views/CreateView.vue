<template>
  <FormLayout title="Criar query">
    <template #nav>
      <router-link v-slot="{ href }" :to="{ name: 'sherlock/list' }">
        <WLink :href="href">Voltar</WLink>
      </router-link>
    </template>
    <template #form>
      <WTextarea
        label="Query"
        v-model="form.customQuery"
        placeholder="Escreva uma query customizada"
        @WBlur="validate('customQuery')"
        :invalid="hasError('customQuery')"
        :errorText="getErrors('customQuery')"
      />
      <WTextfield
        label="Descrição"
        v-model="form.description"
        placeholder="Escreva uma descrição para a query"
        @WBlur="validate('description')"
        :invalid="hasError('description')"
        :errorText="getErrors('description')"
      />

      <WParagraph size="large">Informação de identificação pessoal (PII)</WParagraph>
      <WGrid
        v-if="!isLoading"
        :columns="{
          xs: 3,
          sm: 3,
          md: 3,
          lg: 3,
          xl: 3
        }"
      >
        <WGridItem v-for="item in PIIFields" :key="item.value">
          <WCheckbox
            @WChange="validate('piiFields')"
            :label="item.id"
            v-model="form.piiFields"
            :value="item.value"
          />
        </WGridItem>
        <WLabel>{{ getErrors('piiFields') }}</WLabel>
      </WGrid>
    </template>
    <template #actions>
      <WButton
        variant="cta"
        size="large"
        :disabled="invalid"
        :loading="saveIsPending"
        @click="save"
      >
        Salvar
      </WButton>
    </template>
  </FormLayout>
</template>
<script setup lang="ts">
import type { Ref } from 'vue'
import type { CreateQuery } from '@sherlock/models'
import type { SnackbarComponentProps } from '@commons/index'

import { ref, inject } from 'vue'
import {
  WButton,
  WCheckbox,
  WLabel,
  WLink,
  WParagraph,
  WTextarea,
  WTextfield,
  WGrid,
  WGridItem
} from '@alice-health/wonderland-vue'
import { useValidation } from '@alice-health/vue-hooks'
import { createQueryForm } from '@sherlock/schemas/createQueryForm'

import { FormLayout } from '@commons/index'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { useRouter } from 'vue-router'
import { createQuery } from '@sherlock/api/mutations'
import { getPIIFieldsOptions } from '@sherlock/api/queries'
/*
 * Injections
 */
const snackbar = inject<SnackbarComponentProps>('snackbar')

/**
 * Refs
 */
const form: Ref<CreateQuery> = ref({
  customQuery: '',
  description: '',
  piiFields: false
})

/**
 * Hooks
 */

const router = useRouter()
const queryClient = useQueryClient()

const { getErrors, hasError, validate, invalid } = useValidation({
  formSchema: createQueryForm,
  form
})

const { data: PIIFields, isLoading } = useQuery(getPIIFieldsOptions())

const { mutate: save, isPending: saveIsPending } = useMutation({
  mutationKey: ['createQuery'],
  mutationFn: () => createQuery(form.value),
  onSuccess,
  onError
})

/**
 * Functions
 */

async function onError() {
  snackbar?.value?.$el.add({
    message: 'Erro ao cadastrar a query',
    icon: 'icAlertCircleOutlined'
  })
}

async function onSuccess() {
  await queryClient.invalidateQueries({ queryKey: ['getQueries'] })
  router.push({ name: 'sherlock/list' })
  snackbar?.value?.$el.add({
    message: 'Query cadastrada com sucesso',
    icon: 'icCheckOutlined'
  })
}
</script>
