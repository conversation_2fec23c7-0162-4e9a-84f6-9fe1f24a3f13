import type { RouteR<PERSON>ordRaw, NavigationGuardWithThis } from 'vue-router'

const sherlockRoutes = (beforeEnter: NavigationGuardWithThis<undefined>): RouteRecordRaw[] => {
  return [
    {
      path: '/sherlock',
      name: 'sherlock',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'sherlock-list',
          component: async () => await import('@sherlock/views/ListView.vue')
        },
        {
          path: 'create',
          name: 'sherlock-create',
          component: async () => await import('@sherlock/views/CreateView.vue')
        },
        {
          path: 'channel',
          name: 'sherlock-channel',
          component: async () => await import('@sherlock/views/ChannelView.vue')
        },
        {
          path: 'channel-by-list',
          name: 'sherlock-channel-by-list',
          component: async () => await import('@sherlock/views/ChannelByListView.vue')
        },
        {
          path: 'query-detail',
          name: 'sherlock-query-detail',
          component: async () => await import('@sherlock/views/QueryDetailView.vue')
        }
      ]
    }
  ]
}

export default sherlockRoutes
