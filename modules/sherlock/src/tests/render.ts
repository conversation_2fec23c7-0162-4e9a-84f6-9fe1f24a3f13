import { createRouter, createWebHistory } from 'vue-router'
import { ref, type Component } from 'vue'
import { buildCustomRender } from '@alice-health/unit-presets/src/testing-library'
import { VueQueryPlugin } from '@tanstack/vue-query'

export * from '@alice-health/unit-presets/src/testing-library'

export const customRender = (
  component: Component,
  { props = {}, slots = {}, mocks = {}, stubs = {}, provide = {}, ...other } = {}
) => {
  const routerInstance = createRouter({
    history: createWebHistory('/'),
    routes: [
      { path: '/', name: 'home', component: async () => ({}) },
      {
        path: '/sherlock',
        name: 'sherlock',
        children: [
          {
            path: 'list',
            name: 'sherlock/list',
            component: async () => ({})
          },
          {
            path: 'create',
            name: 'sherlock/create',
            component: async () => ({})
          },
          {
            path: 'channel',
            name: 'sherlock/channel',
            component: async () => ({})
          },
          {
            path: 'channel-by-list',
            name: 'sherlock/channel-by-list',
            component: async () => ({})
          },
          {
            path: 'detail/:id',
            name: 'sherlock/detail',
            component: async () => ({})
          }
        ]
      }
    ]
  })

  const snackbar = ref()

  return {
    ...buildCustomRender(component, {
      props,
      slots,
      mocks,
      stubs,
      plugins: [routerInstance, VueQueryPlugin],
      provide: {
        snackbar,
        ...provide
      },
      ...other
    })
  }
}
