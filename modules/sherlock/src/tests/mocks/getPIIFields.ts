import { HttpResponse, http } from 'msw'

const getPIIFields = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/sherlock/getPIIFields`,
  () => {
    return HttpResponse.json([
      { value: 'FirstName', id: 'Nome' },
      { value: 'LastName', id: 'Sobrenome' },
      { value: 'CPF', id: 'CPF' },
      { value: 'Email', id: 'Email' },
      { value: 'Phone', id: 'Telefone' },
      { value: 'Sex', id: 'Sexo' },
      { value: 'BirthDate', id: 'Data de nascimento' },
      { value: 'FullAddress', id: 'Endereço' },
      { value: 'MothersName', id: 'Nome da mãe' },
      { value: 'CNS', id: 'CNS' }
    ])
  }
)

export default getPIIFields
