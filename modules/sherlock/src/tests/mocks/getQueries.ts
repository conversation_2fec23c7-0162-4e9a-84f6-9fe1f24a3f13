import { HttpResponse, http } from 'msw'

const getQueries = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/sherlock/channels`,
  () => {
    return HttpResponse.json({
      pagination: {
        totalPages: 1,
        pageSize: 1
      },
      results: [
        {
          id: '03a06367-cd36-4667-ac14-c329894d668b',
          description: 'query used to return data PII',
          type: 'QUERY',
          status: 'ERROR',
          createdAt: '2023-01-06T19:38:36.125975'
        }
      ]
    })
  }
)

export default getQueries
