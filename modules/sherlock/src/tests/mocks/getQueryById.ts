import { HttpResponse, http } from 'msw'

export const queryId = '03a06367-cd36-4667-ac14-c329894d668b'

const getQueryById = http.get(
  `${import.meta.env.VITE_BACKOFFICE_BFF_PREFIX_URL}/sherlock/channels/${queryId}`,
  () => {
    return HttpResponse.json({
      id: '03a06367-cd36-4667-ac14-c329894d668b',
      description: 'query used to return data PII',
      type: 'QUERY',
      query:
        'SELECT "staging_pii"."member_current"."id" AS "id", "staging_pii"."member_current"."version" AS "version", "staging_pii"."member_current"."created_at" AS "created_at", "staging_pii"."member_current"."updated_at" AS "updated_at", "staging_pii"."member_current"."person_id" AS "person_id", "staging_pii"."member_current"."contract" AS "contract", "staging_pii"."member_current"."external_beneficiary_number" AS "external_beneficiary_number", "staging_pii"."member_current"."activation_date" AS "activation_date", "staging_pii"."member_current"."archived" AS "archived", "staging_pii"."member_current"."status" AS "status", "staging_pii"."member_current"."status_history" AS "status_history", "staging_pii"."member_current"."selected_product" AS "selected_product", "staging_pii"."member_current"."mv_info" AS "mv_info", "staging_pii"."member_current"."canceled_at" AS "canceled_at"\nFROM "staging_pii"."member_current"\nLIMIT 100',
      piiFields: ['FirstName', 'LastName'],
      status: 'ERROR',
      createdAt: '2023-01-06T19:38:36.125975',
      errorMessage: 'QueryEnrichmentConsumer::enrichQueryResult error'
    })
  }
)

export default getQueryById
