# ┌-----------------------------------------------------------------┐
# ⎪                 Auto generated by Clingon CLI                   ⎪
# ⎪      This file is only a example, feel free to edit them        ⎪
# ⎪                                                                 ⎪
# ⎪ In the README file you will find more details and a complete    ⎪
# ⎪ guide on how to use this feature: .clingon/templates/README.md  ⎪
# ⎪                 -------------------------------                 ⎪
# ⎪                 AFTER READ, REMOVE THIS SECTION                 ⎪
# └-----------------------------------------------------------------┘

# Module configs

- identifier: module
  keepTemplateName: true
  folderWrapper: true
  resources:
    - path: modules
      template: ./module/project.json
    - path: modules
      template: ./module/vitest.config.ts
    - path: modules
      template: ./module/vite.config.ts
    - path: modules
      template: ./module/tsconfig.json
    - path: modules
      template: ./module/tsconfig.spec.json
    - path: modules
      template: ./module/tsconfig.lib.json
    - path: modules
      template: ./module/README.md
    - path: modules
      template: ./module/package.json
    - path: modules
      template: ./module/.eslintrc.json
