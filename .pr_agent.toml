[pr_reviewer]
extra_instructions="""\
Make sure the Pull Request follows our company guidelines:
- avoid leave console.log commands on code base
- Mock every new request with MSW
- avoid leave skiped tests
- avoid write routes directly on the app folder instead import from the modules
- Follow the module-based architecture pattern for new features
- Use TypeScript strict mode and avoid using 'any' type
- Implement proper error handling and error boundaries
- Write unit tests for all new components and hooks
- Use Tanstack Query for data fetching and caching
- Use proper TypeScript interfaces for all props and state
- Use proper naming conventions (PascalCase for components, camelCase for functions)
- Implement proper form validation using the zod library
- Use BEM CSS for styling
- Don't use pinia to made requests to APIs
- Implement proper code splitting and lazy loading
- Use proper environment variables for configuration
- Use the custom render for unit tests
- For the pagination use the hook usePagination
- Every view unit test should mock requests with MSW
- Every request should have a MSW mock
- Every MSW mock in modukes should be shared with the main app
"""