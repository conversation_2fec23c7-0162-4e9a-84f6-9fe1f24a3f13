{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "previewTargetName": "preview", "testTargetName": "test", "serveTargetName": "serve", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/cypress/plugin", "options": {"targetName": "e2e", "componentTestingTargetName": "component-test"}}]}