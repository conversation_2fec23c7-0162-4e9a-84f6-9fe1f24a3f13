sonar.projectKey=frontend-backoffice
sonar.organization=alice-health
sonar.sourceEncoding=UTF-8
sonar.sources=src,modules
sonar.javascript.lcov.reportPaths=coverage/app-backoffice/lcov.info
sonar.exclusions=node_modules/*,coverage/lcov-report/*,dist/*,src/**/__tests__/**/*,**/*.spec.ts,**/*.spec.js,**/*.mock.ts,e2e/*,**/*/index.ts
sonar.coverage.exclusions=**/*.test.js,**/index.js,**/*.config.js,**/*.setup.js,**/*.stories.js,**/*.stories.ts,**/*/index.ts,**/main.ts,**/*.config.ts,**/*.spec.ts,**/*.test.ts,modules/**/*/models/*,src/app/router/**/*.ts
