# Router Configuration Rules

## Route Module Structure

### Basic Structure
```typescript
import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const moduleRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/base-path',
      name: 'base-name',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'feature-name',
          component: async () => await import('@feature-module/views/Component.vue')
        }
      ]
    }
  ]
}

export default moduleRoutes
```

### Rules

1. **Module Naming**
   - Route module functions should be named following the pattern: `{moduleName}Routes`
   - Example: `teamAssociationRoutes`, `userProfileRoutes`

2. **Path Structure**
   - Base paths should be kebab-case
   - Should start with a forward slash
   - Example: `/association`, `/user-profile`

3. **Route Names**
   - Must be unique across the application
   - Should follow the pattern: `feature-action`
   - Example: `association-list`, `user-profile-edit`

4. **Component Imports**
   - Must use dynamic imports with async/await
   - Should follow the module alias pattern: `@module-name/views/Component.vue`
   - Example: `async () => await import('@healthcare-team-association/views/ListView.vue')`

5. **Navigation Guards**
   - Must accept `beforeEnter` guard as a parameter
   - Type must be `NavigationGuardWithThis<undefined>`

6. **Type Safety**
   - Must import and use Vue Router types
   - Return type must be explicitly set to `RouteRecordRaw[]`

7. **Module Export**
   - Must export the route configuration as default export
   - Must be a function that returns `RouteRecordRaw[]`

### Best Practices

1. **Organization**
   - Keep route configurations in a dedicated `routes` folder within each module
   - Use `index.ts` for the main route configuration
   - Separate complex child routes into their own files

2. **Lazy Loading**
   - Always use dynamic imports for components to enable code splitting
   - Use meaningful chunk names for larger modules

3. **Naming Conventions**
   - Base route name should match the module name
   - Child route names should describe their function
   - Use consistent naming patterns across all route modules

4. **Security**
   - Always implement navigation guards for protected routes
   - Pass authentication/authorization checks through the `beforeEnter` parameter

5. **Maintenance**
   - Keep route configurations flat when possible
   - Document complex routing logic
   - Use TypeScript for better type safety and maintainability

### Example Implementation

```typescript
// Good Example
import type { RouteRecordRaw, NavigationGuardWithThis } from 'vue-router'

const moduleNameRoutes = (
  beforeEnter: NavigationGuardWithThis<undefined>
): RouteRecordRaw[] => {
  return [
    {
      path: '/module-name',
      name: 'module-name',
      beforeEnter,
      children: [
        {
          path: '',
          name: 'module-name-list',
          component: async () => await import('@module-name/views/ListView.vue')
        }
      ]
    }
  ]
}

export default moduleNameRoutes

// Bad Example - Don't do this
const routes = {
  path: '/module-name',
  name: 'module-name',
  component: require('@module-name/views/ListView.vue')
}
```

### Common Issues to Avoid

1. ❌ Using synchronous component imports
2. ❌ Missing type annotations
3. ❌ Inconsistent naming patterns
4. ❌ Hardcoded navigation guards
5. ❌ Missing module aliases
6. ❌ Deeply nested route configurations
7. ❌ Duplicate route names