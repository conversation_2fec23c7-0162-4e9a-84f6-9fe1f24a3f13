# GET Request Pattern

This document outlines the standard pattern for implementing GET requests in our codebase.

## Pattern Structure

```typescript
import type { ResponseType } from '@commons/index'
import http from '@http/index'

export const getFunctionName = async () => {
  const { data } = await http.get<ResponseType>('/endpoint/path')
  return data
}
```

## Key Components

1. **Imports**:
   - Import necessary types from `@commons/index` or from local module `'@module-name/models'` 
   - Import the HTTP client from `@http/index`

2. **Function Structure**:
   - Use async/await pattern
   - Destructure the `data` from the response
   - Use TypeScript generics for response type safety
   - Return the data directly

3. **Naming Convention**:
   - Function names should start with `get` prefix
   - Use camelCase naming

## Example Implementation

```typescript
import type { ValueItem } from '@commons/index'
import http from '@http/index'

export const getFeatureNamespaces = async () => {
  const { data } = await http.get<ValueItem[]>('/featureConfig/namespaces')
  return data
}
```

## Best Practices

1. Always type the response data using generics
2. Use consistent error handling
3. Keep the function focused on a single responsibility
4. Document any specific requirements or edge cases
5. Use meaningful and descriptive endpoint paths
