## List Request Pattern

Replace {entity} with your specific entity name (e.g., associations, users, products)

```typescript
import http from '@http/index'
import { useQueryString } from '@alice-health/vue-hooks'
import type { {Entity} } from '@your-module/models'

export type get{Entity}sResponse = {
  results: {Entity}[]
  pagination: {
    totalPages: number
  }
}

export const get{Entity}s = async (params?: Record<string, string>) => {
const { createQueryString } = useQueryString()

const qs = params ? `?${createQueryString(params)}` : ''

const { data } = await http.get<get{Entity}sResponse>(`/{entity}s${qs}`)
return data
}
```

Usage:

1. Replace `{Entity}` with your PascalCase entity name (e.g., TeamAssociation)
2. Replace `{entity}` with your kebab-case entity name (e.g., team-association)
3. Update the import path for your entity type
4. Adjust pagination fields if needed

Example:

```typescript
import type { User } from '@users/models'

export type getUsersResponse = {
  results: User[]
  pagination: {
    totalPages: number
  }
}

export const getUsers = async (params?: Record<string, string>) => {
  const { createQueryString } = useQueryString()
  const qs = params ? `?${createQueryString(params)}` : ''
  const { data } = await http.get<getUsersResponse>(`/users${qs}`)
  return data
}
```
