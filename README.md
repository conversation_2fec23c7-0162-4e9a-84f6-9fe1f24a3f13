# Backoffice
Unified Alice Backoffice

### Documentação oficial
[<PERSON><PERSON><PERSON><PERSON> de projeto Alice Backoffice](https://www.notion.so/alicehealth/Alice-Back-office-Web-50a34645cd644f56ba99f13d6126a7da)

### Environments

| Name           | Link
| -------------- | --------------------------------------------------------------------
| dev 1          | [backoffice-dev1.dev.alice.com.br](https://backoffice-dev1.dev.alice.com.br)
| dev 2          | [backoffice-dev2.dev.alice.com.br](https://backoffice-dev2.dev.alice.com.br)
| staging        | [backoffice.staging.alice.com.br](https://backoffice.staging.alice.com.br)
| production     | [backoffice.alice.com.br](https://backoffice.alice.com.br)
| BFF Swagger    | [backoffice-bff-api.wonderland.engineering/swagger](https://backoffice-bff-api.wonderland.engineering/swagger)

---

### How to run
To run this project, you need to run the commands below:

```bash
yarn
yarn serve:dev2 # This will use .env.dev2 keys
```

---

### Useful commands

| Command               | Description                                       |
| --------------------- | ------------------------------------------------- |
| yarn lint             | Lints and fixes files                             |
| yarn test             | Run unit tests                                    |
| yarn build            | Run application build                             |


---

## How to deploy

The flux of deployment should be like this, dev, staging, and finally production. The new code just goes to staging after the code is ready for production, all manual tests should be made in development environments.

All deploys are tracked in Datadog. So, for a while that we don't have the automation for this, bump a version in the file [package.json](package.json) for each new Pull Request and you will be able to filter errors, performance metrics, and other things by version on Datadog.

### Jenkins by environment

| env        | Description                      | link                                                                                                                                                    |
| ---------- | -------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- |
| dev1       | Env to test features             | [jenkins dev1](https://jenkinsci.devtools.alice.tools/blue/organizations/jenkins/frontend%2Fspa%2Ffront-app-backoffice%2Fdev%2Fdeploy-dev1/activity/)   |
| dev2       | Env to test features             | [jenkins dev2](https://jenkinsci.devtools.alice.tools/blue/organizations/jenkins/frontend%2Fspa%2Ffront-app-backoffice%2Fdev%2Fdeploy-dev2/activity/)   |
| staging    | Just for production-ready code   | [jenkins staging](https://jenkinsci.devtools.alice.tools/blue/organizations/jenkins/frontend%2Fspa%2Ffront-app-backoffice%2Fstaging%2Fdeploy/activity/) |
| production | production code                  | [jenkins prod](https://jenkinsci.devtools.alice.tools/blue/organizations/jenkins/frontend%2Fspa%2Ffront-app-backoffice%2Fprod%2Fdeploy/activity/)       |

### Deployment
| env        | Description
| ---------- | --------------------------------
| dev1       | Merge your branch into development/dev2 and after push the deploy run automatically, this branch reset every day.
| dev2       | Merge your branch into development/dev1 and after push the deploy run automatically, this branch reset every day.
| staging    | After the merge of the Pull request the deploy run automatically
| production | After the merge of the Pull request the deploy run automatically


To do a manual deployment on Jenkins, you should get a hash commit and run this hash on dev1, dev2, or the staging environment.

### PR validator

All pull requests of this project will run some validations on the pipeline to check if it's all right.
The commands are:

| Command                 | Description                                |
| ----------------------- | ------------------------------------------ |
| yarn lint               | Lints and fixes files                      |
| yarn test:unit:coverage | Run tests and generate the coverage report |

---

### Observability

After the deployment, you cans see the sessions, errors, performance metrics, etc in this application on [Datadog](https://app.datadoghq.com/rum/performance-monitoring?query=%40application.id%3Af36ad5b0-1ee8-4e7c-a942-1bb7bd49566e).

---

### Creating a new module
To scaffold a new module, you can use the command below:

```sh
# this generator is BETA yet, so use it carefully, and probably you will need to do some manual changes.
yarn generate scaffold NameOfNewModule --template module
```

Follow the instructions in the [documentation](./docs/module.md) to create a new module.
