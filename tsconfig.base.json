{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom", "WebWorker"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@alice-backoffice/acquisition-accredited-network": ["modules/acquisition/accredited-network/src/index.ts"], "@alice-backoffice/acquisition-products": ["modules/acquisition/products/src/index.ts"], "@alice-backoffice/auth": ["modules/core/auth/src/index.ts"], "@alice-backoffice/company-staff": ["modules/company/staff/src/index.ts"], "@alice-backoffice/feature-flags": ["modules/feature-flags/src/index.ts"], "@alice-backoffice/healthcare-team-address": ["modules/healthcare-team/address/src/index.ts"], "@alice-backoffice/healthcare-team-registration": ["modules/healthcare-team/registration/src/index.ts"], "@alice-backoffice/procedures": ["modules/procedures/src/index.ts"], "@alice-backoffice/code-pricing": ["modules/provider-lifecycle/code-pricing/src/index.ts"], "@alice-backoffice/specialist-earnings": ["modules/provider-lifecycle/specialist-earnings/src/index.ts"], "@alice-backoffice/sales-agent": ["modules/business/sales-agent/src/index.ts"], "@alice-backoffice/sales-firm": ["modules/business/sales-firm/src/index.ts"], "@alice-backoffice/sales-firm-staff": ["modules/business/sales-firm-staff/src/index.ts"], "@alice-backoffice/sherlock": ["modules/sherlock/src/index.ts"], "@alice-backoffice/staff": ["modules/staff/src/index.ts"], "@alice-backoffice/acquisition-vic-product-option": ["modules/acquisition/vic-product-option/src/index.ts"], "@alice-backoffice/specialties": ["modules/specialties/src/index.ts"], "@alice-backoffice/health-professionals": ["modules/health-professionals/src/index.ts"], "@alice-backoffice/healthcare-team-association": ["modules/healthcare-team/association/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}