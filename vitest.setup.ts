/* eslint-disable no-unused-vars */
import '@testing-library/jest-dom/vitest'

import { beforeAll, beforeEach, vi } from 'vitest'
import { defineCustomElements } from '@alice-health/wonderland/loader'

process.env.NODE_ENV = 'test'

/*
 * Mocks
 */

HTMLDialogElement.prototype.show = vi.fn()
HTMLDialogElement.prototype.showModal = vi.fn()
HTMLDialogElement.prototype.close = vi.fn()

beforeAll(async () => {
  defineCustomElements()
})

beforeEach(() => {
  vi.mock('firebase/app')
  vi.mock('firebase/auth')
  vi.mock('firebase/firestore')
  vi.mock('firebase/firestore/lite')
  vi.mock('@alice-health/observability')

  vi.mock('firebase/app', () => {
    return {
      initializeApp: vi.fn(() => {})
    }
  })

  vi.mock('firebase/auth', () => {
    const getRedirectResult = vi.fn(() => {
      return Promise.resolve({
        user: {
          displayName: 'redirectResultTestDisplayName',
          email: '<EMAIL>',
          emailVerified: true
        }
      })
    })
    return {
      getAuth: vi.fn().mockReturnValue({
        signOut: vi.fn(() => {})
      }),
      onAuthStateChanged: vi.fn(() => {}),
      isSignInWithEmailLink: vi.fn(() => false),
      getRedirectResult
    }
  })

  vi.mock('firebase/firestore', () => {
    return {
      getFirestore: vi.fn(),
      serverTimestamp: vi.fn().mockReturnValue(new Date()),
      arrayRemove: vi.fn(),
      arrayUnion: vi.fn()
    }
  })
})
