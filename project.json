{"name": "front-app-backoffice", "$schema": "node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "./src", "// targets": "to see all targets run: nx show project front-app-backoffice --web", "targets": {"serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "front-app-backoffice:build"}, "configurations": {"production": {"buildTarget": "front-app-backoffice:build:production", "mode": "prod"}, "staging": {"buildTarget": "front-app-backoffice:build:staging", "mode": "staging"}, "dev1": {"buildTarget": "front-app-backoffice:build:dev1", "mode": "dev1"}, "dev2": {"buildTarget": "front-app-backoffice:build:dev2", "mode": "dev2"}, "development": {"buildTarget": "front-app-backoffice:build:development", "mode": "development"}}}}}